import {post, get} from '../utlis/require.js'
import store from '../vuex/index.js'

// 获取门店列表
export const getStoreList = data => get('/api/v1/store/list', data)

// 获取门店详情
export const getStoreDetail = data => get('/api/v1/store/detail', data)

// 获取门店统计
export const getStoreCount = () => get('/api/v1/store/count')

// 更新门店信息
export const updateStoreInfo = data => post('/api/v1/order/updateStoreInfo', data)
