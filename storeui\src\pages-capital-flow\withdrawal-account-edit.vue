<template>
  <view class="animated fadeIn account-edit-page">
    <appHead left fixed :title="isEdit ? '编辑提现账户' : '添加提现账户'"></appHead>
    <view class="container">
      <view class="form">
        <!-- 账户类型选择 -->
        <view class="form-item">
          <text class="label">账户类型</text>
          <view class="type-options">
            <view
              class="type-option"
              :class="{ selected: accountForm.accountType === item.value }"
              v-for="item in accountTypes"
              :key="item.value"
              @click="selectAccountType(item.value)"
            >
              {{ item.label }}
            </view>
          </view>
        </view>
        
        <!-- 账户名称 -->
        <view class="form-item">
          <text class="label">账户名称</text>
          <u-input v-model="accountForm.accountName" placeholder="请输入账户名称（便于区分多个账户）"></u-input>
        </view>
        
        <!-- 银行信息 -->
        <view class="form-item">
          <text class="label">银行信息</text>
          <u-input v-model="accountForm.bankName" placeholder="银行名称"></u-input>
          <u-input v-model="accountForm.bankBranch" placeholder="开户行"></u-input>
          <u-input v-model="accountForm.bankAccount" placeholder="银行账号"></u-input>
          <u-input v-model="accountForm.accountHolder" placeholder="开户人姓名"></u-input>
        </view>
        
        <!-- 设为默认 -->
        <view class="form-item">
          <view class="default-switch">
            <text>设为默认提现账户</text>
            <switch :checked="accountForm.isDefault" @change="toggleDefault" color="#ff6a00"></switch>
          </view>
        </view>
        
        <!-- 按钮 -->
        <view class="buttons">
          <u-button type="primary" @click="saveAccount">保存</u-button>
          <u-button @click="cancel">取消</u-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import appHead from '@/common/appHead.vue';

import {
  getWithdrawalAccountDetail,
  createWithdrawalAccount,
  updateWithdrawalAccount
} from '@/api/withdrawal-account.js';

export default {
  components: {
    appHead
  },
  data() {
    return {
      isEdit: false,
      accountId: null,
      accountForm: {
        accountType: 1, // 1=企业对公，2=个人
        accountName: '',
        bankName: '',
        bankBranch: '',
        bankAccount: '',
        accountHolder: '',
        isDefault: false
      },
      accountTypes: [
        { value: 1, label: '企业对公账户' },
        { value: 2, label: '个人账户' }
      ],
      loading: false
    }
  },
  onLoad(options) {
    if (options && options.id) {
      this.isEdit = true;
      this.accountId = options.id;
      this.getAccountDetail();
    }
  },
  methods: {
    // 获取账户详情
    async getAccountDetail() {
      if (!this.accountId) return;

      this.loading = true;

      try {
        // require.js已经处理了业务逻辑，直接使用返回的数据
        const accountDetail = await getWithdrawalAccountDetail(this.accountId);
        this.accountForm = accountDetail || this.accountForm;
        console.log('账户详情获取成功:', this.accountForm);
      } catch (error) {
        console.error('获取账户详情失败:', error);
        uni.showToast({
          title: '获取账户详情失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    // 选择账户类型
    selectAccountType(value) {
      this.accountForm.accountType = value;
      // 企业对公账户不再需要开票图片
      // if (value === 2) {
      //   this.accountForm.invoiceImage = '';
      // }
    },
    
    // 切换默认状态
    toggleDefault(e) {
      this.accountForm.isDefault = e.detail.value;
    },

    // 移除了开票图片相关的方法：chooseImage, previewImage, deleteImage
    // 因为 WithdrawalAccount 不再需要记录发票图片URL
    
    // 保存账户
    async saveAccount() {
      // 表单验证
      if (!this.accountForm.accountName) {
        uni.showToast({
          title: '请输入账户名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.accountForm.bankName) {
        uni.showToast({
          title: '请输入银行名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.accountForm.bankAccount) {
        uni.showToast({
          title: '请输入银行账号',
          icon: 'none'
        });
        return;
      }
      
      if (!this.accountForm.accountHolder) {
        uni.showToast({
          title: '请输入开户人姓名',
          icon: 'none'
        });
        return;
      }
      
      // 企业对公账户不再需要验证开票图片
      // 注释掉原有的开票图片验证逻辑，与withdrawal.vue保持一致
      // if (this.accountForm.accountType === 1 && !this.accountForm.invoiceImage) {
      //   uni.showToast({
      //     title: '请上传开票图片',
      //     icon: 'none'
      //   });
      //   return;
      // }
      
      // 显示加载中
      uni.showLoading({
        title: '保存中...'
      });
      
      try {
        // require.js已经处理了业务逻辑，成功时直接返回数据
        if (this.isEdit) {
          await updateWithdrawalAccount(this.accountId, this.accountForm);
        } else {
          await createWithdrawalAccount(this.accountForm);
        }

        uni.showToast({
          title: this.isEdit ? '更新成功' : '添加成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      } catch (error) {
        console.error('保存账户失败:', error);
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 取消
    cancel() {
      uni.navigateBack();
    }
  }
}
</script>

<style lang="scss" scoped>
.account-edit-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.container {
  padding: 20rpx;
  margin-top: 20rpx;
}

.form {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 40rpx;
}

.label {
  font-size: 30rpx;
  margin-bottom: 20rpx;
  display: block;
  font-weight: 500;
  color: #333;
}

.type-options {
  display: flex;
  justify-content: space-between;
}

.type-option {
  padding: 24rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  width: 48%;
  text-align: center;
  font-size: 28rpx;
  transition: all 0.3s;
}

.type-option.selected {
  border-color: $xyj-theme;
  background: rgba($xyj-theme, 0.1);
  color: $xyj-theme;
}

u-input {
  margin-bottom: 24rpx;
  border: 1px solid #eee;
  padding: 24rpx;
  border-radius: 8rpx;
  background: #f9f9f9;
}

.default-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  
  text {
    font-size: 28rpx;
    color: #333;
  }
}

.buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 60rpx;
}

u-button {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
}

/* 图片上传样式 */
.upload-container {
  margin-top: 20rpx;
}

.upload-box {
  width: 200rpx;
  height: 200rpx;
  border: 1px dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.image-preview {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-preview image {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: rgba(0, 0, 0, 0.5);
}

.action-btn {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 0;
}

.action-btn.delete {
  background-color: rgba(255, 0, 0, 0.7);
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
  display: block;
}
</style>