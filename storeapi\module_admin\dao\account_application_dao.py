from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func, text
from sqlalchemy.orm import selectinload
from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime, timedelta

from module_admin.entity.do.account_application import AccountApplication
from exceptions.exception import QueryException
from utils.log_util import logger


class AccountApplicationDao:
    """开户申请数据访问层"""

    @staticmethod
    async def create_application(db: AsyncSession, application_data: Dict[str, Any], mobile: str) -> str:
        """
        创建开户申请
        
        :param db: 数据库会话
        :param application_data: 申请数据
        :param mobile: 手机号
        :return: 申请UUID
        """
        try:
            # 生成UUID
            application_uuid = str(uuid.uuid4())
            
            # 创建申请记录
            application = AccountApplication(
                uuid=application_uuid,
                mobile=mobile,
                **application_data
            )
            
            db.add(application)
            await db.commit()
            await db.refresh(application)
            
            logger.info(f"创建开户申请成功，UUID: {application_uuid}")
            return application_uuid
            
        except Exception as e:
            await db.rollback()
            logger.error(f"创建开户申请失败: {str(e)}")
            raise QueryException(message=f"创建开户申请失败: {str(e)}")

    @staticmethod
    async def get_application_by_mobile(db: AsyncSession, mobile: str) -> Optional[AccountApplication]:
        """
        根据手机号获取最新的开户申请
        
        :param db: 数据库会话
        :param mobile: 手机号
        :return: 开户申请记录
        """
        try:
            query = select(AccountApplication).where(
                AccountApplication.mobile == mobile
            ).order_by(desc(AccountApplication.apply_time)).limit(1)
            
            result = await db.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"根据手机号获取开户申请失败: {str(e)}")
            raise QueryException(message=f"根据手机号获取开户申请失败: {str(e)}")

    @staticmethod
    async def get_application_by_uuid(db: AsyncSession, application_uuid: str) -> Optional[AccountApplication]:
        """
        根据UUID获取开户申请
        
        :param db: 数据库会话
        :param application_uuid: 申请UUID
        :return: 开户申请记录
        """
        try:
            query = select(AccountApplication).where(
                AccountApplication.uuid == application_uuid
            )
            
            result = await db.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"根据UUID获取开户申请失败: {str(e)}")
            raise QueryException(message=f"根据UUID获取开户申请失败: {str(e)}")

    @staticmethod
    async def get_application_list(
        db: AsyncSession, 
        page: int = 1, 
        page_size: int = 20,
        status: Optional[str] = None,
        mobile: Optional[str] = None,
        name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取开户申请列表
        
        :param db: 数据库会话
        :param page: 页码
        :param page_size: 每页数量
        :param status: 申请状态
        :param mobile: 手机号
        :param name: 姓名
        :return: 申请列表和总数
        """
        try:
            # 构建查询条件
            conditions = []
            
            if status:
                conditions.append(AccountApplication.status == status)
            if mobile:
                conditions.append(AccountApplication.mobile.like(f"%{mobile}%"))
            if name:
                conditions.append(AccountApplication.name.like(f"%{name}%"))
            
            # 查询总数
            count_query = select(func.count(AccountApplication.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            count_result = await db.execute(count_query)
            total = count_result.scalar()
            
            # 查询列表
            query = select(AccountApplication)
            if conditions:
                query = query.where(and_(*conditions))
            
            query = query.order_by(desc(AccountApplication.apply_time)).offset(
                (page - 1) * page_size
            ).limit(page_size)
            
            result = await db.execute(query)
            applications = result.scalars().all()
            
            return {
                "list": applications,
                "total": total,
                "page": page,
                "page_size": page_size
            }
            
        except Exception as e:
            logger.error(f"获取开户申请列表失败: {str(e)}")
            raise QueryException(message=f"获取开户申请列表失败: {str(e)}")

    @staticmethod
    async def update_application_status(
        db: AsyncSession, 
        application_uuid: str, 
        status: str, 
        reviewer_id: str,
        remark: Optional[str] = None
    ) -> bool:
        """
        更新申请状态
        
        :param db: 数据库会话
        :param application_uuid: 申请UUID
        :param status: 新状态
        :param reviewer_id: 审核人ID
        :param remark: 审核备注
        :return: 是否成功
        """
        try:
            # 查找申请记录
            application = await AccountApplicationDao.get_application_by_uuid(db, application_uuid)
            if not application:
                raise QueryException(message="申请记录不存在")
            
            # 更新状态
            application.status = status
            application.reviewer_id = reviewer_id
            application.review_time = datetime.now()
            if remark:
                application.remark = remark
            
            await db.commit()
            
            logger.info(f"更新申请状态成功，UUID: {application_uuid}, 状态: {status}")
            return True
            
        except Exception as e:
            await db.rollback()
            logger.error(f"更新申请状态失败: {str(e)}")
            raise QueryException(message=f"更新申请状态失败: {str(e)}")

    @staticmethod
    async def check_duplicate_application(db: AsyncSession, mobile: str, id_number: Optional[str] = None) -> bool:
        """
        检查是否有重复的待审核申请

        :param db: 数据库会话
        :param mobile: 手机号
        :param id_number: 身份证号（可选）
        :return: 是否有重复申请
        """
        try:
            conditions = [
                AccountApplication.mobile == mobile,
                AccountApplication.status.in_(['0', '1'])  # 待审核或审核中
            ]

            # 如果有身份证号，则加入身份证号条件
            if id_number:
                conditions.append(AccountApplication.id_number == id_number)

            query = select(AccountApplication).where(and_(*conditions))

            result = await db.execute(query)
            existing_application = result.scalar_one_or_none()

            return existing_application is not None

        except Exception as e:
            logger.error(f"检查重复申请失败: {str(e)}")
            raise QueryException(message=f"检查重复申请失败: {str(e)}")

    @staticmethod
    async def get_application_by_trace_no(db: AsyncSession, trace_no: str) -> Optional[AccountApplication]:
        """
        根据易宝流水号获取开户申请

        :param db: 数据库会话
        :param trace_no: 易宝流水号
        :return: 开户申请记录
        """
        try:
            # 首先尝试通过存储的客户流水号精确匹配
            query = select(AccountApplication).where(
                AccountApplication.yeepay_cus_trace_no == trace_no
            )

            result = await db.execute(query)
            application = result.scalar_one_or_none()

            if application:
                logger.info(f"通过客户流水号找到申请记录: {application.uuid}")
                return application

            # 如果没有找到，尝试通过系统跟踪号匹配（如果传入的是系统跟踪号）
            query = select(AccountApplication).where(
                AccountApplication.yeepay_sys_trace_no == trace_no
            )

            result = await db.execute(query)
            application = result.scalar_one_or_none()

            if application:
                logger.info(f"通过系统跟踪号找到申请记录: {application.uuid}")
                return application

            # 如果仍然没有找到，尝试通过时间范围查询（兜底方案）
            if trace_no.startswith("INNET") and len(trace_no) >= 19:
                # 提取时间部分 INNET**************...
                time_str = trace_no[5:19]  # **************
                try:
                    apply_time = datetime.strptime(time_str, "%Y%m%d%H%M%S")
                    # 查询该时间前后5分钟内的申请
                    start_time = apply_time - timedelta(minutes=5)
                    end_time = apply_time + timedelta(minutes=5)

                    query = select(AccountApplication).where(
                        and_(
                            AccountApplication.apply_time >= start_time,
                            AccountApplication.apply_time <= end_time
                        )
                    ).order_by(desc(AccountApplication.apply_time)).limit(1)

                    result = await db.execute(query)
                    application = result.scalar_one_or_none()

                    if application:
                        logger.info(f"通过时间范围找到申请记录: {application.uuid}")
                        return application

                except ValueError:
                    logger.warning(f"无法解析流水号中的时间: {trace_no}")

            logger.warning(f"未找到对应的申请记录: {trace_no}")
            return None

        except Exception as e:
            logger.error(f"根据流水号获取开户申请失败: {str(e)}")
            raise QueryException(message=f"根据流水号获取开户申请失败: {str(e)}")

    @staticmethod
    async def update_application_status_by_notify(
        db: AsyncSession,
        application_uuid: str,
        update_data: Dict[str, Any]
    ) -> bool:
        """
        通过通知更新申请状态（不需要审核人）

        :param db: 数据库会话
        :param application_uuid: 申请UUID
        :param update_data: 更新数据
        :return: 是否成功
        """
        try:
            # 查找申请记录
            application = await AccountApplicationDao.get_application_by_uuid(db, application_uuid)
            if not application:
                raise QueryException(message="申请记录不存在")

            # 更新字段
            for key, value in update_data.items():
                if hasattr(application, key) and value is not None:
                    setattr(application, key, value)

            # 如果更新状态，同时更新审核时间
            if "status" in update_data:
                application.review_time = datetime.now()

            await db.commit()

            logger.info(f"通过通知更新申请状态成功，UUID: {application_uuid}")
            return True

        except Exception as e:
            await db.rollback()
            logger.error(f"通过通知更新申请状态失败: {str(e)}")
            raise QueryException(message=f"通过通知更新申请状态失败: {str(e)}")

    @staticmethod
    async def get_latest_application_by_mobile(db: AsyncSession, mobile: str) -> Optional[AccountApplication]:
        """
        根据手机号获取最新的开户申请

        :param db: 数据库会话
        :param mobile: 手机号
        :return: 最新的开户申请记录
        """
        try:
            query = select(AccountApplication).where(
                AccountApplication.mobile == mobile
            ).order_by(desc(AccountApplication.apply_time)).limit(1)

            result = await db.execute(query)
            application = result.scalar_one_or_none()

            if application:
                logger.info(f"找到手机号 {mobile} 的最新申请记录: {application.uuid}")
            else:
                logger.info(f"手机号 {mobile} 没有申请记录")

            return application

        except Exception as e:
            logger.error(f"根据手机号获取最新申请失败: {str(e)}")
            raise QueryException(message=f"根据手机号获取最新申请失败: {str(e)}")
