<template>
  <view class="order-share-page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 自定义头部 -->
    <view class="custom-header">
      <view class="header-background"></view>
      <view class="header-content">
        <view class="nav-bar">
          <view class="nav-left" @click="goBack">
            <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
          </view>
          <view class="nav-center">
            <text class="nav-title">订单详情</text>
          </view>
          <view class="nav-right"></view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="spinner" size="40" color="#fdd118"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 订单信息展示 -->
    <view v-else-if="orderInfo" class="content-container">
      <!-- 订单卡片 -->
      <view class="order-card">
        <view class="card-header">
          <view class="service-badge">
            <text class="badge-text">急招</text>
          </view>
          <text class="product-name">{{ (orderInfo && orderInfo.product_name) || '家政服务' }}</text>
          <view class="order-status" :class="statusClass">
            {{ (orderInfo && orderInfo.order_status_name) || '待确认' }}
          </view>
        </view>

        <view class="card-content">
          <!-- 服务时间 -->
          <view class="info-item">
            <view class="info-icon">
              <u-icon name="clock" size="18" color="#fdd118"></u-icon>
            </view>
            <view class="info-content">
              <text class="info-label">服务时间</text>
              <text class="info-value">{{ formatServiceTime(orderInfo && orderInfo.service_date, orderInfo && orderInfo.service_hour) }}</text>
            </view>
          </view>

          <!-- 服务地址 -->
          <view class="info-item">
            <view class="info-icon">
              <u-icon name="map" size="18" color="#fdd118"></u-icon>
            </view>
            <view class="info-content">
              <text class="info-label">服务地址</text>
              <text class="info-value">{{ (orderInfo && (orderInfo.service_address_masked || orderInfo.service_address)) || '地址待确认' }}</text>
            </view>
          </view>

          <!-- 客户信息 -->
          <view class="info-item" v-if="customerInfo">
            <view class="info-icon">
              <u-icon name="account" size="18" color="#fdd118"></u-icon>
            </view>
            <view class="info-content">
              <text class="info-label">客户信息</text>
              <text class="info-value">{{ customerInfo.name || '客户' }}{{ customerInfo.mobile ? '（' + customerInfo.mobile + '）' : '' }}</text>
            </view>
          </view>

          <!-- 薪酬信息 -->
          <view class="salary-info">
            <view class="salary-label">服务薪酬</view>
            <view class="salary-amount">¥{{ commissionAmount || (orderInfo && orderInfo.preset_commission) || '0' }}</view>
          </view>

          <!-- 佣金状态提示 -->
          <view v-if="orderInfo && (!orderInfo.preset_commission || orderInfo.preset_commission <= 0)" class="commission-notice">
            <u-icon name="warning" size="16" color="#ff6b35"></u-icon>
            <text class="notice-text">该订单未设置提成金额，暂时无法接单</text>
          </view>

          <!-- 订单状态提示 -->
          <view v-if="orderInfo && orderInfo.can_accept === false" class="status-notice">
            <u-icon name="info-circle" size="16" color="#ff6b35"></u-icon>
            <text class="notice-text">该订单已被接单，无法抢单</text>
          </view>

          <!-- 服务要求 -->
          <view v-if="orderInfo && orderInfo.service_remark" class="service-remark">
            <text class="remark-label">服务要求：</text>
            <text class="remark-content">{{ orderInfo.service_remark }}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-container">
        <view v-if="!isAuthorized" class="auth-section">
          <view class="auth-tips">
            <text class="tips-text">点击下方按钮授权登录后即可接单</text>
          </view>
          <button class="auth-btn" open-type="getPhoneNumber" @getphonenumber="handlePhoneAuth" :loading="authLoading">
            <u-icon name="phone" size="20" color="#fff" style="margin-right: 10rpx;"></u-icon>
            手机号快速验证
          </button>
        </view>

        <view v-else class="order-actions">
          <view v-if="userStatus === 'existing'" class="existing-user-actions">
            <button class="accept-btn" @click="handleAcceptOrder" :loading="acceptLoading">
              立即接单
            </button>
          </view>

          <view v-else-if="userStatus === 'new'" class="new-user-actions">
            <view class="register-tips">
              <text class="tips-text">您还不是我们的服务人员，请先注册</text>
            </view>
            <button class="register-btn" @click="handleQuickRegister">
              注册成为服务人员
            </button>
          </view>

          <view v-else-if="userStatus === 'copying'" class="copying-user-actions">
            <view class="copy-tips">
              <text class="tips-text">检测到您在其他门店已注册，需要同步信息到当前门店</text>
            </view>
            <button class="copy-btn" @click="handleCopyStaffToStore" :loading="copyLoading">
              同步信息并接单
            </button>
          </view>

          <view v-else-if="userStatus === 'copying-progress'" class="copying-status">
            <u-loading-icon mode="spinner" size="30" color="#fdd118"></u-loading-icon>
            <text class="copying-text">正在同步员工信息...</text>
          </view>

          <view v-else-if="userStatus === 'checking'" class="checking-status">
            <u-loading-icon mode="spinner" size="30" color="#fdd118"></u-loading-icon>
            <text class="checking-text">正在验证身份...</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-container">
      <view class="error-icon">
        <u-icon name="close-circle" size="60" color="#ff4757"></u-icon>
      </view>
      <text class="error-text">{{ errorMessage || '订单信息加载失败' }}</text>
      <button class="retry-btn" @click="loadOrderInfo">重新加载</button>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { getOrderShareDetail, checkStaffExists, acceptOrderByShare, copyStaffToStore } from '@/api/order.js'
import { post } from '@/utlis/require.js'

export default {
  name: 'OrderShare',
  data() {
    return {
      orderNumber: '',
      orderInfo: null,
      customerInfo: null,
      loading: true,
      isAuthorized: false,
      userStatus: '', // 'checking', 'existing', 'new', 'copying', 'copying-progress'
      authLoading: false,
      acceptLoading: false,
      copyLoading: false,
      errorMessage: '',
      userMobile: '',
      userOpenid: '',
      commissionAmount: 0, // 佣金金额
      storeUuid: '', // 门店UUID（从分享链接获取）
      storeName: '', // 门店名称（从分享链接获取）
      inviterUserId: '', // 分享员工ID（用作邀请码）
      isFromRegistration: false // 是否来自注册页面
    }
  },

  computed: {
    ...mapState(['StatusBar']),

    // 订单状态样式类
    statusClass() {
      if (!this.orderInfo || !this.orderInfo.order_status) {
        return 'status-unknown'
      }

      const status = this.orderInfo.order_status
      const statusMap = {
        10: 'status-pending',    // 已接单
        20: 'status-pending',    // 派单待确认
        30: 'status-rejected',   // 拒绝接单
        40: 'status-assigned',   // 已派单
        50: 'status-progress',   // 执行中
        60: 'status-progress',   // 开始服务
        70: 'status-progress',   // 服务结束
        80: 'status-completed',  // 已完成
        99: 'status-cancelled'   // 已取消
      }
      return statusMap[status] || 'status-unknown'
    }
  },

  onLoad(options) {
    console.log('订单分享页面加载，参数:', options)
    this.orderNumber = options.orderNumber

    // 获取佣金参数
    if (options.commission) {
      this.commissionAmount = parseFloat(options.commission) || 0
      console.log('获取到佣金参数:', this.commissionAmount)
    }

    // 获取门店信息参数（用于快速入驻）
    if (options.storeUuid) {
      this.storeUuid = options.storeUuid
      console.log('获取到门店UUID:', this.storeUuid)
    }
    if (options.storeName) {
      this.storeName = decodeURIComponent(options.storeName)
      console.log('获取到门店名称:', this.storeName)
    }

    // 获取分享员工ID（用作邀请码）
    if (options.inviterUserId) {
      this.inviterUserId = options.inviterUserId
      console.log('获取到分享员工ID:', this.inviterUserId)
    }

    if (!this.orderNumber) {
      this.errorMessage = '订单号不能为空'
      this.loading = false
      return
    }

    // 处理从注册页面返回的情况
    if (options.autoAccept === 'true' && options.mobile && options.openid) {
      this.userMobile = options.mobile
      this.userOpenid = options.openid
      this.isAuthorized = true
      this.isFromRegistration = true // 标记来自注册页面

      console.log('从员工入驻页面返回，重新验证员工状态')
      // 重新验证员工状态，确保数据一致性
      // 不直接设置为existing，而是通过checkUserIdentity重新检查
    }

    this.loadOrderInfo()

    // 如果已经授权，则检查用户身份（包括从注册页面返回的情况）
    if (this.isAuthorized) {
      this.checkUserIdentity()
    }
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack({
        fail: () => {
          // 如果没有上一页，跳转到首页
          uni.switchTab({
            url: '/pages/home/<USER>'
          })
        }
      })
    },

    // 加载订单信息
    async loadOrderInfo() {
      this.loading = true
      this.errorMessage = ''

      try {
        // 调用API获取订单详情（无需token验证）
        const response = await this.getOrderShareDetail(this.orderNumber)

        if (response && response.order_info) {
          this.orderInfo = response.order_info
          this.customerInfo = response.customer_info

          // 如果URL参数中没有佣金信息，则使用API返回的预设佣金信息
          if (!this.commissionAmount && response.order_info.preset_commission && response.order_info.preset_commission > 0) {
            this.commissionAmount = response.order_info.preset_commission
            console.log('从API获取到预设佣金:', this.commissionAmount)
          } else if (this.commissionAmount) {
            console.log('使用URL参数中的佣金:', this.commissionAmount)
          }

          // 注释掉自动接单逻辑，避免与员工注册返回时的自动接单冲突
          // 自动接单只在从员工入驻页面返回时触发（onLoad中处理）
        } else {
          this.errorMessage = '订单信息不存在'
        }
      } catch (error) {
        console.error('加载订单信息失败:', error)
        this.errorMessage = error.message || '网络错误，请重试'
      } finally {
        this.loading = false
      }
    },

    // 获取订单分享详情
    async getOrderShareDetail(orderNumber) {
      try {
        const response = await getOrderShareDetail(orderNumber)
        return response
      } catch (error) {
        console.error('获取订单分享详情失败:', error)
        throw new Error(error.message || '获取订单信息失败')
      }
    },

    // 格式化服务时间
    formatServiceTime(serviceDate, serviceHour) {
      if (!serviceDate) return '时间待确认'
      
      try {
        const date = new Date(serviceDate)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const time = serviceHour || '00:00'
        
        return `${year}-${month}-${day} ${time}`
      } catch (error) {
        return serviceDate + (serviceHour ? ' ' + serviceHour : '')
      }
    },

    // 手机号快速验证
    async handlePhoneAuth(e) {
      console.log('手机号快速验证:', e)

      // 检查用户是否授权
      if (e.detail.errMsg !== 'getPhoneNumber:ok') {
        console.log('用户拒绝手机号授权')
        uni.showToast({
          title: '需要手机号授权才能接单',
          icon: 'none'
        })
        return
      }

      this.authLoading = true

      try {
        // 先获取微信登录code
        const loginRes = await this.wxLogin()

        if (!loginRes.code) {
          throw new Error('微信登录失败')
        }

        // 解密手机号
        const phoneResult = await this.decryptPhoneNumber({
          wx_code: loginRes.code,
          encrypted_data: e.detail.encryptedData,
          iv: e.detail.iv
        })

        if (phoneResult && phoneResult.phone_number) {
          this.userMobile = phoneResult.phone_number
          this.userOpenid = phoneResult.openid || loginRes.openid
          this.isAuthorized = true

          console.log('手机号获取成功:', this.userMobile)

          // 检查用户身份
          await this.checkUserStatus()
        } else {
          throw new Error('获取手机号失败')
        }
      } catch (error) {
        console.error('手机号验证失败:', error)
        uni.showToast({
          title: error.message || '验证失败，请重试',
          icon: 'none'
        })
      } finally {
        this.authLoading = false
      }
    },

    // 微信登录
    wxLogin() {
      return new Promise((resolve, reject) => {
        uni.login({
          provider: 'weixin',
          success: resolve,
          fail: reject
        })
      })
    },

    // 解密微信手机号
    async decryptPhoneNumber(data) {
      try {
        // 使用与商家端相同的API调用方式
        const response = await post('/api/v1/common/decryptWechatPhone', data, {
          contentType: 'application/json'
        })
        return response
      } catch (error) {
        console.error('解密手机号API调用失败:', error)
        // 如果error有msg字段，使用API返回的具体错误信息
        if (error && error.msg) {
          throw new Error(error.msg)
        }
        // 如果error有message字段，也使用它
        if (error && error.message) {
          throw new Error(error.message)
        }
        throw error
      }
    },

    // 检查用户身份
    async checkUserStatus() {
      // 如果是从注册页面返回的，不重新检查用户状态
      if (this.isFromRegistration) {
        console.log('来自注册页面，跳过用户状态检查')
        return
      }

      this.userStatus = 'checking'

      try {
        // 确保订单号存在
        if (!this.orderNumber) {
          throw new Error('订单号不能为空')
        }

        // 调用API检查员工是否存在，传递订单号以获取门店信息
        const result = await this.checkStaffExists(this.userMobile, this.orderNumber)

        // 根据检查结果进行不同处理
        if (result.exists_in_order_store) {
          // 情况1：员工在当前订单门店存在，显示接单按钮
          console.log('员工在当前订单门店存在，显示接单按钮')
          this.userStatus = 'existing'
          // 不自动接单，让用户主动点击接单按钮
        } else if (result.exists_in_other_store) {
          // 情况2：员工在其他门店存在，显示复制按钮
          console.log('员工在其他门店存在，显示复制按钮')
          this.userStatus = 'copying'
          // 不自动复制，让用户主动点击复制按钮
        } else {
          // 情况3：员工在任何门店都不存在，需要快速入驻
          console.log('员工不存在，需要快速入驻')
          this.userStatus = 'new'
          // 不自动跳转，让用户主动点击注册按钮
        }
      } catch (error) {
        console.error('检查用户身份失败:', error)
        uni.showToast({
          title: '身份验证失败',
          icon: 'none'
        })
      }
    },

    // 检查员工是否存在
    async checkStaffExists(mobile, orderNumber) {
      try {
        const response = await checkStaffExists(mobile, orderNumber)
        console.log('checkStaffExists响应数据:', response)

        // 处理API响应，确保返回标准格式
        // 期望的API响应格式：
        // {
        //   exists_in_order_store: boolean,  // 是否在订单门店存在
        //   exists_in_other_store: boolean,  // 是否在其他门店存在
        //   staff_info: object               // 员工信息
        // }

        // 如果API返回新格式，直接使用
        if (typeof response.exists_in_order_store !== 'undefined') {
          return response
        }

        // 兼容旧格式，转换为新格式
        // 注意：旧格式无法区分是否在订单门店，需要后端API升级
        return {
          exists_in_order_store: response.exists || false,
          exists_in_other_store: false, // 旧API无法区分
          staff_info: response.staff_info || null
        }
      } catch (error) {
        console.error('检查员工状态失败:', error)
        throw new Error(error.message || '身份验证失败')
      }
    },

    // 接单操作
    async handleAcceptOrder() {
      // 检查是否设置了预设佣金
      if (!this.orderInfo || !this.orderInfo.preset_commission || this.orderInfo.preset_commission <= 0) {
        uni.showModal({
          title: '无法接单',
          content: '该订单未设置提成金额，无法被接单。请联系分享者设置佣金后再试。',
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#ff6b35'
        })
        return
      }

      this.acceptLoading = true

      try {
        // 调用接单API
        await this.acceptOrderByShare()
        
        uni.showToast({
          title: '接单成功！',
          icon: 'success'
        })

        // 延迟跳转
        setTimeout(() => {
          this.goBack()
        }, 1500)
      } catch (error) {
        console.error('接单失败:', error)

        // 根据错误类型给出不同提示
        let errorMessage = '接单失败，请重试'
        if (error.message && error.message.includes('订单已被接单')) {
          errorMessage = '该订单已被其他员工接单'
        } else if (error.message && error.message.includes('状态不允许接单')) {
          errorMessage = '订单状态不允许接单'
          // 如果是重复接单导致的状态错误，不重置用户状态
          if (this.isFromRegistration) {
            console.log('重复接单错误，保持用户状态为existing')
          }
        } else if (error.message && error.message.includes('Signature has expired')) {
          errorMessage = '授权已过期，请重新验证手机号'
        } else if (error.message) {
          errorMessage = error.message
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      } finally {
        this.acceptLoading = false
      }
    },

    // 分享接单API
    async acceptOrderByShare() {
      try {
        const response = await acceptOrderByShare({
          order_number: this.orderNumber,
          mobile: this.userMobile,
          openid: this.userOpenid
        })
        return response
      } catch (error) {
        console.error('接单失败:', error)
        throw new Error(error.message || '接单失败，请重试')
      }
    },

    // 跳转到员工入驻申请页面（备用方案，跳转到商家端注册页面）
    handleRegister() {
      // 使用现有的员工入驻申请页面
      uni.navigateTo({
        url: `/pages/login/register?from=order-share&orderNumber=${this.orderNumber}&mobile=${this.userMobile}&openid=${this.userOpenid}`
      })
    },

    // 复制员工到当前门店
    async handleCopyStaffToStore() {
      try {
        this.copyLoading = true
        this.userStatus = 'copying-progress'

        // 调用复制员工API，传递openid
        const result = await this.copyStaffToStore(this.userMobile, this.orderNumber, this.userOpenid)

        if (result && result.success) {
          uni.showToast({
            title: '员工信息已同步',
            icon: 'success'
          })

          // 复制成功后，更新状态为可以接单，但不自动接单
          this.userStatus = 'existing'
        } else {
          throw new Error(result.message || '员工信息同步失败')
        }
      } catch (error) {
        console.error('复制员工到门店失败:', error)
        uni.showToast({
          title: error.message || '处理失败，请重试',
          icon: 'none'
        })
        // 复制失败，回到复制状态
        this.userStatus = 'copying'
      } finally {
        this.copyLoading = false
      }
    },

    // 快速员工入驻
    handleQuickRegister() {
      // 构建跳转URL
      let url = `/pages-home/attendantManage-add?from=order-share&orderNumber=${this.orderNumber}&mobile=${this.userMobile}&openid=${this.userOpenid}`

      // 如果有分享员工ID（邀请码），则添加到URL中
      if (this.inviterUserId) {
        url += `&invitation_code=${this.inviterUserId}`
        console.log('使用邀请码进行员工入驻:', this.inviterUserId)
      } else {
        console.log('无邀请码，使用普通员工入驻流程')
      }

      uni.navigateTo({
        url: url
      })
    },

    // 复制员工到门店的API调用
    async copyStaffToStore(mobile, orderNumber, openid) {
      try {
        const response = await copyStaffToStore(mobile, orderNumber, openid)
        console.log('copyStaffToStore响应数据:', response)
        return response
      } catch (error) {
        console.error('复制员工到门店失败:', error)
        throw new Error(error.message || '员工信息同步失败')
      }
    }
  },

  // 微信小程序分享功能
  onShareAppMessage() {
    let shareobj = {
      title: '家政服务好帮手，进来逛逛吧~', //分享的标题
      path: '/pages/login/login?tg=' + uni.getStorageSync('tg') + '&shareScene=' + uni.getStorageSync('scene'), //好友点击分享之后跳转的页面
      imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png", //分享的图片
    };
    return shareobj;
  },

  // 分享到朋友圈（如果需要）
  onShareTimeline() {
    return {
      title: '家政服务好帮手，进来逛逛吧~',
      imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png"
    };
  }
}
</script>

<style lang="scss" scoped>
.order-share-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f7fb 0%, #ffffff 100%);
  padding-bottom: 120rpx;
}

// 自定义头部
.custom-header {
  position: relative;
  height: calc(120rpx + var(--status-bar-height));

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding-top: var(--status-bar-height);
    height: 120rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
  }

  .nav-bar {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .nav-left, .nav-right {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 30rpx;
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
      }
    }

    .nav-center {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .nav-title {
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;

  .loading-text {
    margin-top: 20rpx;
    color: #666;
    font-size: 28rpx;
  }
}

// 内容容器
.content-container {
  padding: 30rpx;
}

// 订单卡片
.order-card {
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 40rpx;

  .card-header {
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    padding: 30rpx;
    display: flex;
    align-items: center;

    .service-badge {
      background: rgba(255, 255, 255, 0.9);
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      margin-right: 20rpx;

      .badge-text {
        color: #ff801b;
        font-size: 24rpx;
        font-weight: bold;
      }
    }

    .product-name {
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
      flex: 1;
    }

    .order-status {
      padding: 8rpx 16rpx;
      border-radius: 24rpx;
      font-size: 22rpx;
      font-weight: 500;
      margin-left: 20rpx;
    }

    .status-pending {
      background-color: rgba(255, 255, 255, 0.2);
      color: #fff;
    }

    .status-assigned {
      background-color: rgba(245, 124, 0, 0.9);
      color: #fff;
    }

    .status-progress {
      background-color: rgba(56, 142, 60, 0.9);
      color: #fff;
    }

    .status-completed {
      background-color: rgba(123, 31, 162, 0.9);
      color: #fff;
    }

    .status-rejected,
    .status-cancelled {
      background-color: rgba(211, 47, 47, 0.9);
      color: #fff;
    }

    .status-unknown {
      background-color: rgba(102, 102, 102, 0.9);
      color: #fff;
    }
  }

  .card-content {
    padding: 30rpx;
  }
}

// 信息项
.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25rpx;

  .info-icon {
    width: 40rpx;
    height: 40rpx;
    background: rgba(253, 209, 24, 0.1);
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    margin-top: 5rpx;
  }

  .info-content {
    flex: 1;

    .info-label {
      display: block;
      color: #999;
      font-size: 24rpx;
      margin-bottom: 8rpx;
    }

    .info-value {
      color: #333;
      font-size: 28rpx;
      line-height: 1.4;
    }
  }
}

// 薪酬信息
.salary-info {
  background: linear-gradient(135deg, rgba(253, 209, 24, 0.1) 0%, rgba(255, 128, 27, 0.1) 100%);
  padding: 25rpx;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 25rpx 0;
  border: 1rpx solid rgba(253, 209, 24, 0.3);

  .salary-label {
    color: #666;
    font-size: 28rpx;
  }

  .salary-amount {
    color: #ff801b;
    font-size: 36rpx;
    font-weight: bold;
  }
}



// 服务要求
.service-remark {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-top: 20rpx;

  .remark-label {
    color: #666;
    font-size: 24rpx;
    margin-bottom: 8rpx;
  }

  .remark-content {
    color: #333;
    font-size: 26rpx;
    line-height: 1.5;
  }
}

// 状态提示
.status-notice, .commission-notice {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-top: 20rpx;
  background-color: #fff3e0;
  border-radius: 16rpx;
  border-left: 6rpx solid #ff6b35;

  .notice-text {
    margin-left: 16rpx;
    color: #ff6b35;
    font-size: 26rpx;
    font-weight: 500;
  }
}

// 操作按钮区域
.action-container {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

// 授权区域
.auth-section {
  text-align: center;

  .auth-tips {
    margin-bottom: 30rpx;

    .tips-text {
      color: #666;
      font-size: 26rpx;
    }
  }

  .auth-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
    color: #fff;
    border: none;
    border-radius: 44rpx;
    font-size: 30rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 20rpx rgba(7, 193, 96, 0.3);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 15rpx rgba(7, 193, 96, 0.2);
    }

    &[loading] {
      opacity: 0.7;
    }
  }
}

// 订单操作
.order-actions {
  .existing-user-actions {
    .accept-btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      color: #fff;
      border: none;
      border-radius: 44rpx;
      font-size: 32rpx;
      font-weight: bold;
      box-shadow: 0 6rpx 20rpx rgba(253, 209, 24, 0.4);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 15rpx rgba(253, 209, 24, 0.3);
      }

      &[loading] {
        opacity: 0.7;
      }
    }
  }

  .new-user-actions {
    text-align: center;

    .register-tips {
      margin-bottom: 30rpx;

      .tips-text {
        color: #666;
        font-size: 26rpx;
      }
    }

    .register-btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      border: none;
      border-radius: 44rpx;
      font-size: 30rpx;
      font-weight: bold;
      box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.2);
      }
    }
  }

  .copying-user-actions {
    text-align: center;

    .copy-tips {
      margin-bottom: 30rpx;

      .tips-text {
        color: #666;
        font-size: 26rpx;
        line-height: 1.4;
      }
    }

    .copy-btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: #fff;
      border: none;
      border-radius: 44rpx;
      font-size: 30rpx;
      font-weight: bold;
      box-shadow: 0 6rpx 20rpx rgba(79, 172, 254, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 15rpx rgba(79, 172, 254, 0.2);
      }

      &[loading] {
        opacity: 0.7;
      }
    }
  }

  .copying-status, .checking-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0;

    .copying-text, .checking-text {
      margin-top: 20rpx;
      color: #666;
      font-size: 26rpx;
    }
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 30rpx;
  text-align: center;

  .error-icon {
    margin-bottom: 30rpx;
  }

  .error-text {
    color: #666;
    font-size: 28rpx;
    margin-bottom: 40rpx;
    line-height: 1.5;
  }

  .retry-btn {
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    color: #fff;
    border: none;
    padding: 20rpx 40rpx;
    border-radius: 30rpx;
    font-size: 26rpx;

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
