"""
门店数据访问层
"""
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional, Tuple
from exceptions.exception import QueryException
from utils.log_util import logger


class StoreDao:
    """门店数据访问层"""
    
    @staticmethod
    async def get_store_list(
        db: AsyncSession, 
        company_id: str,
        page: int = 1, 
        size: int = 10,
        status: Optional[int] = None
    ) -> Tu<PERSON>[List[Dict[str, Any]], int]:
        """
        获取门店列表
        
        Args:
            db: 数据库会话
            company_id: 公司ID
            page: 页码
            size: 每页数量
            status: 门店状态筛选（0-关闭，1-营业）
            
        Returns:
            门店列表和总数
        """
        try:
            # 构建查询条件
            where_conditions = ["company_id = :company_id", "is_delete = '0'"]
            params = {"company_id": company_id}
            
            if status is not None:
                where_conditions.append("status = :status")
                params["status"] = status
            
            where_clause = " AND ".join(where_conditions)
            
            # 查询总数
            count_query = f"""
                SELECT COUNT(*) as total
                FROM store
                WHERE {where_clause}
            """
            
            count_result = await db.execute(text(count_query), params)
            total = count_result.fetchone()[0]
            
            # 查询列表数据
            offset = (page - 1) * size
            list_query = f"""
                SELECT
                    id,
                    store_uuid,
                    company_id,
                    name,
                    phone,
                    mobile,
                    address,
                    business_hours,
                    manager,
                    status,
                    store_status,
                    remark,
                    introduce,
                    email,
                    is_new,
                    level,
                    flag,
                    is_show_wxapp,
                    open_shop_name,
                    open_shop_uuid,
                    dredge_date,
                    expiry_date,
                    insurance_company,
                    user_count,
                    is_xyj,
                    newhome_id,
                    mini_qr_url,
                    create_time,
                    update_time
                FROM store
                WHERE {where_clause}
                ORDER BY create_time DESC
                LIMIT :size OFFSET :offset
            """
            
            params.update({"size": size, "offset": offset})
            list_result = await db.execute(text(list_query), params)
            
            # 转换结果为字典列表
            stores = []
            for row in list_result.fetchall():
                store = {}
                for key, value in row._mapping.items():
                    store[key] = value
                stores.append(store)
            
            logger.info(f"查询门店列表成功，公司ID: {company_id}, 总数: {total}")
            return stores, total
            
        except Exception as e:
            logger.error(f"查询门店列表失败: {str(e)}")
            raise QueryException(message=f"查询门店列表失败: {str(e)}")
    
    @staticmethod
    async def get_store_detail(db: AsyncSession, store_uuid: str, company_id: str) -> Optional[Dict[str, Any]]:
        """
        获取门店详情
        
        Args:
            db: 数据库会话
            store_uuid: 门店UUID
            company_id: 公司ID
            
        Returns:
            门店详情信息
        """
        try:
            query = """
                SELECT
                    id,
                    store_uuid,
                    company_id,
                    name,
                    phone,
                    mobile,
                    address,
                    business_hours,
                    manager,
                    status,
                    store_status,
                    remark,
                    introduce,
                    email,
                    is_new,
                    level,
                    flag,
                    is_show_wxapp,
                    open_shop_name,
                    open_shop_uuid,
                    dredge_date,
                    expiry_date,
                    insurance_company,
                    user_count,
                    is_xyj,
                    newhome_id,
                    mini_qr_url,
                    create_time,
                    update_time
                FROM store
                WHERE store_uuid = :store_uuid AND company_id = :company_id AND is_delete = '0'
            """
            
            result = await db.execute(text(query), {
                "store_uuid": store_uuid,
                "company_id": company_id
            })
            row = result.fetchone()
            
            if not row:
                return None
            
            # 转换为字典
            store = {}
            for key, value in row._mapping.items():
                store[key] = value
            
            logger.info(f"查询门店详情成功，门店UUID: {store_uuid}")
            return store
            
        except Exception as e:
            logger.error(f"查询门店详情失败: {str(e)}")
            raise QueryException(message=f"查询门店详情失败: {str(e)}")
    
    @staticmethod
    async def get_store_count(db: AsyncSession, company_id: str) -> Dict[str, int]:
        """
        获取门店统计信息
        
        Args:
            db: 数据库会话
            company_id: 公司ID
            
        Returns:
            门店统计信息
        """
        try:
            query = """
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as open_count,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as closed_count
                FROM store
                WHERE company_id = :company_id AND is_delete = '0'
            """
            
            result = await db.execute(text(query), {"company_id": company_id})
            row = result.fetchone()

            if row:
                stats = {
                    "total": row[0] or 0,
                    "open_count": row[1] or 0,
                    "closed_count": row[2] or 0
                }
            else:
                stats = {
                    "total": 0,
                    "open_count": 0,
                    "closed_count": 0
                }
            
            logger.info(f"查询门店统计成功，公司ID: {company_id}, 统计: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"查询门店统计失败: {str(e)}")
            raise QueryException(message=f"查询门店统计失败: {str(e)}")
