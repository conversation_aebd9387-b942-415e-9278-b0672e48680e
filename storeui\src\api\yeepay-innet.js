import { post, get } from '../utlis/require.js'

/**
 * 易宝支付入网相关API
 */

// 企业/个体户入网
export const enterpriseInnet = (data) => {
  return post('/api/v1/yeepay/innet/enterprise', data, { contentType: 'application/json' })
}

// 小微/个人入网
export const microInnet = (data) => {
  return post('/api/v1/yeepay/innet/micro', data, { contentType: 'application/json' })
}

// 入网结果查询
export const queryInnetResult = (data) => {
  return post('/api/v1/yeepay/innet/query', data, { contentType: 'application/json' })
}

// 文件上传
export const uploadInnetFile = (file, fileType) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('file_type', fileType)
  
  return post('/api/v1/yeepay/innet/upload', formData, { 
    contentType: 'multipart/form-data' 
  })
}

// 获取入网常量
export const getInnetConstants = () => {
  return get('/api/v1/yeepay/innet/constants')
}

/**
 * 入网数据构建工具函数
 */

// 构建企业/个体户入网数据
export const buildEnterpriseInnetData = ({
  // 入网主体信息
  ownerType,        // 主体类型：01-企业，02-个体工商户
  ownerName,        // 主体名称
  ownerShortName,   // 主体简称
  ownerCertNo,      // 证件号码（统一社会信用代码）
  ownerCertPic,     // 证件照片文件ID
  ownerCertType = 'L01', // 证件类型，默认L01（营业执照）
  
  // 法人信息
  juridicalName,    // 法人姓名
  juridicalCertNo,  // 法人证件号码
  juridicalCertType = 'P01', // 法人证件类型，默认P01（身份证）
  juridicalCertPic, // 法人证件人像面文件ID
  juridicalCertBackPic, // 法人证件非人像面文件ID
  juridicalMobile,  // 法人手机号
  
  // 联系人信息
  contactName,      // 联系人姓名
  contactMobile,    // 联系人手机号
  contactEmail,     // 联系人邮箱
  
  // 经营地址信息（可选）
  province,         // 省份
  city,             // 城市
  district,         // 区县
  address,          // 详细地址
  
  // 结算账户信息（可选）
  accountType,      // 账户类型：01-个人借记卡，02-对公一般户，03-企业单位结算卡
  accountNo,        // 账号
  accountName,      // 账户名称
  bankCode,         // 开户行代码
  bankName,         // 开户行名称
  settleCycle = 'T1', // 结算周期，默认T1
  
  // 产品信息（可选）
  products = [],    // 产品列表
  productRates = [], // 产品费率列表
  
  businessMode = 'cus' // 业务模式，默认cus
}) => {
  const data = {
    innetOwner: {
      ownerType,
      ownerName,
      ownerShortName,
      ownerCertNo,
      ownerCertPic,
      ownerCertType
    },
    innetJuridical: {
      juridicalName,
      juridicalCertNo,
      juridicalCertType,
      juridicalCertPic,
      juridicalCertBackPic,
      juridicalMobile
    },
    innetContact: {
      contactName,
      contactMobile,
      contactEmail
    },
    businessMode
  }
  
  // 添加经营地址信息（必填）
  if (province && city && district && address) {
    data.innetAddress = {
      province,
      city,
      district,
      address
    }
  }

  // 添加结算账户信息（必填）
  if (accountType && accountNo && accountName && bankCode) {
    data.innetAccount = {
      accountType,
      accountNo,
      accountName,
      bankCode,
      bankName,
      settleCycle
    }
  }
  
  // 添加可选的产品信息
  if (products && products.length > 0) {
    data.innetProduct = products
  }
  
  // 添加可选的产品费率信息
  if (productRates && productRates.length > 0) {
    data.innetProductRate = productRates
  }
  
  return data
}

// 构建小微/个人入网数据
export const buildMicroInnetData = ({
  // 入网主体信息
  ownerName,        // 主体名称
  ownerShortName,   // 主体简称
  
  // 法人信息
  juridicalName,    // 法人姓名
  juridicalCertNo,  // 法人证件号码
  juridicalCertType = 'P01', // 法人证件类型，默认P01（身份证）
  juridicalCertPic, // 法人证件人像面文件ID
  juridicalCertBackPic, // 法人证件非人像面文件ID
  juridicalMobile,  // 法人手机号
  
  // 经营地址信息（必填）
  province,         // 省份
  city,             // 城市
  district,         // 区县
  address,          // 详细地址

  // 结算账户信息（必填）
  accountType,      // 账户类型：01-个人借记卡，02-对公一般户，03-企业单位结算卡
  accountNo,        // 账号
  accountName,      // 账户名称
  bankCode,         // 开户行代码
  bankName,         // 开户行名称
  settleCycle = 'T1', // 结算周期，默认T1
  
  businessMode = 'cus' // 业务模式，默认cus
}) => {
  const data = {
    innetOwner: {
      ownerName,
      ownerShortName
    },
    innetJuridical: {
      juridicalName,
      juridicalCertNo,
      juridicalCertType,
      juridicalCertPic,
      juridicalCertBackPic,
      juridicalMobile
    },
    businessMode
  }
  
  // 添加经营地址信息（必填）
  if (province && city && district && address) {
    data.innetAddress = {
      province,
      city,
      district,
      address
    }
  }

  // 添加结算账户信息（必填）
  if (accountType && accountNo && accountName && bankCode) {
    data.innetAccount = {
      accountType,
      accountNo,
      accountName,
      bankCode,
      bankName,
      settleCycle
    }
  }
  
  return data
}

/**
 * 入网常量定义
 */
export const INNET_CONSTANTS = {
  // 主体类型
  OWNER_TYPES: {
    ENTERPRISE: '01',    // 企业
    INDIVIDUAL: '02'     // 个体工商户
  },
  
  // 证件类型
  CERT_TYPES: {
    BUSINESS_LICENSE: 'L01', // 营业执照
    ID_CARD: 'P01'          // 身份证
  },
  
  // 账户类型
  ACCOUNT_TYPES: {
    PERSONAL_DEBIT: '01',        // 个人借记卡
    CORPORATE_GENERAL: '02',     // 对公一般户
    ENTERPRISE_SETTLEMENT: '03'  // 企业单位结算卡
  },
  
  // 结算周期
  SETTLE_CYCLES: {
    T0: 'T0',  // T+0
    T1: 'T1'   // T+1
  },
  
  // 业务模式
  BUSINESS_MODES: {
    CUS: 'cus'  // 客户模式
  },
  
  // 响应码
  RSP_CODES: {
    SUCCESS: '0000'  // 成功
  },
  
  // 入网状态
  INNET_STATUSES: {
    PENDING: '01',   // 待审核
    APPROVED: '02',  // 审核通过
    REJECTED: '03'   // 审核拒绝
  },
  
  // 文件类型
  FILE_TYPES: {
    IMAGE: 'image',  // 图片
    PDF: 'pdf'       // PDF文档
  }
}

/**
 * 入网状态文本映射
 */
export const getInnetStatusText = (status) => {
  const statusMap = {
    '01': '待审核',
    '02': '审核通过',
    '03': '审核拒绝'
  }
  return statusMap[status] || '未知状态'
}

/**
 * 主体类型文本映射
 */
export const getOwnerTypeText = (type) => {
  const typeMap = {
    '01': '企业',
    '02': '个体工商户'
  }
  return typeMap[type] || '未知类型'
}

/**
 * 账户类型文本映射
 */
export const getAccountTypeText = (type) => {
  const typeMap = {
    '01': '个人借记卡',
    '02': '对公一般户',
    '03': '企业单位结算卡'
  }
  return typeMap[type] || '未知类型'
}
