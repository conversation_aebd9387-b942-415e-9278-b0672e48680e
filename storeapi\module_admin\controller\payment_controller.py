from fastapi import APIRouter, Depends, Body, Request, File, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any
import json
from config.get_db import get_db
from utils.response_util import ResponseUtil
from exceptions.exception import BusinessException, ValidationException
from utils.log_util import logger
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.service.payment_service import PaymentService
from module_admin.service.common_service import CommonService

# 创建支付相关的API路由
payment_controller = APIRouter(
    prefix="/api/v1/payment",
    tags=["支付接口"],
    dependencies=[Depends(InternalUserLoginService.get_current_user)]
)

@payment_controller.post('/recharge', summary="用户充值")
async def recharge(
    request: Request,
    recharge_data: Dict[str, Any] = Body(..., description="充值数据"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """用户充值接口
    
    创建易宝支付订单，返回支付参数供前端调用微信支付
    
    Args:
        request: 请求对象
        recharge_data: 充值数据，包含amount字段
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        支付参数或支付链接
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        
        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")
            
        # 验证充值金额
        amount = recharge_data.get('amount')
        if not amount or amount <= 0:
            raise ValidationException(message="充值金额必须大于0")
            
        logger.info(f"用户 {current_user_id} 发起充值，金额: {amount}")
        
        # 调用支付服务创建易宝支付订单
        payment_result = await PaymentService.create_yeepay_order_service(
            query_db, current_user_id, amount, request
        )
        
        return ResponseUtil.success(
            msg="创建支付订单成功",
            data=payment_result
        )
        
    except ValidationException as e:
        logger.error(f"充值参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"充值业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"充值异常: {str(e)}")
        return ResponseUtil.error(msg=f"充值失败: {str(e)}", code=500)


@payment_controller.post('/recharge/qrcode', summary="扫码支付充值")
async def qrcode_recharge(
    request: Request,
    recharge_data: Dict[str, Any] = Body(..., description="充值数据"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """扫码支付充值接口

    创建易宝扫码支付订单，返回支付二维码URL

    Args:
        request: 请求对象
        recharge_data: 充值数据，包含amount字段
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        支付二维码URL
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        # 验证充值金额
        amount = recharge_data.get('amount')
        if not amount or amount <= 0:
            raise ValidationException(message="充值金额必须大于0")

        logger.info(f"用户 {current_user_id} 发起扫码支付充值，金额: {amount}")

        # 调用支付服务创建易宝扫码支付订单
        payment_result = await PaymentService.create_yeepay_qrcode_order_service(
            query_db, current_user_id, amount, request
        )

        return ResponseUtil.success(
            msg="创建扫码支付订单成功",
            data=payment_result
        )

    except ValidationException as e:
        logger.error(f"扫码支付充值参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"扫码支付充值业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"扫码支付充值异常: {str(e)}")
        return ResponseUtil.error(msg=f"扫码支付充值失败: {str(e)}", code=500)


@payment_controller.post('/yeepay/notify', summary="易宝支付异步通知")
async def yeepay_notify(
    request: Request,
    query_db: AsyncSession = Depends(get_db)
):
    """易宝支付异步通知接口

    接收易宝支付的异步通知，处理支付结果

    Args:
        request: 请求对象
        query_db: 数据库会话

    Returns:
        处理结果
    """
    try:
        # 获取原始请求体
        body = await request.body()
        logger.info(f"=== 收到易宝支付异步通知 ===")
        logger.info(f"请求方法: {request.method}")
        logger.info(f"请求头: {dict(request.headers)}")
        logger.info(f"原始请求体: {body.decode('utf-8') if body else 'Empty'}")

        # 解析JSON数据
        try:
            if body:
                notify_data = json.loads(body.decode('utf-8'))
            else:
                logger.error("请求体为空")
                return ResponseUtil.error(msg="请求体为空", code=400)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return ResponseUtil.error(msg="请求数据格式错误", code=400)

        logger.info(f"解析后的通知数据: {notify_data}")

        # 调用支付服务处理通知
        result = await PaymentService.handle_yeepay_notify_service(query_db, notify_data)

        # 易宝支付要求返回特定格式的响应
        response_data = {
            "code": "SUCCESS",
            "message": "处理成功",
            "result": result.get("status", "success")
        }

        logger.info(f"返回给易宝的响应: {response_data}")
        return response_data

    except Exception as e:
        logger.error(f"处理易宝支付通知异常: {str(e)}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")

        # 即使处理失败，也要返回成功响应给易宝，避免重复通知
        return {
            "code": "SUCCESS",
            "message": "已收到通知"
        }


@payment_controller.get('/balance', summary="获取用户余额")
async def get_user_balance(
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取用户余额接口
    
    获取当前登录用户的余额信息
    
    Args:
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        用户余额信息
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        
        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")
            
        logger.info(f"获取用户 {current_user_id} 余额信息")
        
        # 调用支付服务获取余额
        balance_info = await PaymentService.get_user_balance_service(query_db, current_user_id)
        
        return ResponseUtil.success(
            msg="获取用户余额成功",
            data=balance_info
        )
        
    except ValidationException as e:
        logger.error(f"获取余额参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"获取余额业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取余额异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取余额失败: {str(e)}", code=500)


@payment_controller.get('/balance/flow', summary="获取余额流水")
async def get_balance_flow(
    start_date: str = None,
    end_date: str = None,
    flow_type: str = None,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取余额流水接口
    
    获取当前登录用户的余额流水记录
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        flow_type: 流水类型 (income/expense/all)
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        余额流水列表
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        
        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")
            
        logger.info(f"获取用户 {current_user_id} 余额流水，日期范围: {start_date} - {end_date}, 类型: {flow_type}")
        
        # 调用支付服务获取流水
        flow_list = await PaymentService.get_balance_flow_service(
            query_db, current_user_id, start_date, end_date, flow_type
        )
        
        return ResponseUtil.success(
            msg="获取余额流水成功",
            data=flow_list
        )
        
    except ValidationException as e:
        logger.error(f"获取流水参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"获取流水业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取流水异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取流水失败: {str(e)}", code=500)


@payment_controller.get('/order/status/{order_number}', summary="查询支付订单状态")
async def query_payment_order_status(
    order_number: str,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """查询支付订单状态

    Args:
        order_number: 订单号
        current_user: 当前用户
        query_db: 数据库会话

    Returns:
        订单状态信息
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        logger.info(f"查询支付订单状态 - 用户ID: {current_user_id}, 订单号: {order_number}")

        # 调用支付服务查询易宝支付订单状态
        order_status = await PaymentService.query_yeepay_order_status_service(
            query_db, current_user_id, order_number
        )

        return ResponseUtil.success(
            msg="查询订单状态成功",
            data=order_status
        )

    except ValidationException as e:
        logger.error(f"查询订单状态参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"查询订单状态业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"查询支付订单状态异常: {str(e)}")
        return ResponseUtil.error(msg=f"查询订单状态失败: {str(e)}", code=500)



        logger.error(f"查询支付订单状态异常: {str(e)}")
        return ResponseUtil.error(msg=f"查询订单状态失败: {str(e)}", code=500)


@payment_controller.post('/create-order', summary="创建支付订单")
async def create_payment_order(
    request: Request,
    order_data: Dict[str, Any] = Body(..., description="订单数据"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """创建支付订单接口

    支持各种业务类型的支付订单创建，如认证服务等

    Args:
        request: 请求对象
        order_data: 订单数据，包含business_type、amount等字段
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        支付结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        current_user_name = getattr(current_user.user, 'user_name', None) or getattr(current_user.user, 'name', '未知用户')

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        # 验证必要参数
        business_type = order_data.get('business_type')
        business_type_name = order_data.get('business_type_name')
        amount = order_data.get('amount')
        description = order_data.get('description', '')
        related_info = order_data.get('related_info', {})

        if not business_type:
            raise ValidationException(message="业务类型不能为空")
        if not business_type_name:
            raise ValidationException(message="业务类型名称不能为空")
        if not amount or amount <= 0:
            raise ValidationException(message="支付金额必须大于0")

        logger.info(f"用户 {current_user_id} 创建支付订单 - 业务类型: {business_type}, 金额: {amount}")

        # 调用支付服务创建订单
        payment_result = await PaymentService.create_business_payment_order_service(
            query_db, current_user_id, current_user_name, business_type,
            business_type_name, amount, description, related_info
        )

        return ResponseUtil.success(
            msg="创建支付订单成功",
            data=payment_result
        )

    except ValidationException as e:
        logger.error(f"创建支付订单参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"创建支付订单业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"创建支付订单异常: {str(e)}")
        return ResponseUtil.error(msg=f"创建支付订单失败: {str(e)}", code=500)


@payment_controller.post('/withdrawal/apply', summary="申请提现")
async def apply_withdrawal(
    request: Request,
    withdrawal_data: Dict[str, Any] = Body(..., description="提现申请数据"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """申请提现接口

    创建提现申请，提交审核流程

    Args:
        request: 请求对象
        withdrawal_data: 提现申请数据
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        提现申请结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        current_user_name = getattr(current_user.user, 'user_name', None) or getattr(current_user.user, 'name', None)

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not current_user_name:
            logger.warning("当前用户姓名获取失败")
            raise ValidationException(message="当前用户姓名获取失败")

        # 验证必要字段
        required_fields = ['withdrawal_type', 'apply_amount', 'bank_name', 'bank_account', 'account_holder']
        for field in required_fields:
            if not withdrawal_data.get(field):
                raise ValidationException(message=f"缺少必要字段: {field}")

        # 验证提现金额
        apply_amount = withdrawal_data.get('apply_amount')
        if not isinstance(apply_amount, (int, float)) or apply_amount <= 0:
            raise ValidationException(message="提现金额必须大于0")

        if apply_amount <= 99:
            raise ValidationException(message="最低提现金额为100元")

        logger.info(f"用户 {current_user_id} 申请提现，金额: {apply_amount}")

        # 调用支付服务创建提现申请
        withdrawal_result = await PaymentService.create_withdrawal_application_service(
            query_db, current_user_id, current_user_name, withdrawal_data
        )

        return ResponseUtil.success(
            msg="提现申请提交成功",
            data=withdrawal_result
        )

    except ValidationException as e:
        logger.warning(f"提现申请参数验证失败: {str(e)}")
        return ResponseUtil.error(
            msg=str(e)
        )
    except BusinessException as e:
        logger.error(f"提现申请业务异常: {str(e)}")
        return ResponseUtil.error(
            msg=str(e)
        )
    except Exception as e:
        logger.error(f"提现申请系统异常: {str(e)}")
        return ResponseUtil.error(
            msg=f"提现申请失败: {str(e)}"
        )


@payment_controller.get('/withdrawal/status/{withdrawal_no}', summary="查询提现申请状态")
async def get_withdrawal_status(
    withdrawal_no: str,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """查询提现申请状态接口

    Args:
        withdrawal_no: 提现申请单号
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        提现申请状态信息
    """
    try:
        # 获取当前用户信息（与其他接口保持一致）
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 查询提现申请状态，公司: {company_uuid}, 提现单号: {withdrawal_no}")

        # 查询提现申请信息
        withdrawal_query = """
            SELECT withdrawal_no, withdrawal_type, withdrawal_type_name,
                   apply_amount, actual_amount, status, apply_time,
                   bank_name, bank_account, account_holder,
                   review_time, review_comment, completion_time
            FROM company_withdrawal
            WHERE withdrawal_no = :withdrawal_no AND company_uuid = :company_uuid
        """

        result = await query_db.execute(withdrawal_query, {
            "withdrawal_no": withdrawal_no,
            "company_uuid": company_uuid
        })
        withdrawal_row = result.fetchone()

        if not withdrawal_row:
            raise BusinessException(message="提现申请不存在或无权限查看")

        withdrawal_data = withdrawal_row._mapping

        # 构建返回数据
        status_info = {
            "withdrawal_no": withdrawal_data["withdrawal_no"],
            "withdrawal_type": withdrawal_data["withdrawal_type"],
            "withdrawal_type_name": withdrawal_data["withdrawal_type_name"],
            "apply_amount": float(withdrawal_data["apply_amount"]),
            "actual_amount": float(withdrawal_data["actual_amount"]) if withdrawal_data["actual_amount"] else None,
            "status": withdrawal_data["status"],
            "status_name": _get_status_name(withdrawal_data["status"]),
            "apply_time": withdrawal_data["apply_time"].isoformat() if withdrawal_data["apply_time"] else None,
            "bank_name": withdrawal_data["bank_name"],
            "bank_account": withdrawal_data["bank_account"],
            "account_holder": withdrawal_data["account_holder"],
            "review_time": withdrawal_data["review_time"].isoformat() if withdrawal_data["review_time"] else None,
            "review_comment": withdrawal_data["review_comment"],
            "completion_time": withdrawal_data["completion_time"].isoformat() if withdrawal_data["completion_time"] else None
        }

        return ResponseUtil.success(
            msg="查询提现申请状态成功",
            data=status_info
        )

    except ValidationException as e:
        logger.warning(f"查询提现申请状态参数验证失败: {str(e)}")
        return ResponseUtil.validation_error(msg=str(e))
    except BusinessException as e:
        logger.error(f"查询提现申请状态业务异常: {str(e)}")
        return ResponseUtil.business_error(msg=str(e))
    except Exception as e:
        logger.error(f"查询提现申请状态系统异常: {str(e)}")
        return ResponseUtil.error(msg=f"查询提现申请状态失败: {str(e)}")

def _get_status_name(status: str) -> str:
    """获取状态名称"""
    status_map = {
        "PENDING": "待审核",
        "APPROVED": "已审核",
        "PROCESSING": "处理中",
        "COMPLETED": "已完成",
        "REJECTED": "已拒绝"
    }
    return status_map.get(status, "未知状态")



