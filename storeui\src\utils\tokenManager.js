import store from '../vuex/index.js'
import { get } from '../utlis/require.js'
import { showMsg } from '../utlis/common.js'

/**
 * 统一Token管理器
 * 解决多角色token管理混乱问题
 */
class TokenManager {
  constructor() {
    this.lastActiveTime = Date.now()
    this.backgroundThreshold = 5 * 60 * 1000 // 5分钟后台运行阈值
  }

  /**
   * 获取当前有效的token
   * @returns {string|null} 当前有效的token
   */
  getCurrentToken() {
    const currentRole = this.getCurrentRole()
    
    if (currentRole === 'staff') {
      // 员工端：优先使用access_token，其次staffToken
      return store?.state?.access_token || 
             store?.state?.staffToken || 
             uni.getStorageSync('access_token') || 
             uni.getStorageSync('staffToken')
    } else {
      // 管理端：使用普通token
      return store?.state?.token || uni.getStorageSync('token')
    }
  }

  /**
   * 获取当前角色
   * @returns {string} 当前角色 'staff' 或 'store'
   */
  getCurrentRole() {
    return store?.state?.currentRole || 
           uni.getStorageSync('currentRole') || 
           'store'
  }

  /**
   * 设置token（根据角色自动选择存储位置）
   * @param {string} token - 新的token
   * @param {string} role - 角色类型
   * @param {Object} additionalData - 额外数据
   */
  setToken(token, role = 'store', additionalData = {}) {
    console.log(`=== TokenManager: 设置${role}端token ===`)
    
    if (role === 'staff') {
      // 员工端token设置
      store.commit('Updates', {
        access_token: token,
        staffToken: token,
        token: token, // 兼容性保留
        currentRole: 'staff',
        isLogin: true,
        ...additionalData
      })
      
      // 本地存储
      uni.setStorageSync('access_token', token)
      uni.setStorageSync('staffToken', token)
      uni.setStorageSync('token', token) // 兼容性保留
      uni.setStorageSync('currentRole', 'staff')
      uni.setStorageSync('isLogin', true)
    } else {
      // 管理端token设置
      store.commit('Updates', {
        token: token,
        currentRole: 'store',
        isLogin: true,
        ...additionalData
      })
      
      // 本地存储
      uni.setStorageSync('token', token)
      uni.setStorageSync('currentRole', 'store')
      uni.setStorageSync('isLogin', true)
    }
    
    // 保存额外数据到本地存储
    Object.keys(additionalData).forEach(key => {
      if (additionalData[key] !== undefined) {
        uni.setStorageSync(key, additionalData[key])
      }
    })
    
    console.log(`TokenManager: ${role}端token设置完成`)
  }

  /**
   * 清除所有token和认证信息
   * @param {boolean} clearStorage - 是否清除本地存储
   */
  clearAllTokens(clearStorage = true) {
    console.log('=== TokenManager: 清除所有token ===')
    
    // 清除Vuex状态
    store.commit('clearAuth', clearStorage)
    
    if (clearStorage) {
      // 清除本地存储
      const keysToRemove = [
        'token', 'staffToken', 'access_token',
        'userInfo', 'storeInfo', 'staffInfo',
        'currentRole', 'currentSelectedCompany',
        'isLogin'
      ]
      
      keysToRemove.forEach(key => {
        try {
          uni.removeStorageSync(key)
        } catch (error) {
          console.error(`清除${key}失败:`, error)
        }
      })
    }
    
    console.log('TokenManager: 所有token已清除')
  }

  /**
   * 验证当前token是否有效
   * @returns {Promise<boolean>} token是否有效
   */
  async validateCurrentToken() {
    const token = this.getCurrentToken()
    const currentRole = this.getCurrentRole()
    
    if (!token) {
      console.log('TokenManager: 没有找到token')
      return false
    }
    
    try {
      console.log(`=== TokenManager: 验证${currentRole}端token ===`)
      
      // 根据角色选择验证接口
      const apiUrl = currentRole === 'staff'
        ? '/api/v1/staff-login/getinfo'
        : '/api/v1/internal-login/getInfo'
      
      const result = await get(apiUrl, {}, { showErr: false })
      
      if (result) {
        console.log(`TokenManager: ${currentRole}端token验证成功`)
        
        // 更新用户信息
        if (currentRole === 'staff' && result.staff_info) {
          store.commit('Updates', { staffInfo: result.staff_info })
          uni.setStorageSync('staffInfo', result.staff_info)
        } else if (currentRole === 'store' && result.user_info) {
          store.commit('Updates', { user: result.user_info })
          uni.setStorageSync('user', result.user_info)
        }
        
        return true
      }
      
      return false
    } catch (error) {
      console.log(`TokenManager: ${currentRole}端token验证失败`, error)
      
      // 只有401、403视为token过期
      const isAuthError = error.code === 401 || error.code === 403
      
      if (isAuthError) {
        console.log('TokenManager: 检测到认证错误，清除token')
        this.clearAllTokens(true)
        showMsg('您当前未登录或登录已过期，请重新登录')
      }
      
      return false
    }
  }

  /**
   * 角色切换时的token处理
   * @param {string} newRole - 新角色
   */
  handleRoleSwitch(newRole) {
    console.log(`=== TokenManager: 角色切换到${newRole} ===`)
    
    const oldRole = this.getCurrentRole()
    
    if (oldRole === newRole) {
      console.log('TokenManager: 角色未变化，无需处理')
      return
    }
    
    // 清除当前角色的token缓存（但保留本地存储）
    if (newRole === 'staff') {
      // 切换到员工端：清除管理端的Vuex状态
      store.commit('Updates', {
        token: '', // 清除管理端token的Vuex状态
        currentRole: 'staff'
      })
    } else {
      // 切换到管理端：清除员工端的Vuex状态
      store.commit('Updates', {
        access_token: '', // 清除员工端token的Vuex状态
        staffToken: '',
        currentRole: 'store'
      })
    }
    
    // 从本地存储恢复新角色的token
    this.restoreTokenFromStorage(newRole)
    
    console.log(`TokenManager: 角色切换完成，当前角色: ${newRole}`)
  }

  /**
   * 从本地存储恢复token
   * @param {string} role - 角色类型
   */
  restoreTokenFromStorage(role = null) {
    const currentRole = role || this.getCurrentRole()
    
    console.log(`=== TokenManager: 从本地存储恢复${currentRole}端状态 ===`)
    
    try {
      if (currentRole === 'staff') {
        const accessToken = uni.getStorageSync('access_token')
        const staffToken = uni.getStorageSync('staffToken')
        const staffInfo = uni.getStorageSync('staffInfo')
        
        const finalToken = accessToken || staffToken
        
        if (finalToken) {
          store.commit('Updates', {
            access_token: accessToken,
            staffToken: staffToken,
            token: finalToken, // 兼容性
            currentRole: 'staff',
            staffInfo: staffInfo,
            isLogin: true
          })
        }
      } else {
        const token = uni.getStorageSync('token')
        const storeInfo = uni.getStorageSync('storeInfo')
        const user = uni.getStorageSync('user')
        
        if (token) {
          store.commit('Updates', {
            token: token,
            currentRole: 'store',
            storeInfo: storeInfo,
            user: user,
            isLogin: true
          })
        }
      }
      
      console.log(`TokenManager: ${currentRole}端状态恢复完成`)
    } catch (error) {
      console.error('TokenManager: 状态恢复失败', error)
    }
  }

  /**
   * 检查是否需要重新验证（基于后台运行时间）
   * @returns {boolean} 是否需要重新验证
   */
  shouldRevalidate() {
    const now = Date.now()
    const timeSinceLastActive = now - this.lastActiveTime
    
    console.log(`TokenManager: 距离上次活跃时间: ${Math.round(timeSinceLastActive / 1000)}秒`)
    
    return timeSinceLastActive > this.backgroundThreshold
  }

  /**
   * 更新最后活跃时间
   */
  updateLastActiveTime() {
    this.lastActiveTime = Date.now()
    console.log('TokenManager: 更新最后活跃时间')
  }

  /**
   * 应用进入后台时调用
   */
  onAppHide() {
    console.log('TokenManager: 应用进入后台')
    // 记录进入后台的时间已经在updateLastActiveTime中处理
  }

  /**
   * 应用从后台恢复时调用
   * @returns {Promise<boolean>} 是否需要重新登录
   */
  async onAppShow() {
    console.log('TokenManager: 应用从后台恢复')
    
    if (this.shouldRevalidate()) {
      console.log('TokenManager: 后台运行时间过长，重新验证token')
      const isValid = await this.validateCurrentToken()
      
      if (!isValid) {
        console.log('TokenManager: token验证失败，需要重新登录')
        return false
      }
    }
    
    this.updateLastActiveTime()
    return true
  }
}

// 创建单例实例
const tokenManager = new TokenManager()

export default tokenManager
