"""
公司数据模型
用于公司信息的数据库映射
"""
from datetime import datetime
from sqlalchemy import Column, String, DECIMAL, DateTime
from config.database import Base


class Company(Base):
    """公司信息表"""

    __tablename__ = 'company'

    # 主键
    id = Column(String(64), primary_key=True, comment='公司ID')
    
    # 基本信息
    name = Column(String(100), nullable=False, comment='公司名称')
    city_id = Column(String(20), nullable=True, comment='城市ID')
    city = Column(String(50), nullable=True, comment='城市')
    address = Column(String(255), nullable=True, comment='地址')
    address_desc = Column(String(255), nullable=True, comment='地址描述')
    
    # 财务信息
    balance = Column(DECIMAL(15, 2), nullable=False, default=0.00, comment='公司余额')
    frozen_amount = Column(DECIMAL(15, 2), nullable=False, default=0.00, comment='冻结金额')
    withdrawal_fee_rate = Column(DECIMAL(5, 4), nullable=False, default=0.0000, comment='提现费率(小数形式，如0.2000表示20%)')
    
    # 状态信息
    status = Column(String(2), nullable=True, default='1', comment='状态(1:正常,0:冻结)')
    is_delete = Column(String(2), nullable=True, default='0', comment='是否删除(1:已删除,0:未删除)')
    
    def __repr__(self):
        return f"<Company(id='{self.id}', name='{self.name}')>"
