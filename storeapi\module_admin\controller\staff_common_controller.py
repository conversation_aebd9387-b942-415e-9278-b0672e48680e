from fastapi import APIRouter, Depends, UploadFile, File, BackgroundTasks, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.service.common_service import CommonService
from module_admin.service.service_staff_login_service import ServiceStaffLoginService
from utils.response_util import ResponseUtil
from utils.log_util import logger

# 创建路由器
staff_common_controller = APIRouter(prefix='/api/v1/staff/common', tags=['员工端通用模块'])


@staff_common_controller.post('/upload', summary="员工端文件上传接口")
async def staff_upload(
    file: UploadFile = File(...),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """员工端文件上传接口

    上传文件到华为云OBS并返回文件访问URL和相关信息

    - **file**: 要上传的文件
    """
    try:
        # 从当前员工信息中获取员工姓名
        staff_name = getattr(current_staff, 'real_name', None) or getattr(current_staff, 'mobile', 'unknown')

        logger.info(f"员工 {staff_name} 开始上传文件: {file.filename}")

        upload_result = await CommonService.upload_service(file, None, staff_name)
        logger.info(f'员工文件上传成功: {file.filename}')

        return ResponseUtil.success(data=upload_result)

    except Exception as e:
        logger.error(f"员工文件上传失败: {str(e)}")
        return ResponseUtil.error(msg=f"上传失败: {str(e)}")


@staff_common_controller.get('/download')
async def staff_download(
    request: Request,
    background_tasks: BackgroundTasks,
    file_name: str = Query(alias='fileName'),
    delete: bool = Query(),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """员工端文件下载接口"""
    try:
        staff_name = getattr(current_staff, 'real_name', None) or getattr(current_staff, 'mobile', 'unknown')
        logger.info(f"员工 {staff_name} 下载文件: {file_name}")

        download_result = await CommonService.download_services(background_tasks, file_name, delete)
        logger.info(download_result.message)

        return ResponseUtil.streaming(data=download_result.result)

    except Exception as e:
        logger.error(f"员工文件下载失败: {str(e)}")
        return ResponseUtil.error(msg=f"下载失败: {str(e)}")


@staff_common_controller.get('/download/resource')
async def staff_download_resource(
    request: Request, 
    resource: str = Query(),
    current_staff = Depends(ServiceStaffLoginService.get_current_staff)
):
    """员工端资源文件下载接口"""
    try:
        staff_name = getattr(current_staff, 'real_name', None) or getattr(current_staff, 'mobile', 'unknown')
        logger.info(f"员工 {staff_name} 下载资源: {resource}")

        download_resource_result = await CommonService.download_resource_services(resource)
        logger.info(download_resource_result.message)

        return ResponseUtil.streaming(data=download_resource_result.result)

    except Exception as e:
        logger.error(f"员工资源下载失败: {str(e)}")
        return ResponseUtil.error(msg=f"下载失败: {str(e)}")
