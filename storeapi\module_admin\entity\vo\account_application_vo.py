from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Union
from decimal import Decimal


class IdCardFrontOCRVO(BaseModel):
    """身份证正面OCR识别结果VO"""
    model_config = ConfigDict(from_attributes=True)

    name: Optional[str] = Field(None, description="姓名")
    id_number: Optional[str] = Field(None, description="身份证号码")
    gender: Optional[str] = Field(None, description="性别")
    birthday: Optional[str] = Field(None, description="生日")
    address: Optional[str] = Field(None, description="住址")
    province: Optional[str] = Field(None, description="省份")
    city: Optional[str] = Field(None, description="城市")
    district: Optional[str] = Field(None, description="区县")


class IdCardBackOCRVO(BaseModel):
    """身份证背面OCR识别结果VO"""
    model_config = ConfigDict(from_attributes=True)

    issue_authority: Optional[str] = Field(None, description="签发机关")
    valid_period: Optional[str] = Field(None, description="有效期")


class BankCardOCRVO(BaseModel):
    """银行卡OCR识别结果VO"""
    model_config = ConfigDict(from_attributes=True)

    bank_name: Optional[str] = Field(None, description="银行名称")
    card_number: Optional[str] = Field(None, description="银行卡号")
    card_holder: Optional[str] = Field(None, description="持卡人姓名")
    card_type: Optional[Union[str, int]] = Field(None, description="卡片类型（支持字符串或数字）")


class AccountApplicationSubmitVO(BaseModel):
    """开户申请提交VO"""
    model_config = ConfigDict(from_attributes=True)

    # 基本信息
    mobile: str = Field(..., description="手机号")

    # OCR识别结果
    id_card_front_ocr: Optional[IdCardFrontOCRVO] = Field(None, description="身份证正面OCR识别结果")
    id_card_back_ocr: Optional[IdCardBackOCRVO] = Field(None, description="身份证背面OCR识别结果")
    bank_card_ocr: Optional[BankCardOCRVO] = Field(None, description="银行卡OCR识别结果")

    # 图片URL
    id_card_front_url: Optional[str] = Field(None, description="身份证正面照片URL")
    id_card_back_url: Optional[str] = Field(None, description="身份证背面照片URL")
    bank_card_url: Optional[str] = Field(None, description="银行卡照片URL")

    # 易宝支付相关
    yeepay_customer_code: Optional[str] = Field(None, description="易宝入网客户代码")


class AccountApplicationDetailVO(BaseModel):
    """开户申请详情VO"""
    model_config = ConfigDict(from_attributes=True)
    
    uuid: str = Field(..., description="申请UUID")
    mobile: str = Field(..., description="手机号")
    name: Optional[str] = Field(None, description="姓名")
    id_number: Optional[str] = Field(None, description="身份证号")
    gender: Optional[str] = Field(None, description="性别")
    gender_name: Optional[str] = Field(None, description="性别名称")
    birthday: Optional[str] = Field(None, description="出生日期")
    
    # 地址信息
    province: Optional[str] = Field(None, description="省份")
    city: Optional[str] = Field(None, description="城市")
    district: Optional[str] = Field(None, description="区县")
    address: Optional[str] = Field(None, description="详细地址")
    full_address: Optional[str] = Field(None, description="完整地址")
    
    # 职业信息
    occupation: Optional[str] = Field(None, description="职业")
    company_name: Optional[str] = Field(None, description="工作单位")
    annual_income: Optional[Decimal] = Field(None, description="年收入")
    
    # 银行信息
    bank_name: Optional[str] = Field(None, description="开户银行")
    bank_code: Optional[str] = Field(None, description="银行代码")
    account_no: Optional[str] = Field(None, description="银行账号")
    account_name: Optional[str] = Field(None, description="账户名称")
    
    # 证件信息
    id_card_front_url: Optional[str] = Field(None, description="身份证正面照片URL")
    id_card_back_url: Optional[str] = Field(None, description="身份证背面照片URL")
    bank_card_url: Optional[str] = Field(None, description="银行卡照片URL")
    issuing_authority: Optional[str] = Field(None, description="签发机关")
    validity_period: Optional[str] = Field(None, description="有效期限")

    # 易宝支付相关
    yeepay_customer_code: Optional[str] = Field(None, description="易宝入网客户代码")
    
    # 申请状态
    status: str = Field(..., description="申请状态")
    status_name: Optional[str] = Field(None, description="状态名称")
    remark: Optional[str] = Field(None, description="备注")
    
    # 时间信息
    apply_time: Optional[str] = Field(None, description="申请时间")
    review_time: Optional[str] = Field(None, description="审核时间")
    reviewer_name: Optional[str] = Field(None, description="审核人姓名")


class AccountApplicationListVO(BaseModel):
    """开户申请列表VO"""
    model_config = ConfigDict(from_attributes=True)
    
    uuid: str = Field(..., description="申请UUID")
    mobile: str = Field(..., description="手机号")
    name: Optional[str] = Field(None, description="姓名")
    id_number: Optional[str] = Field(None, description="身份证号")
    status: str = Field(..., description="申请状态")
    status_name: Optional[str] = Field(None, description="状态名称")
    apply_time: Optional[str] = Field(None, description="申请时间")
    review_time: Optional[str] = Field(None, description="审核时间")
    reviewer_name: Optional[str] = Field(None, description="审核人姓名")


class AccountApplicationReviewVO(BaseModel):
    """开户申请审核VO"""
    model_config = ConfigDict(from_attributes=True)
    
    uuid: str = Field(..., description="申请UUID")
    status: str = Field(..., description="审核状态(2:通过,3:拒绝)")
    remark: Optional[str] = Field(None, description="审核备注")


class AccountApplicationStatusVO(BaseModel):
    """开户申请状态VO"""
    model_config = ConfigDict(from_attributes=True)
    
    has_application: bool = Field(..., description="是否有申请记录")
    status: Optional[str] = Field(None, description="申请状态")
    status_name: Optional[str] = Field(None, description="状态名称")
    apply_time: Optional[str] = Field(None, description="申请时间")
    review_time: Optional[str] = Field(None, description="审核时间")
    remark: Optional[str] = Field(None, description="备注")
