<template>
  <view class="page-container pos-re animated fadeIn">
    <!-- 头部背景区域 - 统一使用dispatch风格 -->
    <view class="header-background-section" :style="{ '--status-bar-height': statusBarHeight + 'px' }">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航栏 -->
        <view class="header-section">
          <view class="navbar">
            <text class="nav-title">服务人员</text>
          </view>
        </view>

        <!-- 标签栏 -->
        <view class="tab-section">
          <scroll-view class="tab-container" scroll-x>
            <view class="tab-wrapper">
              <view
                class="tab-item"
                :class="{ active: currentServiceType === '1' }"
                @click="switchServiceType('1')"
              >
                <text class="tab-text">到家</text>
                <view v-if="currentServiceType === '1'" class="tab-line"></view>
              </view>
              <view
                class="tab-item"
                :class="{ active: currentServiceType === '2' }"
                @click="switchServiceType('2')"
              >
                <text class="tab-text">三嫂</text>
                <view v-if="currentServiceType === '2'" class="tab-line"></view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- 内容区域 - 统一使用dispatch风格 -->
    <view class="content">
      <!-- 搜索筛选容器 -->
      <view class="search-filter-container">
        <!-- 搜索框 -->
        <view class="search-box">
          <view class="search-input">
            <u-icon name="search" size="20" color="#999"></u-icon>
            <input
              type="text"
              placeholder="请输入员工ID, 客户姓名/手机号"
              v-model="searchKeyword"
              @input="onSearchInput"
              @confirm="performSearch"
            />
            <view class="search-clear" v-if="searchKeyword" @click="clearSearch">
              <u-icon name="close-circle" size="14" color="#999"></u-icon>
            </view>
          </view>
        </view>

        <!-- 筛选扩展区域 -->
        <view class="filter-extension">
          <!-- 横向滚动状态筛选 -->
          <view class="horizontal-status-filter">
            <scroll-view scroll-x class="status-scroll">
              <view class="status-scroll-content">
                <view
                  class="status-filter-item"
                  :class="{ active: activeTab === 0 }"
                  @click="switchTab(0)"
                >
                  <text>全部</text>
                </view>
                <view
                  class="status-filter-item"
                  :class="{ active: activeTab === 1 }"
                  @click="switchTab(1)"
                >
                  <text>正常</text>
                </view>
                <view
                  class="status-filter-item"
                  :class="{ active: activeTab === 2 }"
                  @click="switchTab(2)"
                >
                  <text>冻结</text>
                </view>
                <view
                  class="status-filter-item"
                  :class="{ active: activeTab === 3 }"
                  @click="switchTab(3)"
                >
                  <text>离职</text>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>

      <!-- 员工列表 -->
      <scroll-view
        class="staff-list-container"
        scroll-y
        :style="{ height: scrollViewHeight }"
        @scrolltolower="onLoadMore"
        refresher-enabled
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
      >
        <view class="staff-list">
          <view
            class="staff-item"
            v-for="(staff, index) in staffList"
            :key="index"
            @click="navigateToDetail(staff)"
          >
            <!-- 员工基本信息 -->
            <view class="staff-info" @click.stop>
              <image
                :src="staff.avatar || 'https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/img/logo.png'"
                class="avatar"
                mode="aspectFill"
              ></image>

              <view class="info-content">
                <view class="info-header">
                  <view class="name-container">
                    <text class="staff-name">{{ staff.real_name }}</text>
                    <view class="status-badge" :class="{
                      'frozen': staff.status === '0',
                      'normal': staff.status === '1',
                      'offline': staff.status === '2',
                      'resigned': staff.status === '3',
                      'blacklisted': staff.status === '4'
                    }">
                      {{ getStatusText(staff.status) }}
                    </view>
                  </view>
                  <view class="staff-source">
                    <text class="source-text">系统</text>
                  </view>
                </view>

                <view class="staff-contact">
                  <text class="staff-phone">{{ staff.mobile || '未填写手机号' }}</text>
                  <text class="join-time" v-if="staff.create_time">入职时间: {{ formatDate(staff.create_time) }}</text>
                </view>

                <view class="staff-location" v-if="staff.address">
                  <text class="location-text">{{ staff.address }}</text>
                </view>

                <view class="staff-skills" v-if="staff.skills && staff.skills.length > 0">
                  <text class="skills-label">技能:</text>
                  <text class="skills-content">{{ staff.skills.slice(0, 3).join('、') }}</text>
                </view>
              </view>
            </view>

            <!-- 员工统计信息 -->
            <view class="staff-stats">
              <view class="stat-item">
                <text class="label">年龄</text>
                <text class="value">{{ staff.age || '未知' }}</text>
              </view>
              <view class="stat-item">
                <text class="label">服务次数</text>
                <text class="value">{{ staff.service_cnt || 0 }}</text>
              </view>
              <view class="stat-item">
                <text class="label">评分</text>
                <text class="value">{{ staff.rating || '暂无' }}</text>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="staff-actions">
              <view class="action-btn primary-btn" @click.stop="handleCall(staff.mobile)">
                <text>联系员工</text>
              </view>
              <view class="action-btn primary-btn" @click.stop="handleStaffStatusToggle(staff)">
                <text>{{ getActionButtonText(staff.status) }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && staffList.length === 0">
          <u-icon name="account" size="60" color="#ccc"></u-icon>
          <text class="empty-text">暂无员工信息</text>
        </view>

        <!-- 加载状态 -->
        <view class="loading-more" v-if="loadingMore">
          <u-loading-icon mode="flower" color="#FF6B35"></u-loading-icon>
          <text class="loading-text">加载中...</text>
        </view>
      </scroll-view>
    </view>

    <!-- 悬浮添加按钮 - 统一使用dispatch样式 -->
    <view class="floating-btn-container">
      <view class="order-floating-btn" @tap="handleAddStaff">
        <u-icon name="plus" size="24" color="#fff"></u-icon>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <u-popup :show="showFilterModal" mode="bottom" @close="showFilterModal = false">
      <view class="filter-modal">
        <view class="filter-header">
          <text class="filter-title">筛选条件</text>
          <text class="filter-reset" @click="resetFilter">重置</text>
        </view>

        <view class="filter-content">
          <!-- 员工状态筛选 -->
          <view class="filter-section">
            <text class="filter-section-title">员工状态</text>
            <view class="filter-options">
              <view
                class="filter-option"
                :class="{ active: filterStatus.includes(status.value) }"
                v-for="status in statusOptions"
                :key="status.value"
                @click="toggleFilterStatus(status.value)"
              >
                {{ status.label }}
              </view>
            </view>
          </view>

          <!-- 技能筛选 -->
          <view class="filter-section">
            <text class="filter-section-title">服务技能</text>
            <view class="filter-options">
              <view
                class="filter-option"
                :class="{ active: filterSkills.includes(skill) }"
                v-for="skill in skillOptions"
                :key="skill"
                @click="toggleFilterSkill(skill)"
              >
                {{ skill }}
              </view>
            </view>
          </view>
        </view>

        <view class="filter-footer">
          <u-button type="default" @click="showFilterModal = false" custom-style="margin-right: 12px;">取消</u-button>
          <u-button type="primary" @click="applyFilter">确定</u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getStaffList, getAuntManageList } from '@/api/staff.js';

export default {
  data() {
    return {
      // 状态栏高度
      statusBarHeight: 0,

      // 服务类型：1-到家，2-三嫂
      currentServiceType: '1',

      // 页面状态
      loading: false,
      refreshing: false,
      loadingMore: false,

      // 统计数据
      statistics: {
        total: 0,
        active: 0,
        frozen: 0,
        online: 0
      },

      // 标签页数据
      activeTab: 0,
      tabs: [
        { name: '全部', count: 0 },
        { name: '正常', count: 0 },
        { name: '下架', count: 0 }
      ],

      // 分页数据
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      totalPages: 0,

      // 搜索相关
      searchKeyword: '',
      searchTimer: null, // 搜索防抖定时器

      // 筛选相关
      showFilterModal: false,
      filterStatus: [],
      filterSkills: [],
      statusOptions: [
        { label: '正常', value: 'normal' },
        { label: '下架', value: 'frozen' }
      ],
      skillOptions: ['家政清洁', '母婴护理', '老人护理', '烹饪', '育儿', '护理'],

      // 员工列表数据
      staffList: [],

      // 排序相关
      sortField: '', // 当前排序字段
      sortOrder: 'desc' // 排序方向：asc升序，desc降序
    };
  },
  computed: {
    // 计算滚动视图高度
    scrollViewHeight() {
      // 获取系统信息
      const systemInfo = uni.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight || 0;
      const screenHeight = systemInfo.screenHeight || 667;

      // 计算各部分高度（单位：px）
      // 头部背景区域：状态栏 + 导航栏 + 标签栏，大约200px
      const headerBackgroundHeight = 200;
      // 搜索筛选区域：搜索框 + 筛选按钮，大约120px
      const searchFilterHeight = 120;
      // 底部安全区域
      const bottomSafeArea = 20;

      // 计算可用高度
      const availableHeight = screenHeight - headerBackgroundHeight - searchFilterHeight - bottomSafeArea;

      return `${Math.max(availableHeight, 400)}px`; // 最小高度400px
    }
  },
  mounted() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight || 0;

    this.loadStaffList();
  },

  onShow() {
    console.log('服务人员页面显示，刷新员工列表');
    // 每次页面显示时刷新员工列表
    this.loadStaffList(true);

    // 监听员工入驻成功的刷新事件
    uni.$on('staffListRefresh', () => {
      this.loadStaffList(true); // 刷新列表
    });
  },

  onHide() {
    // 移除事件监听
    uni.$off('staffListRefresh');
  },

  beforeDestroy() {
    // 清理搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    // 移除事件监听
    uni.$off('staffListRefresh');
  },

  methods: {
    // 切换服务类型
    switchServiceType(type) {
      if (this.currentServiceType === type) return;

      this.currentServiceType = type;
      this.currentPage = 1;
      this.staffList = [];
      this.loadStaffList(true);
    },

    // 切换标签页
    switchTab(index) {
      this.activeTab = index;
      // 重新加载数据
      this.currentPage = 1;
      this.staffList = [];
      this.loadStaffList(true);
    },

    // 下拉刷新
    onRefresh() {
      this.loadStaffList(true);
    },

    // 上拉加载更多
    onLoadMore() {
      // 防止重复加载
      if (this.loading || this.loadingMore || this.refreshing) {
        return;
      }

      if (this.currentPage < this.totalPages) {
        this.loadingMore = true;
        this.currentPage++;
        this.loadStaffList();
      }
    },

    // 搜索相关方法
    onSearchInput() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置防抖，500ms后执行搜索
      this.searchTimer = setTimeout(() => {
        this.performSearch();
      }, 500);
    },

    performSearch() {
      // 重置页码并加载数据
      this.currentPage = 1;
      this.loadStaffList();
    },

    clearSearch() {
      this.searchKeyword = '';
      // 清空搜索后重新加载数据
      this.currentPage = 1;
      this.loadStaffList();
    },

    // 筛选相关方法
    handleFilter() {
      this.showFilterModal = true;
    },

    toggleFilterStatus(status) {
      const index = this.filterStatus.indexOf(status);
      if (index > -1) {
        this.filterStatus.splice(index, 1);
      } else {
        this.filterStatus.push(status);
      }
    },

    toggleFilterSkill(skill) {
      const index = this.filterSkills.indexOf(skill);
      if (index > -1) {
        this.filterSkills.splice(index, 1);
      } else {
        this.filterSkills.push(skill);
      }
    },

    resetFilter() {
      this.filterStatus = [];
      this.filterSkills = [];
    },

    applyFilter() {
      this.showFilterModal = false;

      // 重置页码并加载数据
      this.currentPage = 1;
      this.loadStaffList();
    },

    // 员工操作方法
    navigateToDetail(staff) {
      console.log('点击员工详情，员工数据:', JSON.stringify(staff, null, 2));

      // 根据服务类型决定使用哪个标识符
      let staffIdentifier;
      if (this.currentServiceType === '2') {
        // 三嫂服务 - 必须使用UUID
        staffIdentifier = staff.uuid;
        if (!staffIdentifier) {
          console.error('三嫂服务员工数据无效，缺少UUID:', staff);
          uni.showToast({
            title: '员工信息异常，缺少UUID',
            icon: 'none'
          });
          return;
        }
        console.log('准备跳转到三嫂简历详情页，使用UUID:', staffIdentifier);
        uni.navigateTo({
          url: `/pages/aunt-resume/detail?uuid=${staffIdentifier}`
        });
      } else {
        // 到家服务 - 可以使用ID或UUID
        staffIdentifier = staff.id || staff.uuid;
        if (!staffIdentifier) {
          console.error('到家服务员工数据无效，缺少ID或UUID:', staff);
          uni.showToast({
            title: '员工信息异常，无法查看详情',
            icon: 'none'
          });
          return;
        }
        console.log('准备跳转到到家简历页面，使用ID:', staffIdentifier);
        uni.navigateTo({
          url: `/pages-staff/resume?id=${staffIdentifier}`
        });
      }
    },

    // 获取状态显示文字
    getStatusText(status) {
      const statusMap = {
        '0': '冻结',
        '1': '正常',
        '2': '下架',
        '3': '离职',
        '4': '拉黑'
      };
      return statusMap[status] || '未知';
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        '0': 'frozen',
        '1': 'normal',
        '2': 'offline',
        '3': 'resigned',
        '4': 'blacklisted'
      };
      return classMap[status] || '';
    },

    // 获取操作按钮文字
    getActionButtonText(status) {
      // 正常状态显示"下架员工"，下架状态显示"上架员工"
      if (status === '1') {
        return '下架员工';
      } else if (status === '2') {
        return '上架员工';
      } else {
        return '操作员工'; // 其他状态的默认文字
      }
    },

    // 员工状态切换处理
    handleStaffStatusToggle(staff) {
      if (staff.status === '1') {
        // 正常状态 -> 下架
        this.handleOfflineStaff(staff);
      } else if (staff.status === '2') {
        // 下架状态 -> 上架
        this.handleOnlineStaff(staff);
      } else {
        // 其他状态暂不支持操作
        uni.showToast({
          title: '当前状态不支持此操作',
          icon: 'none'
        });
      }
    },

    handleCall(phone) {
      uni.makePhoneCall({
        phoneNumber: phone
      });
    },

    handleOfflineStaff(staff) {
      // 弹出确认提示框
      uni.showModal({
        title: '确认下架',
        content: `确定要下架员工"${staff.real_name || staff.name}"吗？下架后该员工将无法接收新订单。`,
        confirmText: '确定下架',
        cancelText: '取消',
        confirmColor: '#ff4d4f',
        success: (res) => {
          if (res.confirm) {
            this.offlineStaffRequest(staff);
          }
        }
      });
    },

    // 调用下架员工接口
    offlineStaffRequest(staff) {
      uni.showLoading({
        title: '处理中...'
      });

      // 验证员工数据
      const staffId = staff.id || staff.uuid;
      if (!staffId) {
        uni.hideLoading();
        uni.showToast({
          title: '员工信息异常，操作失败',
          icon: 'none'
        });
        return;
      }

      // 调用下架员工API
      const { post } = require('@/utlis/require.js');
      post('/api/v1/staff/offline', {
        staff_id: String(staffId), // 确保转换为字符串
        status: '2' // 2表示下架状态
      }, {
        contentType: 'application/json' // 指定JSON格式
      }).then(() => {
        uni.hideLoading();
        uni.showToast({
          title: '员工下架成功',
          icon: 'success'
        });

        // 刷新员工列表
        this.loadStaffList(true);
      }).catch(error => {
        uni.hideLoading();
        console.error('下架员工失败:', error);
        uni.showToast({
          title: error?.message || '下架失败，请重试',
          icon: 'none'
        });
      });
    },

    // 上架员工处理
    handleOnlineStaff(staff) {
      // 弹出确认提示框
      uni.showModal({
        title: '确认上架',
        content: `确定要上架员工"${staff.real_name || staff.name}"吗？上架后该员工将可以接收新订单。`,
        confirmText: '确定上架',
        cancelText: '取消',
        confirmColor: '#09be89',
        success: (res) => {
          if (res.confirm) {
            this.onlineStaffRequest(staff);
          }
        }
      });
    },

    // 调用上架员工接口
    onlineStaffRequest(staff) {
      uni.showLoading({
        title: '处理中...'
      });

      // 验证员工数据
      const staffId = staff.id || staff.uuid;
      if (!staffId) {
        uni.hideLoading();
        uni.showToast({
          title: '员工信息异常，操作失败',
          icon: 'none'
        });
        return;
      }

      // 调用上架员工API
      const { post } = require('@/utlis/require.js');
      post('/api/v1/staff/online', {
        staff_id: String(staffId), // 确保转换为字符串
        status: '1' // 1表示正常状态
      }, {
        contentType: 'application/json' // 指定JSON格式
      }).then(() => {
        uni.hideLoading();
        uni.showToast({
          title: '员工上架成功',
          icon: 'success'
        });

        // 刷新员工列表
        this.loadStaffList(true);
      }).catch(error => {
        uni.hideLoading();
        console.error('上架员工失败:', error);
        uni.showToast({
          title: error?.message || '上架失败，请重试',
          icon: 'none'
        });
      });
    },

    handleAssign() {
      // 这里可以跳转到派单页面
      uni.showToast({
        title: '派单功能开发中',
        icon: 'none'
      });
    },

    handleAddStaff() {
      // 根据当前服务类型决定跳转页面
      if (this.currentServiceType === '2') {
        // 三嫂服务 - 跳转到简历录入页面
        uni.navigateTo({
          url: '/pages-staff/resume-add'
        });
      } else {
        // 到家服务 - 跳转到原来的添加服务人员页面
        uni.navigateTo({
          url: '/pages-home/attendantManage-add'
        });
      }
    },

    // 排序处理方法
    handleSort(field) {
      // 如果点击的是当前排序字段，则切换排序方向
      if (this.sortField === field) {
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
      } else {
        // 如果是新的排序字段，默认降序
        this.sortField = field;
        this.sortOrder = 'desc';
      }

      // 重新加载数据
      this.currentPage = 1;
      this.loadStaffList();
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${month}-${day}`;
    },

    // handleAddStaff方法已在上面定义，删除重复方法

    // 加载员工列表
    async loadStaffList(isRefresh = false) {
      try {
        if (isRefresh) {
          this.refreshing = true;
          this.currentPage = 1; // 刷新时重置页码
        } else {
          this.loading = true;
        }

        // 构建查询参数
        const params = {
          page: this.currentPage,
          page_size: this.pageSize,
          work_type: this.currentServiceType // 添加工作类型筛选
        };

        // 添加状态筛选
        if (this.activeTab === 1) {
          params.status = '1'; // 正常
        } else if (this.activeTab === 2) {
          params.status = '2'; // 下架（对应冻结功能）
        } else if (this.activeTab === 3) {
          params.status = '3'; // 离职
        }

        // 添加搜索关键词
        if (this.searchKeyword) {
          params.keyword = this.searchKeyword;
        }

        // 添加排序参数
        if (this.sortField) {
          params.sort_field = this.sortField;
          params.sort_order = this.sortOrder;
        }

        // 根据服务类型选择不同的API
        let response;
        if (this.currentServiceType === '2') {
          // 三嫂服务，暂时也使用门店端的阿姨管理列表接口
          response = await getAuntManageList(params);
        } else {
          // 到家服务，调用原有员工列表接口
          response = await getStaffList(params);
        }

        // 处理响应数据 - 支持多种响应格式
        let staffData = null;
        let totalCount = 0;
        let currentPage = 1;
        let totalPages = 0;
        let statisticsData = null;

        if (response) {
          // 情况1: 响应直接是数组（原来的格式）
          if (Array.isArray(response)) {
            staffData = response;
            totalCount = response.length;
            currentPage = this.currentPage;
            totalPages = 1;
          }
          // 情况2: 响应有list字段（新的格式）
          else if (response.list !== undefined) {
            staffData = response.list;
            totalCount = response.total || 0;
            currentPage = response.page || this.currentPage;
            totalPages = response.total_pages || 0;
            statisticsData = response.statistics;
          }
          // 情况3: 响应有data字段
          else if (response.data !== undefined) {
            if (Array.isArray(response.data)) {
              staffData = response.data;
              totalCount = response.total || response.data.length;
              currentPage = response.page || this.currentPage;
              totalPages = response.total_pages || 1;
              statisticsData = response.statistics;
            } else if (response.data.list !== undefined) {
              staffData = response.data.list;
              totalCount = response.data.total || 0;
              currentPage = response.data.page || this.currentPage;
              totalPages = response.data.total_pages || 0;
              statisticsData = response.data.statistics;
            }
          }
        }

        if (staffData && Array.isArray(staffData)) {
          // 更新员工列表
          if (isRefresh || this.currentPage === 1) {
            // 清空现有数据
            this.staffList.splice(0, this.staffList.length);
          }

          // 处理员工数据
          if (staffData.length > 0) {
            console.log('原始员工数据:', JSON.stringify(staffData, null, 2));

            // 处理员工数据，确保显示正确的中文内容
            const processedList = staffData.map((staff, index) => {
              // 验证员工数据的有效性
              if (!staff.id && !staff.uuid) {
                console.warn(`员工数据[${index}]缺少ID和UUID:`, staff);
              } else {
                console.log(`员工数据[${index}] ID: ${staff.id}, UUID: ${staff.uuid}, 姓名: ${staff.real_name}`);
              }

              return {
                ...staff,
                // 确保姓名显示正确
                real_name: staff.real_name || staff.name || staff.username || '未知',
                // 确保年龄显示正确
                age: staff.age || '未知',
                // 确保地址显示正确
                address: staff.address || '未填写地址',
                // 确保服务次数显示正确
                service_cnt: staff.service_cnt || 0,
                // 处理技能数据
                skills: Array.isArray(staff.skills) ? staff.skills : (staff.skills ? staff.skills.split(',') : []),
                // 确保状态字段正确
                status: String(staff.status || '1') // 确保状态是字符串类型
              };
            });

            this.staffList.push(...processedList);
          }

          // 更新分页信息
          this.totalCount = totalCount;
          this.totalPages = totalPages;
          this.currentPage = currentPage;

          // 更新统计数据
          if (statisticsData) {
            this.statistics = {
              total: statisticsData.total || 0,
              active: statisticsData.normal || 0,
              frozen: statisticsData.frozen || 0,
              online: statisticsData.idle || 0
            };

            // 更新标签页计数
            this.tabs = [
              { name: '全部', count: statisticsData.total || 0 },
              { name: '正常', count: statisticsData.normal || 0 },
              { name: '冻结', count: statisticsData.frozen || 0 }
            ];
          } else {
            // 如果没有统计数据，使用员工列表长度作为总数
            this.statistics = {
              total: totalCount,
              active: 0,
              frozen: 0,
              online: 0
            };
            this.tabs = [
              { name: '全部', count: totalCount },
              { name: '正常', count: 0 },
              { name: '冻结', count: 0 }
            ];
          }

        } else {
          uni.showToast({
            title: '获取员工列表失败',
            icon: 'none'
          });
        }

      } catch (error) {
        console.error('加载员工列表失败:', error);
        uni.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.refreshing = false;
        this.loadingMore = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
// 页面容器 - 统一使用dispatch风格
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 头部背景区域 - 统一使用dispatch风格
.header-background-section {
  flex-shrink: 0;
  position: relative;
  padding-bottom: 20rpx;
  margin-bottom: 0;
  // 添加状态栏安全区域
  padding-top: calc(var(--status-bar-height) + 20rpx);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 20rpx;
  }
}

// 顶部导航区域 - 统一使用dispatch风格
.header-section {
  padding-top: 0;
}

.navbar {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  position: relative;
}

.nav-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  width: 100%;
}


// 标签栏 - 统一使用dispatch风格
.tab-section {
  position: relative;
  width: 100%;
}

.tab-container {
  display: flex;
  background-color: transparent;
  padding: 20rpx 0;
  width: 100%;

  .tab-wrapper {
    display: flex;
    width: 100%;
    justify-content: space-evenly;
    padding: 0 20rpx;
  }

  .tab-item {
    position: relative;
    padding: 20rpx 0;
    flex: 1;
    text-align: center;

    .tab-text {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 500;
    }

    &.active .tab-text {
      color: #ffffff;
      font-weight: 600;
    }

    .tab-line {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 8rpx;
      background-color: #ffffff;
      border-radius: 6rpx;
    }
  }

  &::-webkit-scrollbar {
    display: none;
  }
}

// 内容区域 - 统一使用dispatch风格
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; // 允许 flex 子元素收缩
}

// 搜索筛选容器 - 统一使用dispatch风格
.search-filter-container {
  flex-shrink: 0; // 不参与 flex 压缩
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
}

.search-box {
  margin-bottom: 20rpx;

  .search-input {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 36rpx;
    padding: 10rpx 20rpx;
    transition: all 0.3s;

    &:focus-within {
      background-color: #fff;
      box-shadow: 0 0 0 2rpx #fdd118;
    }

    input {
      flex: 1;
      height: 60rpx;
      margin-left: 10rpx;
      font-size: 28rpx;
    }
  }

  .search-clear {
    width: 40rpx;
    height: 40rpx;
    border-radius: 20rpx;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:active {
      background: #e0e0e0;
      transform: scale(0.9);
    }
  }
}

// 筛选扩展区域
.filter-extension {
  .horizontal-status-filter {
    .status-scroll {
      white-space: nowrap;

      .status-scroll-content {
        display: inline-flex;
        padding: 0 10rpx;
        min-width: 100%;
        gap: 20rpx;
      }

      .status-filter-item {
        flex-shrink: 0;
        padding: 16rpx 32rpx;
        background: #f5f5f5;
        border-radius: 50rpx;
        border: 2rpx solid transparent;
        transition: all 0.3s ease;
        white-space: nowrap;

        &.active {
          background: rgba(253, 209, 24, 0.1);
          border-color: #fdd118;

          text {
            color: #fdd118;
            font-weight: 600;
          }
        }

        text {
          font-size: 26rpx;
          color: #666;
        }
      }

      &::-webkit-scrollbar {
        display: none;
      }
    }
  }
}

// 员工列表容器
.staff-list-container {
  flex: 1;
  padding: 0 20rpx;
  // 使用动态计算的高度，同时保持 flex 布局
}

// 员工列表
.staff-list {
  padding-bottom: 40rpx;
}

.staff-item {
  background: #fff;
  border-radius: 32rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid #f0f0f0;
  padding: 60rpx 50rpx; // 增大内边距
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  min-height: 300rpx; // 设置最小高度

  &:active {
    background: #fafafa;
    transform: scale(0.995);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  }
}

// 员工信息
.staff-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx; // 增大间距
}

.avatar {
  width: 120rpx; // 增大头像尺寸
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx; // 增大间距
  background: #f5f5f5;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
  min-width: 0; // 防止flex子元素溢出
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.name-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.staff-name {
  font-size: 36rpx; // 增大字体
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-badge {
  font-size: 24rpx; // 增大字体
  padding: 8rpx 16rpx; // 增大内边距
  border-radius: 24rpx;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;

  &.normal {
    background: #e8f5e8;
    color: #09be89;
  }

  &.frozen {
    background: #fff0f0;
    color: #ff403f;
  }

  &.offline {
    background: #f0f0f0;
    color: #999999;
  }

  &.resigned {
    background: #f5f5f5;
    color: #666666;
  }

  &.blacklisted {
    background: #2a2a2a;
    color: #ffffff;
  }
}

.staff-source {
  flex-shrink: 0;
}

.source-text {
  font-size: 12px;
  color: #999;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 8px;
}

.staff-contact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx; // 增大间距
  gap: 20rpx; // 增大间距
}

.staff-phone {
  font-size: 28rpx; // 增大字体
  color: #666;
  flex-shrink: 0;
}

.join-time {
  font-size: 24rpx; // 增大字体
  color: #999;
  white-space: nowrap;
}

.staff-location {
  margin-bottom: 12rpx; // 增大间距
}

.location-text {
  font-size: 26rpx; // 增大字体
  color: #666;
  line-height: 1.4;
}

.staff-skills {
  display: flex;
  align-items: center;
  gap: 12rpx; // 增大间距
  margin-bottom: 16rpx; // 增大间距
}

.skills-label {
  font-size: 24rpx; // 增大字体
  color: #999;
  flex-shrink: 0;
}

.skills-content {
  font-size: 24rpx; // 增大字体
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 员工统计信息
.staff-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx; // 增大间距
  padding: 24rpx 0; // 增大内边距
  border-top: 1px solid rgba(253, 209, 24, 0.1);
  border-bottom: 1px solid rgba(253, 209, 24, 0.1);
}

.stat-item {
  flex: 1;
  text-align: center;

  .label {
    display: block;
    font-size: 24rpx; // 增大字体
    color: #999;
    margin-bottom: 8rpx; // 增大间距
  }

  .value {
    display: block;
    font-size: 32rpx; // 增大字体
    font-weight: 600;
    color: #333;
  }
}

// 操作按钮
.staff-actions {
  display: flex;
  gap: 20rpx; // 增大间距
  justify-content: space-between;
}

.action-btn {
  flex: 1;
  height: 72rpx; // 增大高度
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx; // 增大字体
  font-weight: 500;
  transition: all 0.3s ease;

  &.secondary-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(253, 209, 24, 0.3);

    text {
      color: #fdd118;
    }

    &:active {
      background: rgba(253, 209, 24, 0.1);
      transform: scale(0.98);
    }
  }

  &.primary-btn {
    background: linear-gradient(135deg, #fdd118, #ff801c);
    border: 1px solid #fdd118;

    text {
      color: #ffffff;
      font-weight: 600;
    }

    &:active {
      background: linear-gradient(135deg, #e6c015, #e6721a);
      transform: scale(0.98);
    }
  }
}

// 浮动按钮容器 - 统一使用dispatch风格
.floating-btn-container {
  position: fixed;
  bottom: 30px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
}

// 浮动按钮 - 统一使用dispatch风格
.order-floating-btn {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(253, 209, 24, 0.4);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #e6c015 0%, #e6721a 100%);
    box-shadow: 0 4px 16px rgba(253, 209, 24, 0.5);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(253, 209, 24, 0.5);
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 50rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 30rpx;
}

// 加载状态
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  padding: 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

// 筛选弹窗
.filter-modal {
  background: #fff;
  border-radius: 15px 15px 0 0;
  max-height: 70vh;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.filter-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.filter-reset {
  font-size: 14px;
  color: #fdd118;
  font-weight: 500;
}

.filter-content {
  padding: 20px;
  max-height: 50vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 30px;
}

.filter-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-option {
  background: #f5f5f5;
  color: #666;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 15px;
  border: 1px solid #eee;

  &.active {
    background: #fdd118;
    color: #fff;
    border-color: #fdd118;
  }
}

.filter-footer {
  display: flex;
  padding: 20px;
  border-top: 1px solid #eee;
  gap: 10px;
}
</style>

