from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from exceptions.exception import QueryException, DatabaseException
from utils.log_util import logger
import uuid


class AddAccountDao:
    """加账号数据访问层"""

    @staticmethod
    async def get_company_account_count(db: AsyncSession, company_uuid: str) -> Dict[str, int]:
        """
        获取公司账号数量统计

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 账号数量统计
        """
        try:
            query = text("""
                SELECT
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN paid_expire_time IS NOT NULL THEN 1 END) as paid_count,
                    COUNT(CASE WHEN paid_expire_time IS NULL THEN 1 END) as free_count
                FROM internal_user
                WHERE company_id = :company_uuid
                AND status = '1'
            """)

            result = await db.execute(query, {"company_uuid": company_uuid})
            row = result.fetchone()

            if row:
                return {
                    "total_count": row.total_count or 0,
                    "paid_count": row.paid_count or 0,
                    "free_count": row.free_count or 0
                }
            else:
                return {"total_count": 0, "paid_count": 0, "free_count": 0}

        except Exception as e:
            logger.error(f"获取公司账号数量失败: {str(e)}")
            raise QueryException(message=f"获取公司账号数量失败: {str(e)}")

    @staticmethod
    async def check_account_exists(db: AsyncSession, account_list: List[Dict[str, str]]) -> Dict[str, List[str]]:
        """
        检查账号信息是否已存在

        :param db: 数据库会话
        :param account_list: 账号列表，包含name和mobile字段
        :return: 已存在的手机号和姓名列表
        """
        try:
            if not account_list:
                return {"existing_phones": [], "existing_names": []}

            phone_list = [acc["mobile"] for acc in account_list]
            name_list = [acc["name"] for acc in account_list]

            # 检查手机号是否存在
            phone_placeholders = ','.join([f':phone_{i}' for i in range(len(phone_list))])
            phone_query = text(f"""
                SELECT mobile
                FROM internal_user
                WHERE mobile IN ({phone_placeholders})
            """)

            phone_params = {f'phone_{i}': phone for i, phone in enumerate(phone_list)}
            phone_result = await db.execute(phone_query, phone_params)
            existing_phones = [row.mobile for row in phone_result.fetchall()]

            # 检查姓名是否存在（在同一公司内）
            name_placeholders = ','.join([f':name_{i}' for i in range(len(name_list))])
            name_query = text(f"""
                SELECT name
                FROM internal_user
                WHERE name IN ({name_placeholders})
            """)

            name_params = {f'name_{i}': name for i, name in enumerate(name_list)}
            name_result = await db.execute(name_query, name_params)
            existing_names = [row.name for row in name_result.fetchall()]

            return {
                "existing_phones": existing_phones,
                "existing_names": existing_names
            }

        except Exception as e:
            logger.error(f"检查账号信息是否存在失败: {str(e)}")
            raise QueryException(message=f"检查账号信息是否存在失败: {str(e)}")

    @staticmethod
    async def create_accounts_batch(
        db: AsyncSession,
        company_uuid: str,
        company_name: str,
        store_id: str,
        store_uuid: str,
        store_name: str,
        account_list: List[Dict[str, str]],
        expire_time: datetime,
        operator_id: str
    ) -> List[Dict[str, Any]]:
        """
        批量创建账号

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :param company_name: 公司名称
        :param store_id: 门店ID
        :param store_uuid: 门店UUID
        :param store_name: 门店名称
        :param account_list: 账号列表，包含name和mobile字段
        :param expire_time: 有效期
        :param operator_id: 操作人ID
        :return: 创建的账号信息列表
        """
        try:
            created_accounts = []

            for account in account_list:
                # 生成账号UUID
                account_uuid = uuid.uuid4().hex.replace('-', '')

                name = account["name"]
                mobile = account["mobile"]

                # 生成默认密码（可以后续修改）
                import bcrypt
                default_password = "123456"
                hashed_password = bcrypt.hashpw(default_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

                # 插入用户记录（按照文档要求的字段）
                insert_query = text("""
                    INSERT INTO internal_user (
                        uuid, mobile, password, name, sex, sex_name,
                        company_id, company_name, store_id, store_uuid, store_name,
                        role_id, role_name, sub_role_id, status, mobile_system,
                        service_count, is_bind_wx, paid_expire_time,
                        create_time, update_time
                    ) VALUES (
                        :uuid, :mobile, :password, :name, '0', '女',
                        :company_id, :company_name, :store_id, :store_uuid, :store_name,
                        '2', '员工', '0', '1', 0,
                        0, '0', :paid_expire_time,
                        NOW(), NOW()
                    )
                """)

                await db.execute(insert_query, {
                    "uuid": account_uuid,
                    "mobile": mobile,
                    "password": hashed_password,
                    "name": name,
                    "company_id": company_uuid,
                    "company_name": company_name,
                    "store_id": store_id,
                    "store_uuid": store_uuid,
                    "store_name": store_name,
                    "paid_expire_time": expire_time
                })

                # 获取刚插入的用户ID
                user_id_query = text("""
                    SELECT id FROM internal_user WHERE uuid = :uuid
                """)
                user_result = await db.execute(user_id_query, {"uuid": account_uuid})
                user_row = user_result.fetchone()
                user_id = user_row.id

                # 插入用户权限记录（按照文档要求的字段）
                permission_insert_query = text("""
                    INSERT INTO internal_user_permission (
                        user_id, assistant, is_private, is_listen_tape, auth_pay, is_lock,
                        store_balance_auth, is_collection_salary, qrcode_status, is_view_other_resume,
                        is_edit_other_resume, is_training_course_push, is_use_star_clue,
                        is_use_sign_electronic_contract, is_edit_contract_content, is_delete_signed_contract,
                        is_add_income_type, is_view_store_contract, is_use_insurance, is_use_change_insurance,
                        is_use_balance_insurance, is_use_physical_examination, is_use_balance_examination,
                        is_view_examination_income, is_use_certificate, is_view_certificate_price,
                        is_use_balance_certificate, is_use_train, is_open_record_course, is_add_record_course,
                        is_give_record_course, is_adjust_course_price, is_can_record_earnings,
                        is_can_course_setting, is_control_record_cost_price, is_ai_red_envelope,
                        is_can_earnings_manage, is_allow_update_password, is_use_combined_order,
                        is_servicer, card_type, is_can_customize_record, is_can_shop,
                        is_show_increment_coupon, is_view_store_fund, is_edit_website, is_use_college,
                        is_use_combined_nearby, is_edit_gaode_product, is_show_bj, is_show_bj_wxapp,
                        is_export_bj_order, is_view_home_module, is_release_job,
                        create_time, update_time
                    ) VALUES (
                        :user_id, 0, 0, 0, 1, 0,
                        1, 1, 0, 1,
                        1, 2, 1,
                        1, 1, 0,
                        1, 1, 1, 1,
                        1, 1, 1,
                        0, 1, 1,
                        1, 0, 1, 1,
                        1, 1, 1,
                        1, 1, 1,
                        1, 1, 1,
                        0, 1, 0, 1,
                        1, 1, 1, 1,
                        1, 1, 1, 1,
                        0, 1, 1,
                        NOW(), NOW()
                    )
                """)

                await db.execute(permission_insert_query, {
                    "user_id": user_id
                })

                created_accounts.append({
                    "id": user_id,
                    "uuid": account_uuid,
                    "mobile": mobile,
                    "name": name,
                    "paid_expire_time": expire_time.strftime("%Y-%m-%d %H:%M:%S")
                })

            return created_accounts

        except Exception as e:
            logger.error(f"批量创建账号失败: {str(e)}")
            raise DatabaseException(message=f"批量创建账号失败: {str(e)}")

    @staticmethod
    async def get_company_account_list(
        db: AsyncSession,
        company_uuid: str,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """
        获取公司账号列表

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :param page: 页码
        :param size: 每页数量
        :return: 账号列表数据
        """
        try:
            # 计算偏移量
            offset = (page - 1) * size

            # 查询账号列表
            query = text("""
                SELECT
                    uuid, mobile, name, role_name, status_name,
                    paid_expire_time, create_time
                FROM internal_user
                WHERE company_id = :company_uuid
                AND status = '1'
                ORDER BY create_time DESC
                LIMIT :size OFFSET :offset
            """)

            result = await db.execute(query, {
                "company_uuid": company_uuid,
                "size": size,
                "offset": offset
            })
            rows = result.fetchall()

            # 查询总数
            count_query = text("""
                SELECT COUNT(*) as total
                FROM internal_user
                WHERE company_id = :company_uuid
                AND status = '1'
            """)

            count_result = await db.execute(count_query, {"company_uuid": company_uuid})
            total = count_result.fetchone().total

            # 处理结果
            account_list = []
            for row in rows:
                account_info = {
                    "uuid": row.uuid,
                    "mobile": row.mobile,
                    "name": row.name,
                    "role_name": row.role_name,
                    "status_name": row.status_name,
                    "paid_expire_time": row.paid_expire_time.strftime("%Y-%m-%d %H:%M:%S") if row.paid_expire_time else None,
                    "create_time": row.create_time.strftime("%Y-%m-%d %H:%M:%S") if row.create_time else None
                }
                account_list.append(account_info)

            return {
                "account_list": account_list,
                "total": total,
                "page": page,
                "size": size
            }

        except Exception as e:
            logger.error(f"获取公司账号列表失败: {str(e)}")
            raise QueryException(message=f"获取公司账号列表失败: {str(e)}")

    @staticmethod
    async def record_add_account_transaction(
        db: AsyncSession,
        transaction_no: str,
        company_uuid: str,
        operator_id: str,
        operator_name: str,
        account_list: List[Dict[str, str]],
        total_amount: float,
        balance_before: float,
        balance_after: float
    ) -> bool:
        """
        记录加账号交易流水

        :param db: 数据库会话
        :param transaction_no: 交易流水号
        :param company_uuid: 公司UUID
        :param operator_id: 操作人ID
        :param operator_name: 操作人姓名
        :param account_list: 账号列表
        :param total_amount: 总金额
        :param balance_before: 扣费前余额
        :param balance_after: 扣费后余额
        :return: 是否记录成功
        """
        try:
            # 按照真实交易流水表结构记录
            description = f"为{len(account_list)}个用户创建账号: {', '.join([acc['name'] for acc in account_list])}"

            insert_query = text("""
                INSERT INTO company_transaction (
                    company_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    pay_type, operator_id, operator_name, description,
                    transaction_time, created_by, created_at, transaction_status
                ) VALUES (
                    :company_uuid, :transaction_no, 'ADD_ACCOUNT', '加账号服务',
                    2, :amount, :balance_before, :balance_after,
                    'BALANCE', :operator_id, :operator_name, :description,
                    NOW(), :created_by, NOW(), 'SUCCESS'
                )
            """)

            await db.execute(insert_query, {
                "company_uuid": company_uuid,
                "transaction_no": transaction_no,
                "amount": total_amount,
                "balance_before": balance_before,
                "balance_after": balance_after,
                "operator_id": operator_id,
                "operator_name": operator_name,
                "description": description,
                "created_by": operator_name
            })

            return True

        except Exception as e:
            logger.error(f"记录加账号交易流水失败: {str(e)}")
            # 这里不抛出异常，因为流水记录失败不应该影响主流程
            return False
