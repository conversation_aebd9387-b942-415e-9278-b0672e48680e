<template>
  <view class="staff-detail-page">
    <appHead left fixed title="员工详情"></appHead>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="tc lh100 c9">加载中...</view>
    
    <!-- 员工详情内容 -->
    <view v-else-if="staffInfo" class="detail-content">
      <!-- 员工基本信息 -->
      <view class="info-section">
        <view class="section-title">
          基本信息
          <view v-if="canEdit" class="edit-toggle" @click="toggleEdit">
            <uni-icons :type="isEditing ? 'checkmarkempty' : 'compose'" size="16" :color="isEditing ? '#07c160' : '#007aff'" />
            <text>{{ isEditing ? '保存' : '编辑' }}</text>
          </view>
        </view>

        <!-- 姓名 -->
        <view class="info-item">
          <text class="label">姓名</text>
          <input
            v-if="isEditing"
            class="edit-input"
            v-model="editForm.name"
            placeholder="请输入姓名"
            maxlength="20"
          />
          <text v-else class="value">{{ staffInfo.name }}</text>
        </view>

        <!-- 状态 -->
        <view class="info-item">
          <text class="label">状态</text>
          <picker
            v-if="isEditing"
            @change="onStatusChange"
            :value="statusIndex"
            :range="statusList"
            range-key="name"
          >
            <view class="edit-picker">
              {{ getStatusText(editForm.status) }}
              <uni-icons type="arrowright" size="14" color="#999" />
            </view>
          </picker>
          <view v-else class="status-badge" :class="statusClass">
            {{ statusText }}
          </view>
        </view>

        <!-- 角色 -->
        <view class="info-item">
          <text class="label">角色</text>
          <picker
            v-if="isEditing"
            @change="onRoleChange"
            :value="roleIndex"
            :range="roleList"
            range-key="name"
          >
            <view class="edit-picker">
              {{ editForm.role_name || '请选择角色' }}
              <uni-icons type="arrowright" size="14" color="#999" />
            </view>
          </picker>
          <text v-else class="value">{{ staffInfo.role_name || '未设置角色' }}</text>
        </view>

        <!-- 职位 -->
        <view class="info-item" v-if="staffInfo.title || isEditing">
          <text class="label">职位</text>
          <input
            v-if="isEditing"
            class="edit-input"
            v-model="editForm.title"
            placeholder="请输入职位"
            maxlength="30"
          />
          <text v-else class="value">{{ staffInfo.title }}</text>
        </view>

        <!-- 手机号码 -->
        <view class="info-item">
          <text class="label">手机号码</text>
          <view class="phone-row">
            <input
              v-if="isEditing"
              class="edit-input phone-input"
              v-model="editForm.mobile"
              placeholder="请输入手机号"
              type="number"
              maxlength="11"
            />
            <text v-else class="value">{{ staffInfo.mobile || '未填写' }}</text>
            <view class="action-btn phone-btn" @click="makePhoneCall" v-if="!isEditing && staffInfo.mobile">
              <uni-icons type="phone" size="14" color="#fff" />
              <text>拨打</text>
            </view>
          </view>
        </view>

        <!-- 员工编号 -->
        <view class="info-item" v-if="staffInfo.number">
          <text class="label">员工编号</text>
          <text class="value">{{ staffInfo.number }}</text>
        </view>
      </view>



      <!-- 工作信息 -->
      <view class="info-section">
        <view class="section-title">工作信息</view>
        <view class="info-item">
          <text class="label">所属公司</text>
          <text class="value">{{ staffInfo.company_name || '未分配' }}</text>
        </view>
        <view class="info-item">
          <text class="label">所属门店</text>
          <text class="value">{{ staffInfo.store_name || '未分配' }}</text>
        </view>
        <view class="info-item">
          <text class="label">员工角色</text>
          <text class="value">{{ staffInfo.role_name || '未设置' }}</text>
        </view>
        <view class="info-item" v-if="staffInfo.title">
          <text class="label">职位名称</text>
          <text class="value">{{ staffInfo.title }}</text>
        </view>
      </view>

      <!-- 个人信息 -->
      <view class="info-section">
        <view class="section-title">个人信息</view>
        <view class="info-item">
          <text class="label">性别</text>
          <text class="value">{{ staffInfo.sex_name || '未填写' }}</text>
        </view>
        <view class="info-item" v-if="staffInfo.birthday">
          <text class="label">生日</text>
          <text class="value">{{ staffInfo.birthday }}</text>
        </view>
        <view class="info-item" v-if="staffInfo.age">
          <text class="label">年龄</text>
          <text class="value">{{ staffInfo.age }}岁</text>
        </view>
        <view class="info-item" v-if="staffInfo.id_number">
          <text class="label">身份证号</text>
          <text class="value">{{ formatIdNumber(staffInfo.id_number) }}</text>
        </view>
      </view>

      <!-- 微信绑定状态 -->
      <view class="info-section">
        <view class="section-title">微信绑定</view>
        <view class="wechat-bind-info">
          <view class="bind-status" :class="{ 'bound': isWechatBound }">
            <uni-icons
              :type="isWechatBound ? 'checkmarkempty' : 'closeempty'"
              :color="isWechatBound ? '#07c160' : '#999'"
              size="16"
            />
            <text>{{ isWechatBound ? '已绑定微信' : '未绑定微信' }}</text>
          </view>
          <view v-if="isWechatBound && staffInfo.wx_official_nickname" class="wx-info">
            <text class="f26 c9">微信昵称：{{ staffInfo.wx_official_nickname }}</text>
          </view>
        </view>
      </view>

      <!-- 登录信息 -->
      <view class="info-section">
        <view class="section-title">登录信息</view>
        <view class="info-item">
          <text class="label">最后登录时间</text>
          <text class="value">{{ formatDateTime(staffInfo.last_login_time) || '从未登录' }}</text>
        </view>
        <view class="info-item" v-if="staffInfo.service_count !== undefined">
          <text class="label">服务次数</text>
          <text class="value">{{ staffInfo.service_count }}次</text>
        </view>
      </view>

      <!-- 地址信息 -->
      <view class="info-section" v-if="staffInfo.city || staffInfo.address">
        <view class="section-title">地址信息</view>
        <view class="info-item" v-if="staffInfo.city">
          <text class="label">所在城市</text>
          <text class="value">{{ staffInfo.city }}</text>
        </view>
        <view class="info-item" v-if="staffInfo.address">
          <text class="label">常住地址</text>
          <text class="value">{{ staffInfo.address }}</text>
        </view>
        <view class="info-item" v-if="staffInfo.address_desc">
          <text class="label">地址描述</text>
          <text class="value">{{ staffInfo.address_desc }}</text>
        </view>
      </view>

      <!-- 备注信息 -->
      <view class="info-section" v-if="staffInfo.remark">
        <view class="section-title">备注信息</view>
        <view class="remark-content">
          <text class="f28 c3">{{ staffInfo.remark }}</text>
        </view>
      </view>

      <!-- 时间信息 -->
      <view class="info-section">
        <view class="section-title">时间信息</view>
        <view class="info-item">
          <text class="label">创建时间</text>
          <text class="value">{{ formatDateTime(staffInfo.create_time) }}</text>
        </view>
        <view class="info-item">
          <text class="label">更新时间</text>
          <text class="value">{{ formatDateTime(staffInfo.update_time) }}</text>
        </view>
        <view class="info-item" v-if="staffInfo.paid_expire_time">
          <text class="label">付费到期时间</text>
          <text class="value">{{ formatDateTime(staffInfo.paid_expire_time) }}</text>
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="tc lh100 c9">
      <text>加载员工信息失败</text>
      <view class="retry-btn" @click="loadStaffDetail">重新加载</view>
    </view>


  </view>
</template>

<script>
import { getInternalUserDetail, updateInternalUser, getAvailableRoles } from '../../api/staff.js'

export default {
  data() {
    return {
      staffId: '',
      staffInfo: null,
      loading: false,
      canEdit: false, // 是否可以编辑（超级管理员权限）
      isEditing: false, // 是否处于编辑状态
      saving: false, // 是否正在保存
      editForm: {
        id: '',
        name: '',
        mobile: '',
        role_name: '',
        role_id: '',
        status: 1,
        title: ''
      },
      roleList: [], // 动态从API获取角色列表
      statusList: [
        { value: 1, name: '正常' },
        { value: 2, name: '冻结' },
        { value: 3, name: '离职' }
      ],
      roleIndex: 0,
      statusIndex: 0
    }
  },
  computed: {
    safeBottom() {
      return this.getAppData('safeBottom');
    },

    // 判断是否绑定微信（只有wx_official_openid存在值时才算绑定）
    isWechatBound() {
      return this.staffInfo && this.staffInfo.wx_official_openid && this.staffInfo.wx_official_openid.trim() !== '';
    },

    // 状态样式类
    statusClass() {
      if (!this.staffInfo || !this.staffInfo.status) return 'status-unknown';
      const statusMap = {
        1: 'status-normal',
        2: 'status-frozen',
        3: 'status-resigned',
        '1': 'status-normal',
        '2': 'status-frozen',
        '3': 'status-resigned'
      };
      return statusMap[this.staffInfo.status] || 'status-unknown';
    },

    // 状态文本
    statusText() {
      if (!this.staffInfo || !this.staffInfo.status) return '未知';
      const statusMap = {
        1: '正常',
        2: '冻结',
        3: '离职',
        '1': '正常',
        '2': '冻结',
        '3': '离职'
      };
      return statusMap[this.staffInfo.status] || '未知';
    }
  },
  onLoad(options) {
    if (options.id) {
      this.staffId = options.id;
      // 接收权限参数
      this.canEdit = options.canEdit === '1';
      console.log('员工详情页权限:', this.canEdit ? '可编辑' : '只读');

      // 如果有编辑权限，加载角色列表
      if (this.canEdit) {
        this.loadRoleList();
      }

      this.loadStaffDetail();
    } else {
      uni.showToast({
        title: '缺少员工ID参数',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 加载角色列表
    async loadRoleList() {
      try {
        // 从后端API获取可用角色列表（排除超级管理员，只返回状态正常的角色）
        const roles = await getAvailableRoles();
        if (roles && Array.isArray(roles)) {
          this.roleList = roles;
          console.log('角色列表加载成功（仅状态正常的角色）:', this.roleList);
        } else {
          throw new Error('角色列表格式错误');
        }
      } catch (error) {
        console.error('加载角色列表失败:', error);
        // 如果出错，使用默认角色列表
        this.roleList = [
          { id: '4', name: '员工' },
          { id: '3', name: '店长' }
        ];
      }
    },

    // 加载员工详情
    async loadStaffDetail() {
      if (!this.staffId) return;
      
      this.loading = true;
      try {
        const result = await getInternalUserDetail(this.staffId);
        if (result) {
          // API直接返回员工数据对象
          this.staffInfo = result;
        } else {
          throw new Error('获取员工信息失败');
        }
      } catch (error) {
        console.error('加载员工详情失败:', error);
        uni.showToast({
          title: '加载员工信息失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 拨打电话
    makePhoneCall() {
      if (!this.staffInfo || !this.staffInfo.mobile) {
        uni.showToast({
          title: '该员工未填写手机号',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: this.staffInfo.mobile,
        fail: (err) => {
          console.error('拨打电话失败:', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },

    // 切换编辑状态
    toggleEdit() {
      if (!this.canEdit) {
        uni.showToast({
          title: '您没有编辑权限',
          icon: 'none'
        });
        return;
      }

      if (this.isEditing) {
        // 保存编辑
        this.saveStaffInfo();
      } else {
        // 进入编辑状态
        this.enterEditMode();
      }
    },

    // 进入编辑模式
    enterEditMode() {
      this.isEditing = true;
      // 初始化编辑表单数据
      this.editForm = {
        id: this.staffInfo.id,
        name: this.staffInfo.name || '',
        mobile: this.staffInfo.mobile || '',
        role_name: this.staffInfo.role_name || '',
        role_id: this.staffInfo.role_id || this.findRoleIdByName(this.staffInfo.role_name),
        status: this.staffInfo.status || 1,
        title: this.staffInfo.title || ''
      };

      console.log('进入编辑模式，表单数据:', this.editForm);

      // 设置选择器索引
      this.updatePickerIndexes();
    },

    // 根据角色名称查找角色ID
    findRoleIdByName(roleName) {
      if (!roleName || !this.roleList.length) return '';
      const role = this.roleList.find(r => r.name === roleName);
      return role ? role.id : '';
    },

    // 更新选择器索引
    updatePickerIndexes() {
      // 设置角色选择器索引
      const roleIndex = this.roleList.findIndex(role => role.name === this.editForm.role_name);
      this.roleIndex = roleIndex >= 0 ? roleIndex : 0;

      // 设置状态选择器索引
      const statusIndex = this.statusList.findIndex(status => status.value === this.editForm.status);
      this.statusIndex = statusIndex >= 0 ? statusIndex : 0;
    },

    // 角色选择变化
    onRoleChange(e) {
      const index = e.detail.value;
      this.roleIndex = index;
      const selectedRole = this.roleList[index];
      this.editForm.role_name = selectedRole.name;
      this.editForm.role_id = selectedRole.id;
    },

    // 状态选择变化
    onStatusChange(e) {
      const index = e.detail.value;
      this.statusIndex = index;
      const selectedStatus = this.statusList[index];
      this.editForm.status = selectedStatus.value;
    },

    // 验证表单
    validateForm() {
      if (!this.editForm.name.trim()) {
        uni.showToast({
          title: '请输入姓名',
          icon: 'none'
        });
        return false;
      }

      if (!this.editForm.mobile.trim()) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        });
        return false;
      }

      // 验证手机号格式
      const mobileReg = /^1[3-9]\d{9}$/;
      if (!mobileReg.test(this.editForm.mobile)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return false;
      }

      if (!this.editForm.role_name) {
        uni.showToast({
          title: '请选择角色',
          icon: 'none'
        });
        return false;
      }

      return true;
    },

    // 保存员工信息
    async saveStaffInfo() {
      if (!this.validateForm()) {
        return;
      }

      this.saving = true;
      try {
        const updateData = {
          id: String(this.editForm.id), // 转换为字符串
          name: this.editForm.name.trim(),
          mobile: this.editForm.mobile.trim(),
          role_id: String(this.editForm.role_id), // 转换为字符串
          status: String(this.editForm.status), // 转换为字符串
          company_id: this.staffInfo.company_id || '', // 添加必需字段
          store_id: this.staffInfo.store_id || '' // 添加必需字段
        };

        // 只有当title有值时才传递
        if (this.editForm.title && this.editForm.title.trim()) {
          updateData.title = this.editForm.title.trim();
        }

        console.log('保存员工信息请求数据:', updateData);

        const result = await updateInternalUser(updateData);

        if (result) {
          // 更新本地数据
          this.staffInfo = {
            ...this.staffInfo,
            name: updateData.name,
            mobile: updateData.mobile,
            status: parseInt(updateData.status), // 本地保持数字类型用于显示
            role_name: this.editForm.role_name,
            role_id: updateData.role_id,
            title: updateData.title || this.staffInfo.title
          };

          this.isEditing = false;
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
        }
      } catch (error) {
        console.error('保存员工信息失败:', error);
        uni.showToast({
          title: error.msg || error.message || '保存失败',
          icon: 'none'
        });
      } finally {
        this.saving = false;
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '正常',
        2: '冻结',
        3: '离职',
        '1': '正常',
        '2': '冻结',
        '3': '离职'
      };
      return statusMap[status] || '正常';
    },

    // 编辑员工信息（保留原方法，用于底部按钮）
    editStaff() {
      this.toggleEdit();
    },



    // 格式化身份证号（隐藏中间部分）
    formatIdNumber(idNumber) {
      if (!idNumber || idNumber.length < 8) return idNumber;
      return idNumber.substring(0, 4) + '****' + idNumber.substring(idNumber.length - 4);
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '';
      const date = new Date(dateTime);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.staff-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.detail-content {
  padding: 20rpx;
}

// 手机号行样式
.phone-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

// 编辑输入框样式
.edit-input {
  flex: 1;
  height: 60rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  color: #333;

  &:focus {
    border-color: #007aff;
  }
}

.phone-input {
  margin-right: 20rpx;
}

// 编辑选择器样式
.edit-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: #fff;
  color: #333;
  width: 100%;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;

  &.phone-btn {
    background: linear-gradient(135deg, #07c160 0%, #05a050 100%);
    color: #fff;
  }
}

// 状态标签样式
.status-badge {
  font-size: 24rpx;
  padding: 0 20rpx;
  line-height: 40rpx;
  border-radius: 20rpx;

  &.status-normal {
    color: #07c160;
    background: rgba(7, 193, 96, 0.1);
  }

  &.status-frozen {
    color: #ff9500;
    background: rgba(255, 149, 0, 0.1);
  }

  &.status-resigned {
    color: #999;
    background: rgba(153, 153, 153, 0.1);
  }

  &.status-unknown {
    color: #666;
    background: rgba(102, 102, 102, 0.1);
  }
}

// 信息区块
.info-section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.edit-toggle {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #007aff;
  font-weight: normal;

  text {
    margin-left: 8rpx;
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;

  &:last-child {
    border-bottom: none;
  }

  .label {
    font-size: 28rpx;
    color: #666;
    min-width: 200rpx;
    flex-shrink: 0;
  }

  .value {
    font-size: 28rpx;
    color: #333;
    text-align: right;
    flex: 1;
    word-break: break-all;
  }
}

// 微信绑定信息
.wechat-bind-info {
  .bind-status {
    display: flex;
    align-items: center;
    gap: 10rpx;
    font-size: 28rpx;
    color: #999;

    &.bound {
      color: #07c160;
    }
  }

  .wx-info {
    margin-top: 20rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid #f0f0f0;
  }
}

// 备注内容
.remark-content {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
  line-height: 1.6;
}

// 重试按钮
.retry-btn {
  margin-top: 30rpx;
  padding: 20rpx 40rpx;
  background: #fdd118;
  color: #333;
  border-radius: 25rpx;
  font-size: 28rpx;
  display: inline-block;
}

// 底部操作按钮
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

  .edit-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}
</style>
