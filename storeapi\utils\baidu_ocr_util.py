"""
百度OCR识别工具类
"""
import os
import base64
import tempfile
import httpx
from aip import AipOcr
from fastapi import UploadFile
from utils.log_util import logger
from config.env import BaiduOcrConfig


class BaiduOCRUtil:
    """百度OCR识别工具类"""
    
    def __init__(self):
        """初始化百度OCR客户端"""
        self.app_id = BaiduOcrConfig.app_id
        self.api_key = BaiduOcrConfig.api_key
        self.secret_key = BaiduOcrConfig.secret_key
        
        if not all([self.app_id, self.api_key, self.secret_key]):
            logger.warning("百度OCR配置不完整，将使用模拟数据")
            self.client = None
        else:
            self.client = AipOcr(self.app_id, self.api_key, self.secret_key)
            logger.info("百度OCR客户端初始化成功")
    
    async def _file_to_base64(self, file: UploadFile) -> str:
        """将上传文件转换为base64编码"""
        try:
            # 读取文件内容
            file_content = await file.read()
            
            # 转换为base64
            base64_data = base64.b64encode(file_content).decode('utf-8')
            
            # 重置文件指针
            await file.seek(0)
            
            return base64_data
        except Exception as e:
            logger.error(f"文件转base64失败: {str(e)}")
            raise Exception(f"文件处理失败: {str(e)}")

    async def _url_to_image_data(self, image_url: str) -> bytes:
        """将图片URL转换为图片数据"""
        try:
            # 下载图片
            async with httpx.AsyncClient() as client:
                response = await client.get(image_url)
                response.raise_for_status()

                # 返回原始图片数据
                return response.content
        except Exception as e:
            logger.error(f"URL下载图片失败: {str(e)}")
            raise Exception(f"图片下载失败: {str(e)}")
    
    async def recognize_id_card_front(self, image_file: UploadFile) -> dict:
        """
        识别身份证正面
        
        :param image_file: 身份证正面图片文件
        :return: 识别结果
        """
        try:
            logger.info("开始识别身份证正面")
            
            if not self.client:
                # 返回模拟数据
                logger.warning("百度OCR未配置，返回模拟数据")
                return {
                    "name": "张三",
                    "id_number": "110101199001011234",
                    "gender": "男",
                    "birthday": "1990-01-01",
                    "address": "北京市东城区某某街道某某号",
                    "province": "北京市",
                    "city": "北京城区",
                    "district": "东城区"
                }
            
            # 将文件转换为base64
            image_base64 = await self._file_to_base64(image_file)
            
            # 调用百度OCR API
            options = {
                "detect_direction": "true",
                "detect_risk": "false"
            }
            
            result = self.client.idcard(image_base64, "front", options)
            
            if 'error_code' in result:
                logger.error(f"百度OCR识别失败: {result}")
                raise Exception(f"OCR识别失败: {result.get('error_msg', '未知错误')}")
            
            # 解析识别结果
            words_result = result.get('words_result', {})
            
            # 提取身份证信息
            name = words_result.get('姓名', {}).get('words', '')
            id_number = words_result.get('公民身份号码', {}).get('words', '')
            gender = words_result.get('性别', {}).get('words', '')
            birthday = words_result.get('出生', {}).get('words', '')
            address = words_result.get('住址', {}).get('words', '')
            
            # 解析省市区信息（从身份证号码前6位解析）
            province, city, district = self._parse_area_from_id(id_number)
            
            # 格式化生日
            if birthday:
                try:
                    # 将20001201格式转换为2000-12-01
                    if len(birthday) == 8:
                        birthday = f"{birthday[:4]}-{birthday[4:6]}-{birthday[6:8]}"
                except:
                    pass
            
            result_data = {
                "name": name,
                "id_number": id_number,
                "gender": gender,
                "birthday": birthday,
                "address": address,
                "province": province,
                "city": city,
                "district": district
            }
            
            logger.info("身份证正面识别成功")
            return result_data

        except Exception as e:
            logger.error(f"身份证正面识别失败: {str(e)}")
            raise Exception(f"身份证识别失败: {str(e)}")

    async def recognize_id_card_front_by_url(self, image_url: str) -> dict:
        """
        通过图片URL识别身份证正面

        :param image_url: 身份证正面图片URL
        :return: 识别结果
        """
        try:
            logger.info("开始通过URL识别身份证正面")

            if not self.client:
                # 返回模拟数据
                logger.warning("百度OCR未配置，返回模拟数据")
                return {
                    "name": "张三",
                    "id_number": "110101199001011234",
                    "gender": "男",
                    "birthday": "1990-01-01",
                    "address": "北京市东城区某某街道某某号",
                    "province": "北京市",
                    "city": "北京城区",
                    "district": "东城区"
                }

            # 将URL转换为图片数据
            image_data = await self._url_to_image_data(image_url)

            # 调用百度OCR API
            options = {
                "detect_direction": "true",
                "detect_risk": "false"
            }

            result = self.client.idcard(image_data, "front", options)

            if 'error_code' in result:
                logger.error(f"百度OCR识别失败: {result}")
                raise Exception(f"OCR识别失败: {result.get('error_msg', '未知错误')}")

            # 解析识别结果
            words_result = result.get('words_result', {})

            # 提取身份证信息
            name = words_result.get('姓名', {}).get('words', '')
            id_number = words_result.get('公民身份号码', {}).get('words', '')
            gender = words_result.get('性别', {}).get('words', '')
            birthday = words_result.get('出生', {}).get('words', '')
            address = words_result.get('住址', {}).get('words', '')

            # 解析省市区信息（从身份证号码前6位解析）
            province, city, district = self._parse_area_from_id(id_number)

            # 格式化生日
            if birthday:
                try:
                    # 将20001201格式转换为2000-12-01
                    if len(birthday) == 8:
                        birthday = f"{birthday[:4]}-{birthday[4:6]}-{birthday[6:8]}"
                except:
                    pass

            result_data = {
                "name": name,
                "id_number": id_number,
                "gender": gender,
                "birthday": birthday,
                "address": address,
                "province": province,
                "city": city,
                "district": district
            }

            logger.info("身份证正面识别成功")
            return result_data

        except Exception as e:
            logger.error(f"身份证正面识别失败: {str(e)}")
            raise Exception(f"身份证识别失败: {str(e)}")

    async def recognize_id_card_back(self, image_file: UploadFile) -> dict:
        """
        识别身份证背面
        
        :param image_file: 身份证背面图片文件
        :return: 识别结果
        """
        try:
            logger.info("开始识别身份证背面")
            
            if not self.client:
                # 返回模拟数据
                logger.warning("百度OCR未配置，返回模拟数据")
                return {
                    "issue_authority": "北京市公安局东城分局",
                    "valid_period": "2010.01.01-2030.01.01"
                }
            
            # 将文件转换为base64
            image_base64 = await self._file_to_base64(image_file)
            
            # 调用百度OCR API
            options = {
                "detect_direction": "true",
                "detect_risk": "false"
            }
            
            result = self.client.idcard(image_base64, "back", options)
            
            if 'error_code' in result:
                logger.error(f"百度OCR识别失败: {result}")
                raise Exception(f"OCR识别失败: {result.get('error_msg', '未知错误')}")
            
            # 解析识别结果
            words_result = result.get('words_result', {})
            
            # 提取身份证背面信息
            issue_authority = words_result.get('签发机关', {}).get('words', '')
            valid_period = words_result.get('签发日期', {}).get('words', '')
            
            result_data = {
                "issue_authority": issue_authority,
                "valid_period": valid_period
            }
            
            logger.info("身份证背面识别成功")
            return result_data

        except Exception as e:
            logger.error(f"身份证背面识别失败: {str(e)}")
            raise Exception(f"身份证识别失败: {str(e)}")

    async def recognize_id_card_back_by_url(self, image_url: str) -> dict:
        """
        通过图片URL识别身份证背面

        :param image_url: 身份证背面图片URL
        :return: 识别结果
        """
        try:
            logger.info("开始通过URL识别身份证背面")

            if not self.client:
                # 返回模拟数据
                logger.warning("百度OCR未配置，返回模拟数据")
                return {
                    "issue_authority": "北京市公安局东城分局",
                    "valid_period": "2010.01.01-2030.01.01"
                }

            # 将URL转换为图片数据
            image_data = await self._url_to_image_data(image_url)

            # 调用百度OCR API
            options = {
                "detect_direction": "true",
                "detect_risk": "false"
            }

            result = self.client.idcard(image_data, "back", options)

            if 'error_code' in result:
                logger.error(f"百度OCR识别失败: {result}")
                raise Exception(f"OCR识别失败: {result.get('error_msg', '未知错误')}")

            # 解析识别结果
            words_result = result.get('words_result', {})

            # 提取身份证背面信息
            issue_authority = words_result.get('签发机关', {}).get('words', '')
            valid_period = words_result.get('签发日期', {}).get('words', '')

            result_data = {
                "issue_authority": issue_authority,
                "valid_period": valid_period
            }

            logger.info("身份证背面识别成功")
            return result_data

        except Exception as e:
            logger.error(f"身份证背面识别失败: {str(e)}")
            raise Exception(f"身份证识别失败: {str(e)}")

    async def recognize_bank_card(self, image_file: UploadFile) -> dict:
        """
        识别银行卡
        
        :param image_file: 银行卡图片文件
        :return: 识别结果
        """
        try:
            logger.info("开始识别银行卡")
            
            if not self.client:
                # 返回模拟数据
                logger.warning("百度OCR未配置，返回模拟数据")
                return {
                    "bank_name": "中国工商银行",
                    "card_number": "****************123",
                    "card_holder": "张三",
                    "card_type": "储蓄卡"
                }
            
            # 将文件转换为base64
            image_base64 = await self._file_to_base64(image_file)
            
            # 调用百度OCR API
            result = self.client.bankcard(image_base64)
            
            if 'error_code' in result:
                logger.error(f"百度OCR识别失败: {result}")
                raise Exception(f"OCR识别失败: {result.get('error_msg', '未知错误')}")
            
            # 解析识别结果
            result_data = result.get('result', {})
            
            # 提取银行卡信息
            bank_name = result_data.get('bank_name', '')
            card_number = result_data.get('bank_card_number', '')
            card_type = result_data.get('bank_card_type', '')
            
            result_info = {
                "bank_name": bank_name,
                "card_number": card_number,
                "card_holder": "",  # 银行卡OCR通常不返回持卡人姓名
                "card_type": card_type
            }
            
            logger.info("银行卡识别成功")
            return result_info

        except Exception as e:
            logger.error(f"银行卡识别失败: {str(e)}")
            raise Exception(f"银行卡识别失败: {str(e)}")

    async def recognize_bank_card_by_url(self, image_url: str) -> dict:
        """
        通过图片URL识别银行卡

        :param image_url: 银行卡图片URL
        :return: 识别结果
        """
        try:
            logger.info("开始通过URL识别银行卡")

            if not self.client:
                # 返回模拟数据
                logger.warning("百度OCR未配置，返回模拟数据")
                return {
                    "bank_name": "中国工商银行",
                    "card_number": "****************",
                    "card_holder": "",
                    "card_type": "储蓄卡"
                }

            # 将URL转换为图片数据
            image_data = await self._url_to_image_data(image_url)

            # 调用百度OCR API
            result = self.client.bankcard(image_data)

            if 'error_code' in result:
                logger.error(f"百度OCR识别失败: {result}")
                raise Exception(f"OCR识别失败: {result.get('error_msg', '未知错误')}")

            # 解析识别结果
            result_data = result.get('result', {})

            # 提取银行卡信息
            bank_name = result_data.get('bank_name', '')
            card_number = result_data.get('bank_card_number', '')
            card_type = result_data.get('bank_card_type', '')

            result_info = {
                "bank_name": bank_name,
                "card_number": card_number,
                "card_holder": "",  # 银行卡OCR通常不返回持卡人姓名
                "card_type": card_type
            }

            logger.info("银行卡识别成功")
            return result_info

        except Exception as e:
            logger.error(f"银行卡识别失败: {str(e)}")
            raise Exception(f"银行卡识别失败: {str(e)}")

    async def recognize_business_license(self, image_file: UploadFile) -> dict:
        """
        识别营业执照
        
        :param image_file: 营业执照图片文件
        :return: 识别结果
        """
        try:
            logger.info("开始识别营业执照")
            
            if not self.client:
                # 返回模拟数据
                logger.warning("百度OCR未配置，返回模拟数据")
                return {
                    "company_name": "北京某某科技有限公司",
                    "legal_person": "张三",
                    "address": "北京市朝阳区某某街道某某号",
                    "register_number": "91110000123456789X",
                    "business_scope": "技术开发、技术咨询、技术服务",
                    "register_date": "2020-01-01",
                    "valid_period": "2020-01-01至2040-01-01"
                }
            
            # 将文件转换为base64
            image_base64 = await self._file_to_base64(image_file)
            
            # 调用百度OCR API
            result = self.client.businessLicense(image_base64)
            
            if 'error_code' in result:
                logger.error(f"百度OCR识别失败: {result}")
                raise Exception(f"OCR识别失败: {result.get('error_msg', '未知错误')}")
            
            # 解析识别结果
            words_result = result.get('words_result', {})
            
            # 提取营业执照信息
            company_name = words_result.get('单位名称', {}).get('words', '')
            legal_person = words_result.get('法人', {}).get('words', '')
            address = words_result.get('地址', {}).get('words', '')
            register_number = words_result.get('证件编号', {}).get('words', '')
            business_scope = words_result.get('经营范围', {}).get('words', '')
            register_date = words_result.get('成立日期', {}).get('words', '')
            valid_period = words_result.get('有效期', {}).get('words', '')
            
            result_data = {
                "company_name": company_name,
                "legal_person": legal_person,
                "address": address,
                "register_number": register_number,
                "business_scope": business_scope,
                "register_date": register_date,
                "valid_period": valid_period
            }
            
            logger.info("营业执照识别成功")
            return result_data

        except Exception as e:
            logger.error(f"营业执照识别失败: {str(e)}")
            raise Exception(f"营业执照识别失败: {str(e)}")

    async def recognize_business_license_by_url(self, image_url: str) -> dict:
        """
        通过图片URL识别营业执照

        :param image_url: 营业执照图片URL
        :return: 识别结果
        """
        try:
            logger.info("开始通过URL识别营业执照")

            if not self.client:
                # 返回模拟数据
                logger.warning("百度OCR未配置，返回模拟数据")
                return {
                    "company_name": "北京某某科技有限公司",
                    "legal_person": "张三",
                    "address": "北京市朝阳区某某街道某某号",
                    "register_number": "91110000123456789X",
                    "business_scope": "技术开发、技术咨询、技术服务",
                    "register_date": "2020-01-01",
                    "valid_period": "2020-01-01至长期"
                }

            # 将URL转换为图片数据
            image_data = await self._url_to_image_data(image_url)

            # 调用百度OCR API
            result = self.client.businessLicense(image_data)

            if 'error_code' in result:
                logger.error(f"百度OCR识别失败: {result}")
                raise Exception(f"OCR识别失败: {result.get('error_msg', '未知错误')}")

            # 解析识别结果
            words_result = result.get('words_result', {})

            # 提取营业执照信息
            company_name = words_result.get('单位名称', {}).get('words', '')
            legal_person = words_result.get('法人', {}).get('words', '')
            address = words_result.get('地址', {}).get('words', '')
            register_number = words_result.get('证件编号', {}).get('words', '')
            business_scope = words_result.get('经营范围', {}).get('words', '')
            register_date = words_result.get('成立日期', {}).get('words', '')
            valid_period = words_result.get('有效期', {}).get('words', '')

            result_data = {
                "company_name": company_name,
                "legal_person": legal_person,
                "address": address,
                "register_number": register_number,
                "business_scope": business_scope,
                "register_date": register_date,
                "valid_period": valid_period
            }

            logger.info("营业执照识别成功")
            return result_data

        except Exception as e:
            logger.error(f"营业执照识别失败: {str(e)}")
            raise Exception(f"营业执照识别失败: {str(e)}")

    def _parse_area_from_id(self, id_number: str) -> tuple:
        """
        从身份证号码解析省市区信息
        
        :param id_number: 身份证号码
        :return: (省, 市, 区)
        """
        try:
            if not id_number or len(id_number) < 6:
                return "", "", ""
            
            # 这里应该根据身份证前6位查询地区编码表
            # 暂时返回默认值，实际项目中需要接入地区编码数据库
            area_code = id_number[:6]
            
            # 简单的地区编码映射（实际项目中应该查询完整的地区编码表）
            area_mapping = {
                "110000": ("北京市", "北京城区", "东城区"),
                "120000": ("天津市", "天津城区", "和平区"),
                "310000": ("上海市", "上海城区", "黄浦区"),
                "500000": ("重庆市", "重庆城区", "渝中区"),
            }
            
            # 根据前2位匹配省份
            province_code = area_code[:2] + "0000"
            if province_code in area_mapping:
                return area_mapping[province_code]
            
            return "", "", ""
            
        except Exception as e:
            logger.error(f"解析地区信息失败: {str(e)}")
            return "", "", ""


# 创建全局实例
baidu_ocr_util = BaiduOCRUtil()
