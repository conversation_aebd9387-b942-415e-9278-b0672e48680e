"""
提现账户服务层
"""
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.withdrawal_account import WithdrawalAccount
from module_admin.dao.withdrawal_account_dao import WithdrawalAccountDao
from exceptions.exception import BusinessException, ValidationException
from utils.log_util import logger


class WithdrawalAccountService:
    """提现账户服务层"""
    
    @classmethod
    async def get_withdrawal_accounts_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str
    ) -> List[Dict[str, Any]]:
        """
        获取公司提现账户信息列表

        Args:
            query_db: 数据库会话
            company_uuid: 公司UUID

        Returns:
            提现账户信息列表
        """
        try:
            logger.info(f"获取公司 {company_uuid} 的提现账户信息")

            # 查询提现账户列表
            accounts = await WithdrawalAccountDao.get_withdrawal_accounts_by_company(
                query_db, company_uuid
            )
            
            # 转换为返回格式
            withdrawal_accounts = []
            for account in accounts:
                account_info = {
                    "id": account.id,
                    "accountName": account.account_name,
                    "accountType": account.account_type,
                    "bankName": account.bank_name,
                    "bankBranch": account.bank_branch or "",
                    "bankAccount": account.bank_account,
                    "accountHolder": account.account_holder,
                    "isDefault": account.is_default
                }
                withdrawal_accounts.append(account_info)
            
            logger.info(f"成功获取公司 {company_uuid} 的 {len(withdrawal_accounts)} 个提现账户")
            return withdrawal_accounts

        except Exception as e:
            logger.error(f"获取提现账户信息失败: {str(e)}")
            raise BusinessException(message=f"获取提现账户信息失败: {str(e)}")

    @classmethod
    async def get_withdrawal_account_detail_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        account_id: int
    ) -> Dict[str, Any]:
        """
        获取提现账户详情服务

        Args:
            query_db: 数据库会话
            company_uuid: 公司UUID
            account_id: 账户ID

        Returns:
            提现账户详情
        """
        try:
            logger.info(f"获取公司 {company_uuid} 的账户 {account_id} 详情")

            # 查询账户详情
            account = await WithdrawalAccountDao.get_withdrawal_account_by_id(
                query_db, account_id, company_uuid
            )
            
            if not account:
                raise BusinessException(message="提现账户不存在")
            
            account_detail = {
                "id": account.id,
                "accountName": account.account_name,
                "accountType": account.account_type,
                "bankName": account.bank_name,
                "bankBranch": account.bank_branch or "",
                "bankAccount": account.bank_account,
                "accountHolder": account.account_holder,
                "invoiceImage": account.invoice_image or "",
                "remark": account.remark or "",
                "isDefault": account.is_default,
                "status": account.status,
                "createdAt": account.created_at.strftime("%Y-%m-%d %H:%M:%S") if account.created_at else "",
                "updatedAt": account.updated_at.strftime("%Y-%m-%d %H:%M:%S") if account.updated_at else ""
            }
            
            logger.info(f"成功获取账户详情: {account_detail}")
            return account_detail
            
        except BusinessException as e:
            logger.error(f"获取账户详情业务异常: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"获取账户详情失败: {str(e)}")
            raise BusinessException(message=f"获取账户详情失败: {str(e)}")

    @classmethod
    async def create_withdrawal_account_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        user_id: str,
        user_name: str,
        account_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        新增提现账户服务

        Args:
            query_db: 数据库会话
            company_uuid: 公司UUID
            user_id: 用户ID
            user_name: 用户姓名
            account_data: 账户数据

        Returns:
            新增账户结果
        """
        try:
            logger.info(f"用户 {user_id} 为公司 {company_uuid} 新增提现账户")
            
            # 验证必要字段
            required_fields = ['accountName', 'accountType', 'bankName', 'bankAccount', 'accountHolder']
            for field in required_fields:
                if not account_data.get(field):
                    raise ValidationException(message=f"缺少必要字段: {field}")
            
            # 验证账户类型
            account_type = account_data.get('accountType')
            if account_type not in [1, 2]:
                raise ValidationException(message="账户类型必须为1(企业对公)或2(个人)")
            
            # 检查银行账号是否已存在
            bank_account = account_data.get('bankAccount')
            if await WithdrawalAccountDao.check_account_exists(query_db, company_uuid, bank_account):
                raise BusinessException(message="该银行账户已存在")
            
            # 创建账户记录
            withdrawal_account = WithdrawalAccount(
                company_uuid=company_uuid,
                user_id=user_id,
                account_name=account_data.get('accountName'),
                account_type=account_type,
                bank_name=account_data.get('bankName'),
                bank_branch=account_data.get('bankBranch', ''),
                bank_account=bank_account,
                account_holder=account_data.get('accountHolder'),
                remark=account_data.get('remark', ''),
                is_default=account_data.get('isDefault', False),
                created_by=user_id
            )
            
            # 如果设置为默认账户，需要先取消其他默认账户
            if withdrawal_account.is_default:
                await WithdrawalAccountDao.set_default_account(query_db, 0, company_uuid)  # 先取消所有默认
            
            # 保存到数据库
            created_account = await WithdrawalAccountDao.create_withdrawal_account(
                query_db, withdrawal_account
            )
            
            # 如果是默认账户，设置为默认
            if created_account.is_default:
                await WithdrawalAccountDao.set_default_account(query_db, created_account.id, company_uuid)
            
            logger.info(f"✅ 新增提现账户成功: {created_account.id}")
            
            return {
                "id": created_account.id,
                "accountName": created_account.account_name,
                "accountType": created_account.account_type,
                "bankName": created_account.bank_name,
                "bankAccount": created_account.bank_account,
                "accountHolder": created_account.account_holder,
                "message": "提现账户新增成功"
            }
            
        except BusinessException as e:
            logger.error(f"新增提现账户业务异常: {str(e)}")
            raise e
        except ValidationException as e:
            logger.error(f"新增提现账户参数验证异常: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"新增提现账户系统异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"新增提现账户失败: {str(e)}")

    @classmethod
    async def update_withdrawal_account_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        user_id: str,
        account_id: int,
        account_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        修改提现账户服务

        Args:
            query_db: 数据库会话
            company_uuid: 公司UUID
            user_id: 用户ID
            account_id: 账户ID
            account_data: 账户数据

        Returns:
            修改结果
        """
        try:
            logger.info(f"用户 {user_id} 修改公司 {company_uuid} 的提现账户 {account_id}")
            
            # 检查账户是否存在
            existing_account = await WithdrawalAccountDao.get_withdrawal_account_by_id(
                query_db, account_id, company_uuid
            )
            if not existing_account:
                raise BusinessException(message="提现账户不存在")
            
            # 如果修改了银行账号，检查是否重复
            new_bank_account = account_data.get('bankAccount')
            if new_bank_account and new_bank_account != existing_account.bank_account:
                if await WithdrawalAccountDao.check_account_exists(
                    query_db, company_uuid, new_bank_account, account_id
                ):
                    raise BusinessException(message="该银行账户已存在")
            
            # 准备更新数据
            update_data = {
                'updated_by': user_id,
                'updated_at': datetime.now()
            }
            
            # 字段映射：前端驼峰命名 -> 数据库下划线命名
            field_mapping = {
                'accountName': 'account_name',
                'accountType': 'account_type',
                'bankName': 'bank_name',
                'bankBranch': 'bank_branch',
                'bankAccount': 'bank_account',
                'accountHolder': 'account_holder',
                'remark': 'remark'
            }

            # 更新允许的字段
            for frontend_field, db_field in field_mapping.items():
                if frontend_field in account_data:
                    update_data[db_field] = account_data[frontend_field]
            
            # 处理默认账户设置
            if 'isDefault' in account_data and account_data['isDefault']:
                await WithdrawalAccountDao.set_default_account(query_db, account_id, company_uuid)
            
            # 更新账户信息
            success = await WithdrawalAccountDao.update_withdrawal_account(
                query_db, account_id, company_uuid, update_data
            )
            
            if not success:
                raise BusinessException(message="更新提现账户失败")
            
            logger.info(f"✅ 修改提现账户成功: {account_id}")
            
            return {
                "id": account_id,
                "message": "提现账户修改成功"
            }
            
        except BusinessException as e:
            logger.error(f"修改提现账户业务异常: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"修改提现账户系统异常: {str(e)}")
            raise BusinessException(message=f"修改提现账户失败: {str(e)}")

    @classmethod
    async def delete_withdrawal_account_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        user_id: str,
        account_id: int
    ) -> Dict[str, Any]:
        """
        删除提现账户服务

        Args:
            query_db: 数据库会话
            company_uuid: 公司UUID
            user_id: 用户ID
            account_id: 账户ID

        Returns:
            删除结果
        """
        try:
            logger.info(f"用户 {user_id} 删除公司 {company_uuid} 的提现账户 {account_id}")

            # 检查账户是否存在
            existing_account = await WithdrawalAccountDao.get_withdrawal_account_by_id(
                query_db, account_id, company_uuid
            )
            if not existing_account:
                raise BusinessException(message="提现账户不存在")

            # 检查是否为默认账户
            if existing_account.is_default:
                raise BusinessException(message="默认账户不能删除，请先设置其他账户为默认")

            # 删除账户
            success = await WithdrawalAccountDao.delete_withdrawal_account(
                query_db, account_id, company_uuid, user_id
            )

            if not success:
                raise BusinessException(message="删除提现账户失败")

            logger.info(f"✅ 删除提现账户成功: {account_id}")

            return {
                "id": account_id,
                "message": "提现账户删除成功"
            }

        except BusinessException as e:
            logger.error(f"删除提现账户业务异常: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"删除提现账户系统异常: {str(e)}")
            raise BusinessException(message=f"删除提现账户失败: {str(e)}")

    @classmethod
    async def set_default_account_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        account_id: int
    ) -> Dict[str, Any]:
        """
        设置默认账户服务

        Args:
            query_db: 数据库会话
            company_uuid: 公司UUID
            account_id: 账户ID

        Returns:
            设置结果
        """
        try:
            logger.info(f"设置公司 {company_uuid} 的默认提现账户 {account_id}")

            # 检查账户是否存在
            existing_account = await WithdrawalAccountDao.get_withdrawal_account_by_id(
                query_db, account_id, company_uuid
            )
            if not existing_account:
                raise BusinessException(message="提现账户不存在")

            # 设置默认账户
            success = await WithdrawalAccountDao.set_default_account(
                query_db, account_id, company_uuid
            )

            if not success:
                raise BusinessException(message="设置默认账户失败")

            logger.info(f"✅ 设置默认账户成功: {account_id}")

            return {
                "id": account_id,
                "message": "设置默认账户成功"
            }

        except BusinessException as e:
            logger.error(f"设置默认账户业务异常: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"设置默认账户系统异常: {str(e)}")
            raise BusinessException(message=f"设置默认账户失败: {str(e)}")
