"""
公司版本关联数据模型
用于公司与软件版本关联关系的数据库映射
"""
from datetime import datetime
from sqlalchemy import Column, String, Integer, DECIMAL, SmallInteger, BigInteger, DateTime, Text
from config.database import Base


class CompanyVersionRelation(Base):
    """公司版本关联表"""

    __tablename__ = 'company_version_relation'

    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='ID')
    
    # 关联信息
    company_uuid = Column(String(36), nullable=False, comment='公司UUID')
    version_uuid = Column(String(36), nullable=False, comment='版本UUID')
    
    # 时间信息
    purchase_time = Column(DateTime, nullable=False, default=datetime.now, comment='购买时间')
    expire_time = Column(DateTime, nullable=False, comment='过期时间')
    
    # 状态信息
    status = Column(SmallInteger, nullable=False, default=1, comment='状态(1:正常,0:停用)')
    
    # 订单信息
    price = Column(DECIMAL(10, 2), nullable=True, comment='价格')
    order_no = Column(String(64), nullable=True, comment='订单号')
    
    # 备注信息
    remark = Column(Text, nullable=True, comment='备注')
    
    # 系统字段
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), nullable=True, comment='创建者')
    update_by = Column(String(64), nullable=True, comment='更新者')
    
    def __repr__(self):
        return f"<CompanyVersionRelation(id={self.id}, company_uuid='{self.company_uuid}', version_uuid='{self.version_uuid}')>"
