from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from module_admin.service.area_code_service import AreaCodeService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from config.get_db import get_db
from utils.response_util import ResponseUtil
from exceptions.exception import ValidationException
from utils.log_util import logger

# 创建路由器
area_code_controller = APIRouter(prefix="/api/v1/area", tags=["省市区编码"])


@area_code_controller.get('/provinces', summary="获取省份列表")
async def get_provinces(
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取所有省份列表接口
    
    获取系统中所有的省份信息
    
    Args:
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        省份列表
    """
    try:
        logger.info("获取省份列表")
        
        # 调用服务层获取省份列表
        result = await AreaCodeService.get_provinces(query_db)
        
        return ResponseUtil.success(
            msg="获取省份列表成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取省份列表失败: {str(e)}")
        return ResponseUtil.error(msg="获取省份列表失败")


@area_code_controller.get('/cities', summary="根据省编码获取城市列表")
async def get_cities_by_province(
    province_code: str = Query(..., description="省编码"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """根据省编码获取城市列表接口
    
    根据指定的省编码获取该省下的所有城市
    
    Args:
        province_code: 省编码
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        城市列表
    """
    try:
        logger.info(f"获取省份 {province_code} 的城市列表")
        
        # 调用服务层获取城市列表
        result = await AreaCodeService.get_cities_by_province(query_db, province_code)
        
        return ResponseUtil.success(
            msg="获取城市列表成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取城市列表失败: {str(e)}")
        return ResponseUtil.error(msg="获取城市列表失败")


@area_code_controller.get('/districts', summary="根据市编码获取区县列表")
async def get_districts_by_city(
    city_code: str = Query(..., description="市编码"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """根据市编码获取区县列表接口
    
    根据指定的市编码获取该市下的所有区县
    
    Args:
        city_code: 市编码
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        区县列表
    """
    try:
        logger.info(f"获取城市 {city_code} 的区县列表")
        
        # 调用服务层获取区县列表
        result = await AreaCodeService.get_districts_by_city(query_db, city_code)
        
        return ResponseUtil.success(
            msg="获取区县列表成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取区县列表失败: {str(e)}")
        return ResponseUtil.error(msg="获取区县列表失败")


@area_code_controller.get('/info-by-id-card', summary="根据身份证号获取省市区信息")
async def get_area_info_by_id_card(
    id_card: str = Query(..., description="身份证号"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """根据身份证号获取省市区信息接口
    
    根据身份证号前6位解析出对应的省市区信息
    
    Args:
        id_card: 身份证号
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        省市区信息
    """
    try:
        if not id_card or len(id_card) < 6:
            raise ValidationException(message="身份证号格式不正确")
        
        logger.info(f"根据身份证号获取省市区信息: {id_card[:6]}****")
        
        # 调用服务层获取省市区信息
        result = await AreaCodeService.get_area_info_by_id_card(query_db, id_card)
        
        if not result:
            return ResponseUtil.error(msg="未找到对应的省市区信息")
        
        return ResponseUtil.success(
            msg="获取省市区信息成功",
            data=result
        )
        
    except ValidationException as e:
        logger.warning(f"获取省市区信息参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"获取省市区信息失败: {str(e)}")
        return ResponseUtil.error(msg="获取省市区信息失败")


@area_code_controller.get('/info-by-district-code', summary="根据区编码获取省市区信息")
async def get_area_info_by_district_code(
    district_code: str = Query(..., description="区编码"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """根据区编码获取省市区信息接口
    
    根据区编码获取对应的省市区信息
    
    Args:
        district_code: 区编码
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        省市区信息
    """
    try:
        logger.info(f"根据区编码获取省市区信息: {district_code}")
        
        # 调用服务层获取省市区信息
        result = await AreaCodeService.get_area_info_by_district_code(query_db, district_code)
        
        if not result:
            return ResponseUtil.error(msg="未找到对应的省市区信息")
        
        return ResponseUtil.success(
            msg="获取省市区信息成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取省市区信息失败: {str(e)}")
        return ResponseUtil.error(msg="获取省市区信息失败")
