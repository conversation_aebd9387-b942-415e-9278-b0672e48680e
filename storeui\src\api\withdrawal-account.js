import { post, get } from '../utlis/require.js'

/**
 * 提现账户相关API
 * 注意：require.js已经统一处理了响应拦截，成功时直接返回业务数据，失败时自动抛出异常
 */

// 获取提现账户列表
export const getWithdrawalAccounts = () => {
  return get('/api/v1/payment/withdrawal/accounts').catch(error => {
    console.error('获取提现账户列表失败:', error);
    throw error;
  });
}

// 获取提现账户详情
export const getWithdrawalAccountDetail = (accountId) => {
  return get(`/api/v1/payment/withdrawal/accounts/${accountId}`).catch(error => {
    console.error('获取提现账户详情失败:', error);
    throw error;
  });
}

// 创建提现账户
export const createWithdrawalAccount = (data) => {
  return post('/api/v1/payment/withdrawal/accounts', data, { contentType: 'application/json' }).catch(error => {
    console.error('创建提现账户失败:', error);
    throw error;
  });
}

// 修改提现账户
export const updateWithdrawalAccount = (accountId, data) => {
  return post(`/api/v1/payment/withdrawal/accounts/${accountId}`, data, {
    contentType: 'application/json',
    method: 'PUT'
  }).catch(error => {
    console.error('修改提现账户失败:', error);
    throw error;
  });
}

// 删除提现账户
export const deleteWithdrawalAccount = (accountId) => {
  return post(`/api/v1/payment/withdrawal/accounts/${accountId}`, {}, {
    contentType: 'application/json',
    method: 'DELETE'
  }).catch(error => {
    console.error('删除提现账户失败:', error);
    throw error;
  });
}

// 设置默认提现账户
export const setDefaultWithdrawalAccount = (accountId) => {
  return post(`/api/v1/payment/withdrawal/accounts/${accountId}/default`, {}, {
    contentType: 'application/json'
  }).catch(error => {
    console.error('设置默认提现账户失败:', error);
    throw error;
  });
}

export default {
  getWithdrawalAccounts,
  getWithdrawalAccountDetail,
  createWithdrawalAccount,
  updateWithdrawalAccount,
  deleteWithdrawalAccount,
  setDefaultWithdrawalAccount
}
