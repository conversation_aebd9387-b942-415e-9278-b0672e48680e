<template>
  <view class="store-management-page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <u-icon name="arrow-left" color="#333" size="20"></u-icon>
        </view>
        <view class="navbar-title">门店管理</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 简洁统计区域 -->
    <view class="stats-header">
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-number">{{ storeStats.total }}</text>
          <text class="stat-label">总门店</text>
        </view>
        <view class="stat-item">
          <text class="stat-number open">{{ storeStats.open_count }}</text>
          <text class="stat-label">营业中</text>
        </view>
        <view class="stat-item">
          <text class="stat-number closed">{{ storeStats.closed_count }}</text>
          <text class="stat-label">已关闭</text>
        </view>
      </view>
    </view>

    <!-- 筛选Tab -->
    <view class="filter-tabs">
      <view class="tabs-container">
        <view
          class="tab-item"
          :class="{ active: currentTab === 'all' }"
          @click="switchTab('all')"
        >
          <text class="tab-text">全部</text>
          <view class="tab-indicator" v-if="currentTab === 'all'"></view>
        </view>
        <view
          class="tab-item"
          :class="{ active: currentTab === 'open' }"
          @click="switchTab('open')"
        >
          <text class="tab-text">营业中</text>
          <view class="tab-indicator" v-if="currentTab === 'open'"></view>
        </view>
        <view
          class="tab-item"
          :class="{ active: currentTab === 'closed' }"
          @click="switchTab('closed')"
        >
          <text class="tab-text">已关闭</text>
          <view class="tab-indicator" v-if="currentTab === 'closed'"></view>
        </view>
      </view>
    </view>

    <!-- 门店列表 -->
    <view class="store-list">
      <view v-if="loading" class="loading-container">
        <u-loading-icon mode="spinner"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>
      
      <view v-else-if="storeList.length === 0" class="empty-container">
        <u-empty text="暂无门店数据" mode="list"></u-empty>
      </view>
      
      <view v-else>
        <view 
          class="store-item" 
          v-for="store in storeList" 
          :key="store.store_uuid"
          @click="viewStoreDetail(store)"
        >
          <view class="store-header">
            <view class="store-name">{{ store.name }}</view>
            <view class="store-status" :class="store.status === 1 ? 'open' : 'closed'">
              {{ store.status === 1 ? '营业中' : '已关闭' }}
            </view>
          </view>
          
          <view class="store-info">
            <view class="info-row" v-if="store.manager">
              <u-icon name="account" color="#999" size="14"></u-icon>
              <text class="info-text">负责人：{{ store.manager }}</text>
            </view>
            
            <view class="info-row" v-if="store.phone">
              <u-icon name="phone" color="#999" size="14"></u-icon>
              <text class="info-text">{{ store.phone }}</text>
              <view class="call-btn" @click.stop="makeCall(store.phone)">
                <u-icon name="phone-fill" color="#07c160" size="16"></u-icon>
              </view>
            </view>
            
            <view class="info-row" v-if="store.address">
              <u-icon name="map" color="#999" size="14"></u-icon>
              <text class="info-text">{{ store.address }}</text>
            </view>
            
            <view class="info-row" v-if="store.business_hours">
              <u-icon name="clock" color="#999" size="14"></u-icon>
              <text class="info-text">营业时间：{{ store.business_hours }}</text>
            </view>
          </view>
          
          <view class="store-footer">
            <text class="create-time">创建时间：{{ formatTime(store.create_time) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分页加载 -->
    <view class="pagination-container" v-if="storeList.length > 0">
      <view v-if="hasMore" class="load-more" @click="loadMore">
        <text class="load-more-text">加载更多</text>
      </view>
      <view v-else class="no-more">
        <text class="no-more-text">没有更多数据了</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { get } from '@/utlis/require.js'

export default {
  data() {
    return {
      currentTab: 'all', // 当前选中的Tab
      storeList: [], // 门店列表
      storeStats: { // 门店统计
        total: 0,
        open_count: 0,
        closed_count: 0
      },
      loading: false,
      page: 1,
      size: 10,
      hasMore: true
    }
  },

  computed: {
    ...mapState(['StatusBar', 'token']),
    
    // 当前筛选状态
    currentStatus() {
      switch (this.currentTab) {
        case 'open':
          return 1
        case 'closed':
          return 0
        default:
          return null
      }
    }
  },

  onLoad() {
    this.initData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData()
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },

  methods: {
    // 初始化数据
    async initData() {
      await this.loadStoreStats()
      await this.loadStoreList(true)
    },

    // 刷新数据
    async refreshData() {
      this.page = 1
      this.hasMore = true
      await this.loadStoreStats()
      await this.loadStoreList(true)
    },

    // 加载门店统计
    async loadStoreStats() {
      try {
        const response = await get('/api/v1/store/count')
        this.storeStats = response
      } catch (error) {
        console.error('加载门店统计失败:', error)
        uni.showToast({
          title: '加载统计数据失败',
          icon: 'none'
        })
      }
    },

    // 加载门店列表
    async loadStoreList(reset = false) {
      if (this.loading) return

      this.loading = true

      try {
        const params = {
          page: reset ? 1 : this.page,
          size: this.size
        }

        if (this.currentStatus !== null) {
          params.status = this.currentStatus
        }

        const response = await get('/api/v1/store/list', params)
        const { list, has_next } = response

        if (reset) {
          this.storeList = list
          this.page = 1
        } else {
          this.storeList = [...this.storeList, ...list]
        }

        this.hasMore = has_next
        if (has_next) {
          this.page++
        }
      } catch (error) {
        console.error('加载门店列表失败:', error)
        uni.showToast({
          title: '加载门店列表失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 切换Tab
    switchTab(tab) {
      if (this.currentTab === tab) return
      
      this.currentTab = tab
      this.refreshData()
    },

    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadStoreList()
      }
    },

    // 查看门店详情
    viewStoreDetail(store) {
      console.log('查看门店详情:', store)
      uni.navigateTo({
        url: `/pages-set/storeManagement/storeInfo?store_uuid=${store.store_uuid}`
      })
    },

    // 拨打电话
    makeCall(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        fail: (err) => {
          console.error('拨打电话失败:', err)
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          })
        }
      })
    },

    // 返回上一页
    goBack() {
      uni.navigateBack({
        fail: () => {
          uni.reLaunch({
            url: '/pages/home/<USER>'
          })
        }
      })
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      
      const date = new Date(timeStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.store-management-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #f5f5f5 100%);
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  padding-top: calc(var(--status-bar-height) + 20px);
  position: relative;
  z-index: 10;
  box-shadow: 0 4px 20px rgba(253, 209, 24, 0.15);

  .navbar-content {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;

    .navbar-left {
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 22px;
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.25);
        transform: scale(0.95);
      }
    }

    .navbar-title {
      font-size: 18px;
      font-weight: 700;
      color: #fff;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .navbar-right {
      width: 44px;
      height: 44px;
    }
  }
}

/* 优化统计区域 */
.stats-header {
  background: #fff;
  padding: 25px 20px;
  margin: 15px 20px 0;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(253, 209, 24, 0.1);

  .stats-row {
    display: flex;
    justify-content: space-around;

    .stat-item {
      text-align: center;
      flex: 1;
      position: relative;
      padding: 10px 0;

      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 40px;
        background: linear-gradient(to bottom, transparent, #efefef, transparent);
      }

      &:last-child::after {
        display: none;
      }

      .stat-number {
        display: block;
        font-size: 28px;
        font-weight: 800;
        color: $xyj-theme;
        margin-bottom: 8px;
        text-shadow: 0 1px 3px rgba(253, 209, 24, 0.2);

        &.open {
          color: $xyj-decorate3;
          text-shadow: 0 1px 3px rgba(9, 190, 137, 0.2);
        }

        &.closed {
          color: $text-main;
          text-shadow: none;
        }
      }

      .stat-label {
        font-size: 13px;
        color: $text-main;
        font-weight: 600;
        letter-spacing: 0.5px;
      }
    }
  }
}

/* 优化筛选Tab */
.filter-tabs {
  background: #fff;
  margin: 15px 20px 0;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  overflow: hidden;

  .tabs-container {
    display: flex;

    .tab-item {
      flex: 1;
      position: relative;
      text-align: center;
      padding: 18px 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: #fff;

      .tab-text {
        font-size: 15px;
        color: $text-main;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
      }

      .tab-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 4px;
        background: linear-gradient(135deg, $xyj-theme 0%, $xyj-decorate2 100%);
        border-radius: 2px 2px 0 0;
        animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 -2px 8px rgba(253, 209, 24, 0.3);
      }

      &.active {
        background: linear-gradient(135deg, rgba(253, 209, 24, 0.05) 0%, rgba(255, 128, 27, 0.05) 100%);

        .tab-text {
          color: $text-title;
          font-weight: 700;
          transform: translateY(-1px);
        }
      }

      &:active {
        background: rgba(253, 209, 24, 0.1);
        transform: scale(0.98);
      }
    }
  }
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
    transform: translateX(-50%) scaleX(0);
  }
  to {
    width: 40px;
    opacity: 1;
    transform: translateX(-50%) scaleX(1);
  }
}

/* 优化门店列表 */
.store-list {
  padding: 15px 20px 20px;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 60px 0;
    background: #fff;
    border-radius: 20px;
    margin-top: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);

    .loading-text {
      margin-top: 15px;
      font-size: 14px;
      color: $text-main;
      font-weight: 500;
    }
  }

  .empty-container {
    padding: 60px 0;
    background: #fff;
    border-radius: 20px;
    margin-top: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  }

  .store-item {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    transition: box-shadow 0.2s ease;

    &:active {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .store-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .store-name {
        font-size: 17px;
        font-weight: 600;
        color: $text-title;
        flex: 1;
        line-height: 1.3;
        margin-right: 12px;
      }

      .store-status {
        padding: 6px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;

        &.open {
          background: rgba(9, 190, 137, 0.1);
          color: $xyj-decorate3;
          border: 1px solid rgba(9, 190, 137, 0.2);
        }

        &.closed {
          background: rgba(153, 153, 153, 0.1);
          color: $text-main;
          border: 1px solid rgba(153, 153, 153, 0.2);
        }
      }
    }

    .store-info {
      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        padding: 4px 0;

        &:last-child {
          margin-bottom: 0;
        }

        .info-text {
          font-size: 14px;
          color: $text-main;
          margin-left: 10px;
          flex: 1;
          line-height: 1.4;
          font-weight: 500;
        }

        .call-btn {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(9, 190, 137, 0.1);
          border-radius: 16px;
          margin-left: 8px;
          border: 1px solid rgba(9, 190, 137, 0.2);
          transition: background-color 0.2s ease;

          &:active {
            background: $xyj-decorate3;
          }
        }
      }
    }

    .store-footer {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;

      .create-time {
        font-size: 12px;
        color: $text-main;
        background: #f8f9fa;
        padding: 4px 8px;
        border-radius: 8px;
        display: inline-block;
        font-weight: 500;
      }
    }
  }
}

/* 分页容器 */
.pagination-container {
  padding: 20px;
  text-align: center;

  .load-more {
    padding: 16px 32px;
    background: $xyj-theme;
    color: #fff;
    border-radius: 24px;
    display: inline-block;
    box-shadow: 0 4px 12px rgba(253, 209, 24, 0.3);
    transition: all 0.2s ease;
    border: none;

    &:active {
      background: $xyj-decorate2;
      box-shadow: 0 2px 8px rgba(255, 128, 27, 0.4);
    }

    .load-more-text {
      font-size: 15px;
      font-weight: 600;
    }
  }

  .no-more {
    .no-more-text {
      font-size: 14px;
      color: $text-main;
      background: #f8f9fa;
      padding: 8px 16px;
      border-radius: 16px;
      display: inline-block;
      font-weight: 500;
    }
  }
}
</style>
