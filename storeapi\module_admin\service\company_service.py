from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, List, Optional, Any
from decimal import Decimal
from datetime import datetime, timedelta
from module_admin.dao.company_dao import CompanyDao
from module_admin.entity.vo.software_renewal_vo import RenewalPreviewResponse, SoftwareRenewalResponse
from exceptions.exception import BusinessException, QueryException, ResourceNotFoundException, ValidationException
from utils.log_util import logger

class CompanyService:
    """公司服务"""
    
    @classmethod
    async def get_account_balance_service(cls, query_db: AsyncSession, company_id: str) -> Dict[str, Any]:
        """
        获取公司账户余额服务
        
        :param query_db: 数据库会话
        :param company_id: 公司ID
        :return: 账户余额信息
        """
        try:
            # 调用DAO层获取公司账户余额
            account_balance = await CompanyDao.get_account_balance(query_db, company_id)
            
            if not account_balance:
                raise ResourceNotFoundException(message=f"未找到ID为{company_id}的公司账户信息")
            
            return account_balance
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取公司账户余额查询异常: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"公司账户资源不存在: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取公司账户余额服务异常: {str(e)}")
            raise BusinessException(message=f"获取公司账户余额失败: {str(e)}")
    
    @classmethod
    async def get_insurance_list_service(
        cls, 
        query_db: AsyncSession, 
        page: int, 
        size: int
    ) -> Dict[str, Any]:
        """
        获取保险列表服务
        
        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :return: 保险列表数据
        """
        try:
            # 调用DAO层获取保险列表
            insurance_list, total = await CompanyDao.get_insurance_list(query_db, page, size)
            
            # 构建返回数据
            result = {
                "page": page,
                "size": size,
                "list": insurance_list,
                "total": total
            }
            
            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取保险列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取保险列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取保险列表失败: {str(e)}")
    
    @classmethod
    async def find_insurance_order_list_service(
        cls, 
        query_db: AsyncSession, 
        page: int, 
        size: int, 
        status: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取保险订单列表服务
        
        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 订单状态
        :return: 保险订单列表数据
        """
        try:
            # 调用DAO层获取保险订单列表
            insurance_order_list, total = await CompanyDao.find_insurance_order_list(query_db, page, size, status)
            
            # 构建返回数据
            result = {
                "page": page,
                "size": size,
                "list": insurance_order_list,
                "total": total
            }
            
            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取保险订单列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取保险订单列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取保险订单列表失败: {str(e)}")
    
    @classmethod
    async def find_store_detail_service(cls, query_db: AsyncSession, store_id: str) -> Dict[str, Any]:
        """
        获取门店详情服务
        
        :param query_db: 数据库会话
        :param store_id: 门店ID
        :return: 门店详情数据
        """
        try:
            # 调用DAO层获取门店详情
            store = await CompanyDao.find_store_detail(query_db, store_id)
            
            if not store:
                raise ResourceNotFoundException(message=f"未找到ID为{store_id}的门店")
            
            return store
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取门店详情查询异常: {e.message}")
            raise e
        except ResourceNotFoundException as e:
            # 资源不存在异常，直接向上传递
            logger.error(f"门店资源不存在: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取门店详情服务异常: {str(e)}")
            raise BusinessException(message=f"获取门店详情失败: {str(e)}")
    
    @classmethod
    async def find_store_service(
        cls, 
        query_db: AsyncSession, 
        page: int, 
        size: int, 
        keywords: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询门店列表服务
        
        :param query_db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param keywords: 关键词
        :return: 门店列表数据
        """
        try:
            # 调用DAO层查询门店列表
            store_list, total = await CompanyDao.find_store(query_db, page, size, keywords)
            
            # 构建返回数据
            result = {
                "page": page,
                "size": size,
                "list": store_list,
                "total": total
            }
            
            return result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"查询门店列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"查询门店列表服务异常: {str(e)}")
            raise BusinessException(message=f"查询门店列表失败: {str(e)}")
    
    @classmethod
    async def create_pay_order_service(cls, query_db: AsyncSession, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建支付订单服务
        
        :param query_db: 数据库会话
        :param order_data: 订单数据
        :return: 创建的订单数据
        """
        try:
            # 验证必要字段
            if not order_data.get("amount"):
                raise BusinessException(message="订单金额不能为空")
            
            if not order_data.get("pay_type"):
                raise BusinessException(message="支付类型不能为空")
            
            # 调用DAO层创建支付订单
            order = await CompanyDao.create_pay_order(query_db, order_data)
            
            return order
        except BusinessException as e:
            # 业务异常，直接向上传递
            logger.error(f"创建支付订单业务异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"创建支付订单服务异常: {str(e)}")
            raise BusinessException(message=f"创建支付订单失败: {str(e)}")

    @classmethod
    async def get_system_versions_service(cls, query_db: AsyncSession, company_uuid: str) -> List[Dict[str, Any]]:
        """
        获取系统版本列表服务

        :param query_db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 系统版本列表数据
        """
        try:
            # 调用DAO层获取系统版本列表
            versions = await CompanyDao.get_system_versions_with_company_relation(query_db, company_uuid)

            return versions
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取系统版本列表查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取系统版本列表服务异常: {str(e)}")
            raise BusinessException(message=f"获取系统版本列表失败: {str(e)}")

    @classmethod
    async def get_company_payment_info_service(cls, query_db: AsyncSession, company_uuid: str) -> Dict[str, Any]:
        """
        获取公司支付方式列表和账户余额服务

        :param query_db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 支付方式列表和账户余额信息
        """
        try:
            # 调用DAO层获取公司支付信息
            payment_info = await CompanyDao.get_company_payment_info(query_db, company_uuid)

            return payment_info
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取公司支付信息查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取公司支付信息服务异常: {str(e)}")
            raise BusinessException(message=f"获取公司支付信息失败: {str(e)}")

    @classmethod
    async def get_company_withdrawal_config_service(cls, query_db: AsyncSession, company_uuid: str) -> Dict[str, Any]:
        """
        获取公司提现配置服务

        :param query_db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 提现配置信息
        """
        try:
            # 调用DAO层获取公司提现配置
            withdrawal_config = await CompanyDao.get_company_withdrawal_config(query_db, company_uuid)

            return withdrawal_config
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"获取公司提现配置查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"获取公司提现配置服务异常: {str(e)}")
            raise BusinessException(message=f"获取公司提现配置失败: {str(e)}")

    @classmethod
    async def preview_software_renewal_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        version_uuid: str,
        renewal_months: int
    ) -> Dict[str, Any]:
        """
        软件续费预览服务

        :param query_db: 数据库会话
        :param company_uuid: 公司UUID
        :param version_uuid: 软件版本UUID
        :param renewal_months: 续费月数
        :return: 续费预览信息
        """
        try:
            # 调用DAO层获取续费预览信息
            preview_info = await CompanyDao.preview_software_renewal(
                query_db, company_uuid, version_uuid, renewal_months
            )

            return preview_info
        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"软件续费预览验证异常: {e.message}")
            raise e
        except BusinessException as e:
            # 业务异常，直接向上传递
            logger.error(f"软件续费预览业务异常: {e.message}")
            raise e
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"软件续费预览查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"软件续费预览服务异常: {str(e)}")
            raise BusinessException(message=f"软件续费预览失败: {str(e)}")

    @classmethod
    async def execute_software_renewal_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        version_uuid: str,
        renewal_months: int,
        operator_id: str,
        operator_name: str,
        remark: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行软件续费服务

        :param query_db: 数据库会话
        :param company_uuid: 公司UUID
        :param version_uuid: 软件版本UUID
        :param renewal_months: 续费月数
        :param operator_id: 操作人ID
        :param operator_name: 操作人姓名
        :param remark: 续费备注
        :return: 续费执行结果
        """
        try:
            # 调用DAO层执行软件续费
            renewal_result = await CompanyDao.execute_software_renewal(
                query_db, company_uuid, version_uuid, renewal_months,
                operator_id, operator_name, remark
            )

            return renewal_result
        except ValidationException as e:
            # 验证异常，直接向上传递
            logger.error(f"软件续费执行验证异常: {e.message}")
            raise e
        except BusinessException as e:
            # 业务异常，直接向上传递
            logger.error(f"软件续费执行业务异常: {e.message}")
            raise e
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"软件续费执行查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"软件续费执行服务异常: {str(e)}")
            raise BusinessException(message=f"软件续费执行失败: {str(e)}")

    @classmethod
    async def check_company_version_expiry_service(cls, query_db: AsyncSession, company_uuid: str) -> Dict[str, Any]:
        """
        检查公司版本过期状态服务

        :param query_db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 版本过期检查结果
        """
        try:
            # 调用DAO层检查版本过期状态
            expiry_result = await CompanyDao.check_company_version_expiry(query_db, company_uuid)

            return expiry_result
        except QueryException as e:
            # 查询异常，直接向上传递
            logger.error(f"检查版本过期状态查询异常: {e.message}")
            raise e
        except Exception as e:
            # 其他异常，包装为业务异常
            logger.error(f"检查版本过期状态服务异常: {str(e)}")
            raise BusinessException(message=f"检查版本过期状态失败: {str(e)}")
