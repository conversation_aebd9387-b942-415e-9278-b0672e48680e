import tokenManager from './tokenManager.js'
import { switchToStaff, switchToStore } from '../api/staff-auth.js'

/**
 * 角色管理器
 * 处理员工端和门店端之间的角色切换
 */
class RoleManager {
  /**
   * 切换到员工端
   * @param {string} mobile - 手机号
   * @returns {Promise<boolean>} 切换是否成功
   */
  async switchToStaff(mobile) {
    try {
      console.log('=== RoleManager: 切换到员工端 ===', mobile)
      
      // 调用切换API
      const result = await switchToStaff(mobile)
      
      if (result && result.access_token) {
        // 使用TokenManager设置员工端token
        tokenManager.setToken(result.access_token, 'staff', {
          staffInfo: result.staff_info
        })
        
        console.log('RoleManager: 切换到员工端成功')
        return true
      } else {
        console.error('RoleManager: 切换到员工端失败，未获取到token')
        return false
      }
    } catch (error) {
      console.error('RoleManager: 切换到员工端异常', error)
      uni.showToast({
        title: '切换失败，请重试',
        icon: 'none'
      })
      return false
    }
  }

  /**
   * 切换到管理端
   * @param {string} mobile - 手机号
   * @returns {Promise<boolean>} 切换是否成功
   */
  async switchToStore(mobile) {
    try {
      console.log('=== RoleManager: 切换到管理端 ===', mobile)
      
      // 调用切换API
      const result = await switchToStore(mobile)
      
      if (result && result.access_token) {
        // 使用TokenManager设置管理端token
        tokenManager.setToken(result.access_token, 'store', {
          user: result.user,
          storeInfo: result.store_info
        })
        
        console.log('RoleManager: 切换到管理端成功')
        return true
      } else {
        console.error('RoleManager: 切换到管理端失败，未获取到token')
        return false
      }
    } catch (error) {
      console.error('RoleManager: 切换到管理端异常', error)
      uni.showToast({
        title: '切换失败，请重试',
        icon: 'none'
      })
      return false
    }
  }

  /**
   * 执行角色切换后的清理工作
   * @param {string} newRole - 新角色
   */
  cleanupAfterRoleSwitch(newRole) {
    console.log(`=== RoleManager: 角色切换后清理，新角色: ${newRole} ===`)
    
    // 使用TokenManager处理角色切换
    tokenManager.handleRoleSwitch(newRole)
    
    // 更新最后活跃时间
    tokenManager.updateLastActiveTime()
    
    console.log('RoleManager: 角色切换清理完成')
  }

  /**
   * 检查用户是否具有双重身份
   * @returns {boolean} 是否具有双重身份
   */
  hasDualRole() {
    const storeInfo = uni.getStorageSync('storeInfo')
    const staffInfo = uni.getStorageSync('staffInfo')
    
    return !!(storeInfo && staffInfo)
  }

  /**
   * 获取当前角色的显示名称
   * @returns {string} 角色显示名称
   */
  getCurrentRoleDisplayName() {
    const currentRole = tokenManager.getCurrentRole()
    return currentRole === 'staff' ? '员工接单端' : '门店管理端'
  }

  /**
   * 获取另一个角色的显示名称
   * @returns {string} 另一个角色的显示名称
   */
  getOtherRoleDisplayName() {
    const currentRole = tokenManager.getCurrentRole()
    return currentRole === 'staff' ? '门店管理端' : '员工接单端'
  }

  /**
   * 智能角色切换（自动检测目标角色）
   * @param {string} mobile - 手机号
   * @returns {Promise<boolean>} 切换是否成功
   */
  async smartRoleSwitch(mobile) {
    const currentRole = tokenManager.getCurrentRole()
    const targetRole = currentRole === 'staff' ? 'store' : 'staff'
    
    console.log(`=== RoleManager: 智能切换从${currentRole}到${targetRole} ===`)
    
    let success = false
    
    if (targetRole === 'staff') {
      success = await this.switchToStaff(mobile)
    } else {
      success = await this.switchToStore(mobile)
    }
    
    if (success) {
      // 执行切换后清理
      this.cleanupAfterRoleSwitch(targetRole)
      
      // 显示成功提示
      uni.showToast({
        title: `已切换到${this.getCurrentRoleDisplayName()}`,
        icon: 'none'
      })
      
      // 跳转到对应首页
      setTimeout(() => {
        if (targetRole === 'staff') {
          uni.reLaunch({
            url: '/pages-staff/home/<USER>'
          })
        } else {
          uni.reLaunch({
            url: '/pages/home/<USER>'
          })
        }
      }, 1500)
    }
    
    return success
  }

  /**
   * 验证当前角色的token有效性
   * @returns {Promise<boolean>} token是否有效
   */
  async validateCurrentRoleToken() {
    return await tokenManager.validateCurrentToken()
  }

  /**
   * 强制刷新当前角色的用户信息
   * @returns {Promise<boolean>} 刷新是否成功
   */
  async refreshCurrentUserInfo() {
    console.log('=== RoleManager: 强制刷新用户信息 ===')
    
    // 通过重新验证token来刷新用户信息
    const isValid = await tokenManager.validateCurrentToken()
    
    if (isValid) {
      console.log('RoleManager: 用户信息刷新成功')
    } else {
      console.log('RoleManager: 用户信息刷新失败')
    }
    
    return isValid
  }
}

// 创建单例实例
const roleManager = new RoleManager()

// 导出清理函数供现有组件使用（向后兼容）
export const cleanupAfterRoleSwitch = (newRole) => {
  roleManager.cleanupAfterRoleSwitch(newRole)
}

export default roleManager
