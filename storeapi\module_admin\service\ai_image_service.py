#!/usr/bin/env python3
"""
AI图片生成服务
基于Coze工作流的产品主图一键生成功能
"""

import json
import requests
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from exceptions.exception import BusinessException

logger = logging.getLogger(__name__)


class AIImageService:
    """AI图片生成服务"""
    
    # Coze工作流配置
    COZE_CONFIG = {
        "api_token": "sat_wPEVhy0VYiEtBE3OlfJiS7Nrzc6wpnXyWuvsqOZIfxnTW8p6yW5MXjditppi2O8j",
        "base_url": "https://api.coze.cn",
        "workflow_id": "7532396941256278052",
        "timeout": 30  # 30秒超时
    }
    
    @classmethod
    async def generate_product_image(cls, service_name: str) -> Dict[str, Any]:
        """
        生成产品主图
        
        Args:
            service_name: 服务名称/产品名称
            
        Returns:
            Dict[str, Any]: 生成结果
            {
                "success": bool,
                "image_url": str,  # 生成的图片URL
                "message": str,    # 结果消息
                "workflow_data": dict  # 工作流返回的完整数据
            }
        """
        try:
            logger.info(f"开始生成产品主图，服务名称: {service_name}")
            
            # 参数验证
            if not service_name or not service_name.strip():
                raise BusinessException(message="服务名称不能为空")

            service_name = service_name.strip()

            # 长度验证
            if len(service_name) < 2:
                raise BusinessException(message="服务名称至少需要2个字符")

            if len(service_name) > 100:
                raise BusinessException(message="服务名称不能超过100个字符")

            # 内容验证（可选：过滤特殊字符）
            import re
            if not re.match(r'^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）]+$', service_name):
                raise BusinessException(message="服务名称包含不支持的字符，请使用中文、英文、数字或常用符号")
            
            # 调用Coze工作流
            workflow_result = await cls._call_coze_workflow(service_name.strip())
            
            # 检查工作流是否执行成功
            if not workflow_result:
                logger.error("工作流返回空结果")
                return {
                    "success": False,
                    "image_url": None,
                    "message": "AI服务返回异常，请稍后重试",
                    "workflow_data": workflow_result
                }

            # 解析工作流结果
            coze_image_url = cls._extract_image_url(workflow_result)

            if not coze_image_url:
                logger.warning(f"工作流执行成功但未获取到图片URL，完整结果: {json.dumps(workflow_result, ensure_ascii=False)}")
                return {
                    "success": False,
                    "image_url": None,
                    "message": "图片生成失败，AI服务未返回有效图片",
                    "workflow_data": workflow_result
                }

            logger.info(f"从Coze获取到图片URL: {coze_image_url}")

            # 下载图片并上传到OBS
            try:
                obs_image_url = await cls._download_and_upload_to_obs(coze_image_url, service_name)

                logger.info(f"产品主图生成并上传成功: {obs_image_url}")
                return {
                    "success": True,
                    "image_url": obs_image_url,
                    "message": "图片生成成功",
                    "coze_url": coze_image_url,  # 保留原始URL用于调试
                    "workflow_data": workflow_result
                }

            except Exception as upload_error:
                logger.error(f"图片上传到OBS失败: {upload_error}")
                import traceback
                logger.error(f"上传异常堆栈: {traceback.format_exc()}")

                # 如果上传失败，返回原始URL作为备选
                return {
                    "success": True,
                    "image_url": coze_image_url,  # 使用原始URL
                    "message": "图片生成成功，但上传到云存储失败，使用临时链接",
                    "upload_error": str(upload_error),
                    "workflow_data": workflow_result
                }
            
        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"生成产品主图失败: {str(e)}")
            raise BusinessException(message=f"图片生成失败: {str(e)}")
    
    @classmethod
    async def _call_coze_workflow(cls, service_name: str) -> Dict[str, Any]:
        """
        调用Coze工作流
        
        Args:
            service_name: 服务名称
            
        Returns:
            Dict[str, Any]: 工作流执行结果
        """
        try:
            # 构建请求数据
            payload = {
                "workflow_id": cls.COZE_CONFIG["workflow_id"],
                "parameters": {
                    "serviceName": service_name
                }
            }
            
            # 构建请求头
            headers = {
                'Authorization': f'Bearer {cls.COZE_CONFIG["api_token"]}',
                'Content-Type': 'application/json'
            }
            
            url = f"{cls.COZE_CONFIG['base_url']}/v1/workflow/run"
            
            logger.info(f"调用Coze工作流: {url}")
            logger.debug(f"请求参数: {json.dumps(payload, ensure_ascii=False)}")
            
            # 使用asyncio运行同步请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=cls.COZE_CONFIG["timeout"]
                )
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                error_msg = f"Coze API错误 {response.status_code}: {response.text}"
                logger.error(error_msg)
                raise BusinessException(message=f"AI服务调用失败: {error_msg}")
            
            # 解析JSON响应
            result = response.json()
            logger.info("Coze工作流执行完成")
            logger.debug(f"工作流返回结果: {json.dumps(result, ensure_ascii=False)}")
            
            return result
            
        except requests.Timeout:
            logger.error(f"Coze工作流调用超时 ({cls.COZE_CONFIG['timeout']}秒)")
            raise BusinessException(message="图片生成超时，请稍后重试")
        except requests.RequestException as e:
            logger.error(f"Coze工作流网络请求错误: {e}")
            raise BusinessException(message="AI服务网络连接失败")
        except Exception as e:
            logger.error(f"调用Coze工作流失败: {e}")
            raise BusinessException(message=f"AI服务调用失败: {str(e)}")

    @classmethod
    async def _download_and_upload_to_obs(cls, image_url: str, service_name: str) -> str:
        """
        下载图片并上传到OBS

        Args:
            image_url: Coze生成的图片URL
            service_name: 服务名称，用于生成文件名

        Returns:
            str: OBS中的图片URL
        """
        from datetime import datetime
        from utils.file_upload_util import FileUploadUtil

        try:
            logger.info(f"开始从Coze URL下载并上传图片: {image_url}")

            # 1. 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_service_name = "".join(c for c in service_name if c.isalnum() or c in (' ', '-', '_')).strip()
            safe_service_name = safe_service_name.replace(' ', '_')[:20]  # 限制长度

            # 2. 确定文件扩展名（从URL或默认为PNG）
            if image_url.lower().endswith('.png') or 'png' in image_url.lower():
                ext = '.png'
            elif image_url.lower().endswith(('.jpg', '.jpeg')) or 'jpg' in image_url.lower():
                ext = '.jpg'
            elif image_url.lower().endswith('.webp') or 'webp' in image_url.lower():
                ext = '.webp'
            else:
                ext = '.png'  # 默认使用PNG

            filename = f"ai_product_{safe_service_name}_{timestamp}{ext}"

            # 3. 使用FileUploadUtil从URL下载并上传到OBS
            file_upload_util = FileUploadUtil()

            upload_result = await file_upload_util.upload_from_url(
                url=image_url,
                file_name=filename,
                route="ai-images",  # 使用专门的AI图片路径
                created_by="ai_system",
                db=None  # 不创建数据库记录，只上传文件
            )

            obs_url = upload_result["file_url"]
            logger.info(f"图片从Coze下载并上传到OBS成功: {obs_url}")

            return obs_url

        except Exception as e:
            logger.error(f"下载并上传图片失败: {e}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise Exception(f"图片处理失败: {str(e)}")


    @classmethod
    def _extract_image_url(cls, workflow_result: Dict[str, Any]) -> Optional[str]:
        """
        从工作流结果中提取图片URL

        Args:
            workflow_result: 工作流返回的结果

        Returns:
            Optional[str]: 图片URL，如果未找到则返回None
        """
        try:
            logger.debug(f"开始提取图片URL，工作流结果: {json.dumps(workflow_result, ensure_ascii=False)}")

            # 处理Coze工作流的实际响应格式
            # 响应结构: {"code": 0, "data": "{\"output\":\"image_url\"}", "msg": "Success"}

            # 1. 首先检查响应是否成功
            if workflow_result.get("code") != 0:
                logger.error(f"工作流执行失败，code: {workflow_result.get('code')}, msg: {workflow_result.get('msg')}")
                return None

            # 2. 获取data字段
            data_field = workflow_result.get("data")
            if not data_field:
                logger.error("工作流响应中缺少data字段")
                return None

            # 3. 解析data字段（它是一个JSON字符串）
            try:
                if isinstance(data_field, str):
                    # data是JSON字符串，需要解析
                    parsed_data = json.loads(data_field)
                    logger.debug(f"解析data字段成功: {parsed_data}")
                else:
                    # data已经是字典
                    parsed_data = data_field
                    logger.debug(f"data字段已是字典: {parsed_data}")

                # 4. 从解析后的数据中提取output字段
                if isinstance(parsed_data, dict) and "output" in parsed_data:
                    image_url = parsed_data["output"]
                    if isinstance(image_url, str) and image_url.strip():
                        image_url = image_url.strip()
                        # 验证是否为有效的URL
                        if image_url.startswith(('http://', 'https://')):
                            logger.info(f"成功提取图片URL: {image_url}")
                            return image_url
                        else:
                            logger.warning(f"提取到的不是有效URL: {image_url}")
                    else:
                        logger.warning(f"output字段不是有效字符串: {image_url}")
                else:
                    logger.warning(f"解析后的数据中没有output字段: {parsed_data}")

            except json.JSONDecodeError as e:
                logger.error(f"解析data字段JSON失败: {e}, data内容: {data_field}")

            # 5. 如果上述方法失败，尝试其他可能的路径
            logger.info("尝试其他可能的图片URL路径...")
            fallback_paths = [
                ["data", "image_url"],
                ["data", "url"],
                ["output"],
                ["image_url"],
                ["url"],
                ["result", "output"],
                ["result", "image_url"]
            ]

            for path in fallback_paths:
                try:
                    current = workflow_result
                    for key in path:
                        current = current[key]

                    if isinstance(current, str) and current.strip():
                        current = current.strip()
                        if current.startswith(('http://', 'https://')):
                            logger.info(f"从备用路径 {' -> '.join(path)} 提取到图片URL: {current}")
                            return current
                except (KeyError, TypeError, AttributeError):
                    continue

            # 6. 最后尝试递归搜索
            image_url = cls._recursive_search_url(workflow_result)
            if image_url:
                logger.info(f"通过递归搜索找到图片URL: {image_url}")
                return image_url

            logger.warning("所有方法都未能提取到图片URL")
            return None

        except Exception as e:
            logger.error(f"提取图片URL异常: {e}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return None
    
    @classmethod
    def _recursive_search_url(cls, data: Any, max_depth: int = 5, current_depth: int = 0) -> Optional[str]:
        """
        递归搜索数据结构中的URL
        
        Args:
            data: 要搜索的数据
            max_depth: 最大搜索深度
            current_depth: 当前搜索深度
            
        Returns:
            Optional[str]: 找到的URL，如果未找到则返回None
        """
        if current_depth >= max_depth:
            return None
        
        if isinstance(data, str):
            # 检查是否为URL
            data = data.strip()
            if data.startswith(('http://', 'https://')):
                # 检查是否包含图片扩展名或者是图片服务的URL
                image_indicators = [
                    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg',
                    'image', 'img', 'pic', 'photo', 'avatar', 'thumbnail'
                ]
                if any(indicator in data.lower() for indicator in image_indicators):
                    return data
        elif isinstance(data, dict):
            # 优先检查包含image、url、picture等关键词的键
            priority_keys = ['image_url', 'imageUrl', 'image', 'url', 'picture', 'pic', 'photo']
            for key in priority_keys:
                if key in data:
                    result = cls._recursive_search_url(data[key], max_depth, current_depth + 1)
                    if result:
                        return result
            
            # 然后检查其他键
            for key, value in data.items():
                if key not in priority_keys:
                    result = cls._recursive_search_url(value, max_depth, current_depth + 1)
                    if result:
                        return result
        elif isinstance(data, list):
            for item in data:
                result = cls._recursive_search_url(item, max_depth, current_depth + 1)
                if result:
                    return result
        
        return None
