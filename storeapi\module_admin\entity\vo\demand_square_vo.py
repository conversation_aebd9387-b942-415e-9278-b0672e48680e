from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from decimal import Decimal

class DemandSquareQueryVO(BaseModel):
    """需求广场查询参数VO"""
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    business_type: Optional[int] = Field(default=None, description="业务类型：1-到家，2-到店")
    demand_status: Optional[int] = Field(default=1, description="线索状态：1-待抢单，2-已抢单")

class DemandSquareResponseVO(BaseModel):
    """需求广场响应VO"""
    id: int
    uuid: str
    platform_order_number: str
    business_type: int
    commission_amount: Decimal
    service_project: str
    service_address: str
    service_time: datetime
    demand_status: int
    grab_store_uuid: Optional[str] = None  # 抢单门店UUID
    grab_store_name: Optional[str] = None  # 抢单门店名称
    grab_user_uuid: Optional[str] = None   # 抢单用户UUID
    grab_user_name: Optional[str] = None   # 抢单用户名称
    customer_name: Optional[str] = None
    customer_phone: Optional[str] = None
    service_requirements: Optional[str] = None
    source_platform: Optional[str] = None
    expire_time: datetime
    create_time: datetime

    # 计算字段
    remaining_seconds: Optional[int] = None  # 剩余秒数
    status_text: Optional[str] = None  # 状态文本
    business_type_text: Optional[str] = None  # 业务类型文本
    
    class Config:
        from_attributes = True

class DemandSquareListResponseVO(BaseModel):
    """需求广场列表响应VO"""
    total: int = Field(description="总数量")
    page: int = Field(description="当前页码")
    size: int = Field(description="每页数量")
    pages: int = Field(description="总页数")
    list: List[DemandSquareResponseVO] = Field(description="需求列表")

class DemandSquareStatsVO(BaseModel):
    """需求广场统计VO"""
    total_count: int = Field(description="总数量")
    total_amount: Decimal = Field(description="总金额")
    home_service_count: int = Field(description="到家服务数量")
    store_service_count: int = Field(description="到店服务数量")


class DemandGrabRequestVO(BaseModel):
    """抢单请求VO"""
    demand_uuid: str = Field(description="需求UUID")
    store_uuid: str = Field(description="门店UUID")


class DemandGrabResponseVO(BaseModel):
    """抢单响应VO"""
    success: bool = Field(description="抢单是否成功")
    message: str = Field(description="响应消息")
    order_number: Optional[str] = Field(default=None, description="生成的订单号")
    demand_uuid: str = Field(description="需求UUID")
    grab_time: Optional[datetime] = Field(default=None, description="抢单时间")
    need_staff_selection: Optional[bool] = Field(default=False, description="是否需要选择员工")


class ShareOrderRequestVO(BaseModel):
    """共享订单请求VO"""
    order_id: int = Field(description="订单ID")
    order_number: str = Field(description="订单号")
    commission_amount: float = Field(description="佣金金额")
    expire_hours: int = Field(default=48, description="过期小时数")


class StaffSelectionRequestVO(BaseModel):
    """员工选择请求VO"""
    demand_uuid: str = Field(description="需求UUID")
    selected_staff_id: int = Field(description="选中的员工ID")


class AvailableStaffVO(BaseModel):
    """可用员工VO"""
    id: int = Field(description="员工ID")
    uuid: str = Field(description="员工UUID")
    real_name: str = Field(description="员工姓名")
    user_name: Optional[str] = Field(default=None, description="用户名")
    star_level: Optional[str] = Field(default="0", description="星级")
    service_cnt: Optional[int] = Field(default=0, description="服务次数")
    avatar: Optional[str] = Field(default=None, description="头像")
    status: Optional[int] = Field(default=1, description="状态")

    class Config:
        from_attributes = True
