/**
 * 前端统一错误处理工具
 * 用于标准化错误提示，提升用户体验
 */

/**
 * 错误消息映射表
 * 将后端错误消息转换为用户友好的提示
 */
const ERROR_MESSAGE_MAP = {
  // 网络相关错误
  'Network Error': '网络连接失败，请检查网络设置',
  'timeout': '请求超时，请稍后重试',
  'Request failed': '请求失败，请稍后重试',
  
  // 业务逻辑错误
  '余额不足': '余额不足，请先充值',
  '已被购买': '该线索已被其他门店购买',
  '已经共享': '该线索已经共享过，请勿重复操作',
  '权限不足': '权限不足，无法执行此操作',
  '状态不允许': '当前状态不允许此操作',
  
  // 参数验证错误
  '参数验证失败': '输入信息有误，请检查后重试',
  '必填字段': '请填写完整信息',
  '格式不正确': '输入格式不正确，请重新输入',
  
  // 系统错误
  '系统繁忙': '系统繁忙，请稍后重试',
  '服务不可用': '服务暂时不可用，请稍后重试'
}

/**
 * 统一错误处理类
 */
class ErrorHandler {
  /**
   * 处理API错误
   * @param {Error|Object} error - 错误对象
   * @param {string} operation - 操作描述（如"购买线索"、"共享订单"）
   * @param {Object} options - 配置选项
   * @returns {string} - 用户友好的错误消息
   */
  static handleApiError(error, operation = '操作', options = {}) {
    console.error(`${operation}失败:`, error)
    
    let errorMessage = this.extractErrorMessage(error)
    let userFriendlyMessage = this.getUserFriendlyMessage(errorMessage)
    
    // 如果没有找到友好消息，使用默认格式
    if (!userFriendlyMessage) {
      userFriendlyMessage = `${operation}失败，请稍后重试`
    }
    
    // 显示错误提示（如果不是静默模式）
    if (!options.silent) {
      this.showErrorToast(userFriendlyMessage, options)
    }
    
    return userFriendlyMessage
  }
  
  /**
   * 从错误对象中提取错误消息
   * @param {Error|Object} error - 错误对象
   * @returns {string} - 错误消息
   */
  static extractErrorMessage(error) {
    if (typeof error === 'string') {
      return error
    }
    
    if (error && typeof error === 'object') {
      // 优先级：msg > message > data.msg > data.message
      return error.msg || 
             error.message || 
             (error.data && error.data.msg) ||
             (error.data && error.data.message) ||
             error.toString()
    }
    
    return '未知错误'
  }
  
  /**
   * 获取用户友好的错误消息
   * @param {string} technicalMessage - 技术错误消息
   * @returns {string|null} - 用户友好的消息，如果没有匹配返回null
   */
  static getUserFriendlyMessage(technicalMessage) {
    if (!technicalMessage) return null
    
    // 检查是否包含映射表中的关键词
    for (const [key, friendlyMsg] of Object.entries(ERROR_MESSAGE_MAP)) {
      if (technicalMessage.includes(key)) {
        return friendlyMsg
      }
    }
    
    // 检查常见的业务错误模式
    if (technicalMessage.includes('余额') && technicalMessage.includes('不足')) {
      return ERROR_MESSAGE_MAP['余额不足']
    }
    
    if (technicalMessage.includes('已购买') || technicalMessage.includes('已被购买')) {
      return ERROR_MESSAGE_MAP['已被购买']
    }
    
    if (technicalMessage.includes('已共享')) {
      return ERROR_MESSAGE_MAP['已经共享']
    }
    
    if (technicalMessage.includes('权限')) {
      return ERROR_MESSAGE_MAP['权限不足']
    }
    
    if (technicalMessage.includes('状态') && technicalMessage.includes('不允许')) {
      return ERROR_MESSAGE_MAP['状态不允许']
    }
    
    return null
  }
  
  /**
   * 显示错误提示
   * @param {string} message - 错误消息
   * @param {Object} options - 配置选项
   */
  static showErrorToast(message, options = {}) {
    const config = {
      title: message,
      icon: 'none',
      duration: options.duration || 3000,
      ...options.toastConfig
    }
    
    uni.showToast(config)
  }
  
  /**
   * 处理网络错误
   * @param {Error} error - 网络错误
   * @param {string} operation - 操作描述
   * @returns {string} - 错误消息
   */
  static handleNetworkError(error, operation = '操作') {
    console.error(`${operation}网络错误:`, error)
    
    let message = '网络连接失败，请检查网络设置'
    
    if (error.message) {
      if (error.message.includes('timeout')) {
        message = '请求超时，请稍后重试'
      } else if (error.message.includes('Network Error')) {
        message = '网络连接失败，请检查网络设置'
      }
    }
    
    this.showErrorToast(message)
    return message
  }
  
  /**
   * 处理业务错误
   * @param {Object} response - API响应
   * @param {string} operation - 操作描述
   * @returns {string} - 错误消息
   */
  static handleBusinessError(response, operation = '操作') {
    const errorMessage = this.extractErrorMessage(response)
    return this.handleApiError({ message: errorMessage }, operation)
  }
}

/**
 * 统一成功处理类
 */
class SuccessHandler {
  /**
   * 显示成功提示
   * @param {string} message - 成功消息
   * @param {Object} options - 配置选项
   */
  static showSuccessToast(message, options = {}) {
    const config = {
      title: message,
      icon: 'success',
      duration: options.duration || 2000,
      ...options.toastConfig
    }
    
    uni.showToast(config)
  }
  
  /**
   * 处理操作成功
   * @param {string} operation - 操作描述
   * @param {Object} options - 配置选项
   */
  static handleSuccess(operation = '操作', options = {}) {
    const message = options.message || `${operation}成功`
    
    if (!options.silent) {
      this.showSuccessToast(message, options)
    }
    
    return message
  }
}

// 导出工具类
export { ErrorHandler, SuccessHandler }

// 默认导出ErrorHandler（向后兼容）
export default ErrorHandler
