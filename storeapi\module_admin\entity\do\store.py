"""
门店数据模型
用于门店信息的数据库映射
"""
from sqlalchemy import Column, String, Integer, Text, DateTime, BigInteger
from config.database import Base
from datetime import datetime


class Store(Base):
    """门店信息表"""

    __tablename__ = 'store'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='门店ID')
    
    # 基本信息
    store_uuid = Column(String(255), nullable=False, unique=True, index=True, comment='门店编号')
    company_id = Column(String(255), nullable=True, index=True, comment='公司id')
    name = Column(String(100), nullable=False, comment='门店名称')
    phone = Column(String(20), nullable=True, comment='联系电话')
    mobile = Column(String(20), nullable=True, comment='门店电话')
    address = Column(String(64), nullable=True, comment='地址')
    business_hours = Column(String(255), nullable=True, comment='营业时间')
    manager = Column(String(50), nullable=True, comment='负责人')
    
    # 状态信息
    status = Column(Integer, default=1, comment='状态：0-关闭，1-营业')
    store_status = Column(Integer, default=1, comment='门店状态')
    
    # 扩展信息
    remark = Column(String(255), nullable=True, comment='备注')
    introduce = Column(Text, nullable=True, comment='门店简介')
    email = Column(String(100), nullable=True, comment='邮箱')
    is_new = Column(Integer, nullable=True, comment='是否新店')
    level = Column(String(10), nullable=True, comment='门店等级')
    flag = Column(Integer, default=0, comment='标记')
    is_show_wxapp = Column(Integer, default=1, comment='是否显示在微信小程序')
    open_shop_name = Column(String(100), nullable=True, comment='开放店铺名称')
    open_shop_uuid = Column(String(255), nullable=True, comment='第三方关联门店ID')
    dredge_date = Column(DateTime, nullable=True, comment='开通日期')
    expiry_date = Column(DateTime, nullable=True, comment='到期日期')
    insurance_company = Column(String(100), nullable=True, comment='保险公司')
    user_count = Column(Integer, nullable=True, comment='用户数量')

    # 小程序相关
    mini_qr_url = Column(String(500), nullable=True, comment='小程序码URL')

    # 系统字段
    is_delete = Column(Integer, default=0, comment='是否删除(1为已删除，0为未删除)')
    is_xyj = Column(Integer, nullable=True, comment='是否小羽佳关联')
    newhome_id = Column(String(50), nullable=True, comment='新家ID')
    created_by = Column(String(64), nullable=True, comment='创建人')
    updated_by = Column(String(64), nullable=True, comment='修改人')
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    def __repr__(self):
        return f"<Store(id={self.id}, name='{self.name}', store_uuid='{self.store_uuid}')>"
