<template>
  <view class="detail-page">
    <!-- 页面加载动画 -->
    <view class="page-loading" v-if="loading">
      <view class="loading-container">
        <view class="loading-spinner">
          <u-loading-icon mode="circle" size="50" color="#fdd118"></u-loading-icon>
        </view>
        <text class="loading-text">正在加载线索详情...</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-container" v-else>
      <!-- 顶部背景区域 -->
      <view class="header-background-section">
        <view class="header-background"></view>

        <!-- 导航栏 -->
        <view class="header-content">
          <view class="navbar">
            <view class="nav-left" @click="goBack">
              <u-icon name="arrow-left" size="20" color="#fff"></u-icon>
            </view>
            <view class="nav-title">
              <text class="title-text">线索详情（编号:{{ orderDetail.id || '0000' }}）</text>
            </view>
            <view class="nav-right">
              <u-icon name="share" size="18" color="#fff" style="margin-right: 20rpx;"></u-icon>
              <u-icon name="more-dot-fill" size="18" color="#fff"></u-icon>
            </view>
          </view>

          <!-- 客户信息卡片 -->
          <view class="customer-info-card">
          <!-- 卡片头部 -->
          <view class="card-header">
            <view class="header-left">
              <view class="source-tag">{{ getSourceDisplayName() }}</view>
              <text class="customer-name">{{ orderDetail.name || '未命名客户' }}｜{{ orderDetail.aunt_name || '家政服务' }}</text>
            </view>
            <view class="header-right">
              <view class="add-contract-btn" @click="addContract">
                <text class="btn-text">添加合同</text>
              </view>
              <view class="phone-btn" @click="handleCall">
                <u-icon name="phone-fill" size="20" color="#fff"></u-icon>
              </view>
            </view>
          </view>



          <!-- 地址信息 -->
          <view class="address-section" v-if="orderDetail.address" @click="goToCustomerDetail">
            <text class="address-label">地址：</text>
            <text class="address-text">{{ orderDetail.address }}</text>
            <u-icon name="arrow-right" size="12" color="#999"></u-icon>
          </view>
          <view class="remark-section" v-if="orderDetail.requirements">
            <text class="remark-label">备注：</text>
            <text class="remark-text">{{ orderDetail.requirements }}</text>
          </view>

          <!-- 标签区域 -->
          <view class="tags-section">
            <!-- 现有标签 -->
            <view
              class="tag-item"
              v-for="tag in orderDetail.tags"
              :key="tag.id"
              :style="{ backgroundColor: tag.color || '#1890FF' }"
              v-if="orderDetail.tags && orderDetail.tags.length > 0"
            >
              <text class="tag-text">{{ tag.name }}</text>
            </view>
            <!-- 添加标签按钮 -->
            <view class="tag-item add-tag" @click="addTag">
              <u-icon name="plus" size="12" color="#999"></u-icon>
              <text class="tag-text">标签</text>
            </view>
          </view>

          <!-- 已选择的阿姨信息 -->
          <view class="accepted-aunt-info" v-if="orderDetail.accepted_aunt_uuid">
            <view class="accepted-aunt-header">
              <view class="accepted-icon">
                <u-icon name="checkmark-circle" size="16" color="#52c41a"></u-icon>
              </view>
              <text class="accepted-title">已选择接单阿姨</text>
            </view>
            <view class="accepted-aunt-details">
              <view class="aunt-info-row">
                <text class="aunt-name">{{ orderDetail.accepted_aunt_name || '未知阿姨' }}</text>
                <view class="contact-aunt-btn" @click="contactAcceptedAunt" v-if="orderDetail.accepted_aunt_mobile">
                  <u-icon name="phone" size="14" color="#fff"></u-icon>
                  <text class="contact-text">联系</text>
                </view>
              </view>
              <text class="accepted-time" v-if="orderDetail.accepted_time">
                选择时间：{{ formatTime(orderDetail.accepted_time) }}
              </text>
            </view>
          </view>

          <!-- 销售信息 -->
          <view class="sales-info merged-sales-info">
            <view class="sales-row" style="display: flex; align-items: center; padding: 8rpx 0;">
              <view class="sales-item-half" style="flex: 1; display: flex; align-items: center; min-width: 0;" @click.stop="handleSalesOwner('sales')">
                <text class="sales-label" style="font-size: 28rpx; color: #666; margin-right: 12rpx; white-space: nowrap; flex-shrink: 0;">销售归属：</text>
                <text class="sales-value" style="font-size: 28rpx; color: #333; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; flex: 1;">{{ orderDetail.sales_owner_name || '暂无' }}</text>
                <u-icon name="arrow-right" color="#999" size="16" style="margin-left: 8rpx;"></u-icon>
              </view>
              <view class="sales-item-half" style="flex: 1; display: flex; align-items: center; margin-left: 32rpx; min-width: 0;" @click.stop="handleSalesOwner('afterSales')">
                <text class="sales-label" style="font-size: 28rpx; color: #666; margin-right: 12rpx; white-space: nowrap; flex-shrink: 0;">售后归属：</text>
                <text class="sales-value" style="font-size: 28rpx; color: #333; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; flex: 1;">{{ orderDetail.after_sales_owner_name || '暂无' }}</text>
                <u-icon name="arrow-right" color="#999" size="16" style="margin-left: 8rpx;"></u-icon>
              </view>
            </view>
          </view>

          <!-- 功能按钮区域 -->
          <view class="function-buttons">
            <view class="function-btn customer-insight-btn" @click="handleCustomerInsight">
              <u-icon name="eye" size="18" color="#1976d2" style="margin-right: 8rpx;"></u-icon>
              <text class="btn-text">客户洞察</text>
            </view>
            <view class="function-btn housework-list-btn" @click="handleHouseworkList">
              <u-icon name="list" size="18" color="#388e3c" style="margin-right: 8rpx;"></u-icon>
              <text class="btn-text">家务清单</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 标签页导航 -->
    <view class="tab-nav">
      <view
        class="tab-item"
        :class="{ active: currentTab === index }"
        v-for="(tab, index) in tabs"
        :key="index"
        @click="switchTab(index)"
      >
        {{ tab }}
      </view>
    </view>

    <!-- 主要内容 -->
    <scroll-view scroll-y class="main-content">
      <!-- 发单/面试 -->
      <view v-if="currentTab === 0" class="tab-content dispatch-interview-tab">
        <!-- 一键发单功能 -->
        <view class="section-card">
          <view class="section-title">一键发单</view>
          <view class="dispatch-actions">
            <!-- 如果还没有生成分享链接，显示生成按钮 -->
            <button
              v-if="!shareUrl"
              class="dispatch-btn"
              @click="handleDispatchOrder"
              :disabled="dispatchLoading"
            >
              <u-icon name="share" size="16" color="#fff" style="margin-right: 8rpx;"></u-icon>
              {{ dispatchLoading ? '生成中...' : '生成分享链接' }}
            </button>

            <!-- 如果已经生成分享链接，显示分享按钮 -->
            <button
              v-else
              class="dispatch-btn share-btn"
              open-type="share"
              @click="prepareShare"
            >
              <u-icon name="share" size="16" color="#fff" style="margin-right: 8rpx;"></u-icon>
              分享发单
            </button>

            <text class="dispatch-tip" v-if="!shareUrl">点击生成专属分享链接</text>
            <text class="dispatch-tip" v-else>点击分享给微信好友，让阿姨申请接单</text>
          </view>
        </view>

        <!-- 申请接单人员列表 -->
        <view class="section-card">
          <view class="applications-header">
            <view class="section-title">
              申请接单人员 ({{ applicationList.length }})
            </view>
            <button class="refresh-btn" @click="loadApplications" :disabled="applicationsLoading">
              <u-icon name="reload" size="14" color="#666"></u-icon>
            </button>
          </view>

          <!-- 加载状态 -->
          <view class="applications-loading" v-if="applicationsLoading">
            <u-loading-icon mode="circle" size="20" color="#fdd118"></u-loading-icon>
            <text>加载中...</text>
          </view>

          <!-- 申请列表 -->
          <view class="applications-list" v-else-if="applicationList.length > 0">
            <view
              class="application-item"
              v-for="(application, index) in applicationList"
              :key="application.uuid"
              :class="{ 'accepted': application.status === 'accepted' }"
            >
              <view class="application-info">
                <view class="aunt-basic">
                  <view class="aunt-name">{{ application.aunt_name || '未知' }}</view>
                  <view class="aunt-mobile">{{ formatMobile(application.aunt_mobile) }}</view>
                </view>
                <view class="aunt-details" v-if="application.aunt_age || application.aunt_experience_years">
                  <text v-if="application.aunt_age">{{ application.aunt_age }}岁</text>
                  <text v-if="application.aunt_experience_years">{{ application.aunt_experience_years }}年经验</text>
                  <text v-if="application.aunt_hometown">{{ application.aunt_hometown }}</text>
                </view>
                <view class="application-time">申请时间：{{ application.application_time }}</view>
              </view>

              <view class="application-actions">
                <!-- 待处理状态 -->
                <view class="action-buttons" v-if="application.status === 'pending'">
                  <view class="interview-btn" @click="scheduleInterview(application)">
                    <u-icon name="calendar" size="14" color="#fff"></u-icon>
                    <text class="btn-text">面试</text>
                  </view>
                  <view class="contact-btn" @click="callAunt(application.aunt_mobile)">
                    <u-icon name="phone" size="14" color="#fff"></u-icon>
                    <text class="btn-text">联系</text>
                  </view>
                </view>

                <!-- 已接受状态 -->
                <view class="status-accepted" v-else-if="application.status === 'accepted'">
                  <view class="status-tag accepted">
                    <u-icon name="checkmark-circle" size="12" color="#52c41a"></u-icon>
                    已选择
                  </view>
                  <view class="contact-btn" @click="callAunt(application.aunt_mobile)">
                    <u-icon name="phone" size="14" color="#fff"></u-icon>
                    <text class="btn-text">联系</text>
                  </view>
                </view>

                <!-- 已拒绝状态 -->
                <view class="status-rejected" v-else>
                  <view class="status-tag rejected">
                    <u-icon name="close-circle" size="12" color="#999"></u-icon>
                    未选择
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="applications-empty" v-else>
            <u-icon name="man" size="40" color="#ccc"></u-icon>
            <text>暂无申请接单人员</text>
            <text class="empty-tip">分享线索后，阿姨申请接单会显示在这里</text>
          </view>
        </view>

        <!-- 面试记录 -->
        <view class="section-card">
          <view class="section-title">面试记录：</view>

          <!-- 面试记录列表 -->
          <view class="interview-list" v-if="interviewList.length > 0">
            <view
              class="interview-item"
              v-for="interview in interviewList"
              :key="interview.uuid"
            >
              <view class="interview-header">
                <text class="aunt-name">{{ interview.aunt_name }}</text>
                <view class="interview-status" :class="interview.status">
                  <text class="status-text">{{ getInterviewStatusText(interview.status) }}</text>
                </view>
              </view>
              <view class="interview-details">
                <text class="interview-time">面试时间：{{ formatInterviewTime(interview.interview_time) }}</text>
                <text class="interview-type">面试方式：{{ interview.interview_type === 'online' ? '视频面试' : '线下面试' }}</text>
                <text class="interview-remark" v-if="interview.interview_remark">备注：{{ interview.interview_remark }}</text>
              </view>
              <view class="interview-actions">
                <!-- 选择按钮：仅在未选择接单人员时显示 -->
                <view
                  class="select-btn"
                  @click="selectInterviewAunt(interview)"
                  v-if="!orderDetail.accepted_aunt_uuid"
                >
                  <u-icon name="checkmark" size="12" color="#fff"></u-icon>
                  <text class="btn-text">选择</text>
                </view>
                <view class="contact-btn" @click="callAunt(interview.aunt_mobile)" v-if="interview.aunt_mobile">
                  <u-icon name="phone" size="12" color="#fff"></u-icon>
                  <text class="btn-text">联系</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" v-else>
            <u-icon name="calendar" size="40" color="#ccc"></u-icon>
            <text class="empty-text">暂无面试记录</text>
            <text class="empty-tip">点击申请人员的"面试"按钮安排面试</text>
          </view>
        </view>
      </view>

      <!-- 跟进记录 -->
      <view v-if="currentTab === 1" class="tab-content follow-tab">
        <!-- 加载状态 -->
        <view class="loading-records" v-if="followLoading">
          <u-loading-icon mode="spinner" size="40" color="#fdd118"></u-loading-icon>
          <text class="loading-text">加载跟进记录中...</text>
        </view>

        <!-- 跟进记录列表 -->
        <view class="follow-records-container" v-else-if="filteredFollowRecords.length > 0">
          <view
            class="follow-record-item"
            v-for="(record, index) in filteredFollowRecords"
            :key="record.id"
          >
            <!-- 记录头部：操作者和时间 -->
            <view class="record-header">
              <view class="record-author">
                <text class="author-name">{{ record.user_name || '系统' }}</text>
                <text class="record-time">{{ formatRecordTime(record.create_time) }}</text>
              </view>
              <view class="record-actions">
                <text
                  class="action-btn-text"
                  :data-status="record.status || '1'"
                >{{ record.status_name || '待跟进' }}</text>
              </view>
            </view>

            <!-- 记录内容 -->
            <view class="record-content">
              <text class="content-text">{{ record.follow_content }}</text>
            </view>

            <!-- 跟进记录图片 -->
            <view class="record-images" v-if="record.images && record.images.length > 0">
              <text class="images-label">[ 图片 ]</text>
              <view class="images-grid">
                <view
                  class="record-image-item"
                  v-for="(image, imgIndex) in record.images"
                  :key="imgIndex"
                  @click="previewRecordImage(record.images, imgIndex)"
                >
                  <image :src="image.url" mode="aspectFill" class="record-image"></image>
                </view>
              </view>
            </view>

            <!-- 下次跟进时间 -->
            <view class="next-follow-info" v-if="record.next_follow_time">
              <text class="next-follow-text">下次跟进: {{ formatRecordTime(record.next_follow_time) }}</text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-records" v-else>
          <u-icon name="chat" size="60" color="#ddd"></u-icon>
          <text class="empty-text" v-if="followRecords.length === 0">暂无跟进记录</text>
          <text class="empty-text" v-else>该状态下暂无跟进记录</text>
          <text class="empty-tip" v-if="followRecords.length === 0">点击下方按钮添加第一条跟进记录</text>
          <text class="empty-tip" v-else>请选择其他状态或添加新的跟进记录</text>
        </view>

        <!-- 底部输入区域 -->
        <view class="bottom-input-area">
          <view class="input-container">
            <view class="input-left" @click="showContractStatusSelector">
              <text class="status-tag">{{ currentStatusTagText }}</text>
            </view>
            <view class="input-center" @click="handleUpdateStatus">
              <text class="input-placeholder">输入跟进信息（必填）</text>
            </view>
            <view class="input-right" @click="handleUpdateStatus">
              <u-icon name="plus-circle" size="24" color="#fdd118"></u-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 合同记录 -->
      <view v-if="currentTab === 2" class="tab-content">
        <!-- 加载状态 -->
        <view v-if="contractLoading" class="loading-container">
          <u-loading-icon mode="spinner" size="40" color="#fdd118"></u-loading-icon>
          <text class="loading-text">加载合同记录中...</text>
        </view>

        <!-- 合同记录列表 -->
        <view v-else-if="contractList.length > 0" class="contract-container">
          <view
            v-for="contract in contractList"
            :key="contract.id"
            class="contract-card"
            @click="handleContractClick(contract)"
          >
            <!-- 合同基本信息 -->
            <view class="contract-header">
              <text class="contract-number">{{ contract.self_number || contract.number || '合同编号未知' }}</text>
              <text class="contract-status">{{ contract.status || '状态未知' }}</text>
            </view>

            <!-- 合同详细信息 -->
            <view class="contract-body">
              <text class="contract-type">类型：{{ contract.contract_type_name || '未知类型' }}</text>
              <text class="contract-amount">金额：¥{{ contract.amount || '0.00' }}</text>
              <text v-if="contract.service_end_time" class="contract-time">
                服务结束：{{ formatDate(contract.service_end_time) }}
              </text>
            </view>

            <!-- 合同创建时间 -->
            <view class="contract-footer">
              <text class="create-time">
                创建时间：{{ formatDate(contract.created_at || contract.create_time) }}
              </text>
              <u-icon name="arrow-right" size="16" color="#999"></u-icon>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-container">
          <u-icon name="file-text" size="40" color="#ccc"></u-icon>
          <text class="empty-text">暂无合同记录</text>
          <text class="empty-tip">该客户还没有签订合同</text>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 跟进记录弹窗 -->
    <u-popup :show="showStatusModal" mode="bottom" @close="showStatusModal = false">
      <view class="status-modal">
        <view class="modal-header">
          <text class="modal-title">添加跟进记录</text>
        </view>
        <view class="modal-content">
          <view class="status-options">
            <view
              class="status-option"
              :class="{ active: selectedStatus === status.value }"
              v-for="status in statusOptions"
              :key="status.value"
              @click="selectedStatus = status.value"
            >
              {{ status.label }}
            </view>
          </view>
          <view class="follow-section">
            <text class="follow-label">跟进内容</text>
            <textarea
              class="follow-input"
              v-model="followContent"
              placeholder="请输入本次跟进的具体内容"
              maxlength="500"
            ></textarea>
          </view>
          <view class="next-follow-section">
            <text class="next-follow-label">下次跟进时间</text>
            <u-datetime-picker
              :show="showDatePicker"
              v-model="nextFollowTime"
              mode="datetime"
              :min-date="new Date().getTime()"
              @confirm="onDateConfirm"
              @cancel="showDatePicker = false"
            ></u-datetime-picker>
            <view class="date-input" @click="showDatePicker = true">
              <text class="date-text">{{ nextFollowTimeDisplay || '选择下次跟进时间（可选）' }}</text>
              <u-icon name="calendar" size="16" color="#999"></u-icon>
            </view>
          </view>

          <!-- 图片上传区域 -->
          <view class="image-upload-section">
            <text class="upload-label">添加图片</text>
            <view class="image-upload-container">
              <!-- 已上传的图片 -->
              <view class="uploaded-images" v-if="uploadedImages.length > 0">
                <view
                  class="image-item"
                  v-for="(image, index) in uploadedImages"
                  :key="index"
                >
                  <image :src="image.url" mode="aspectFill" class="image-preview" @click="previewImage(image.url)"></image>
                  <view class="image-delete" @click="removeImage(index)">
                    <u-icon name="close" size="12" color="#fff"></u-icon>
                  </view>
                </view>
              </view>

              <!-- 上传按钮 -->
              <view class="upload-btn" @click="chooseImage" v-if="uploadedImages.length < 9">
                <u-icon name="camera" size="24" color="#999"></u-icon>
                <text class="upload-text">添加图片</text>
              </view>
            </view>
            <text class="upload-tip">最多可上传9张图片</text>
          </view>
        </view>
        <view class="modal-footer">
          <u-button type="default" @click="showStatusModal = false" custom-style="margin-right: 12px;">取消</u-button>
          <u-button type="primary" @click="confirmUpdateStatus">确定</u-button>
        </view>
      </view>
    </u-popup>

    <!-- 标签选择弹窗 -->
    <u-popup :show="showTagModal" mode="bottom" @close="showTagModal = false">
      <view class="tag-modal">
        <view class="modal-header">
          <text class="modal-title">选择标签</text>
          <view class="header-actions">
            <text class="add-new-btn" @click="showCreateTag">新增</text>
            <text class="cancel-btn" @click="cancelTagEdit">取消</text>
            <text class="confirm-btn" @click="confirmSaveTags">确定</text>
          </view>
        </view>

        <view class="modal-content">
          <!-- 已选标签区域 -->
          <view class="selected-tags-section" v-if="selectedTags.length > 0">
            <text class="section-title">已选标签</text>
            <view class="selected-tags-list">
              <view
                class="selected-tag-item"
                v-for="tagId in selectedTags"
                :key="tagId"
              >
                <text class="tag-name">{{ getTagNameById(tagId) }}</text>
                <view class="remove-tag" @click="toggleTagSelection(tagId)">
                  <u-icon name="close" size="12" color="#fff"></u-icon>
                </view>
              </view>
            </view>
          </view>

          <!-- 可选标签区域 -->
          <view class="available-tags-section">
            <text class="section-title">可选标签</text>
            <view class="available-tags-list">
              <view
                class="available-tag-item"
                v-for="tag in availableTags"
                :key="tag.id"
                :class="{ selected: selectedTags.includes(tag.id) }"
                @click="toggleTagSelection(tag.id)"
                :style="{ backgroundColor: selectedTags.includes(tag.id) ? (tag.color || '#1890FF') : '#f5f5f5' }"
              >
                <text class="tag-name" :style="{ color: selectedTags.includes(tag.id) ? '#fff' : '#333' }">{{ tag.name }}</text>
                <view class="tag-action" v-if="selectedTags.includes(tag.id)">
                  <u-icon name="checkmark" size="14" color="#fff"></u-icon>
                </view>
                <view class="tag-action" v-else>
                  <u-icon name="plus" size="14" color="#999"></u-icon>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 新增标签弹窗 -->
    <u-popup :show="showCreateTagModal" mode="center" @close="cancelCreateTag">
      <view class="create-tag-modal">
        <view class="create-tag-header">
          <text class="create-tag-title">新增标签</text>
        </view>

        <view class="create-tag-content">
          <!-- 标签名称 -->
          <view class="form-item">
            <text class="form-label">标签名称</text>
            <input
              class="form-input"
              v-model="newTagData.name"
              placeholder="请输入标签名称"
              :maxlength="10"
            />
          </view>

          <!-- 标签颜色 -->
          <view class="form-item">
            <text class="form-label">标签颜色</text>
            <view class="color-picker">
              <view
                class="color-item"
                v-for="color in predefinedColors"
                :key="color"
                :style="{ backgroundColor: color }"
                :class="{ selected: newTagData.color === color }"
                @click="selectColor(color)"
              >
                <u-icon name="checkmark" size="16" color="#fff" v-if="newTagData.color === color"></u-icon>
              </view>
            </view>
          </view>

          <!-- 预览 -->
          <view class="form-item">
            <text class="form-label">预览效果</text>
            <view class="tag-preview">
              <view
                class="preview-tag"
                :style="{ backgroundColor: newTagData.color || '#1890FF' }"
              >
                <text class="preview-text">{{ newTagData.name || '标签名称' }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="create-tag-buttons">
          <view class="create-tag-btn cancel" @click="cancelCreateTag">
            <text class="btn-text">取消</text>
          </view>
          <view class="create-tag-btn confirm" @click="confirmCreateTag">
            <text class="btn-text">确定</text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 销售归属员工选择弹窗 -->
    <sales-attribution-selector
      :show="showSalesAttributionSelector"
      :leadId="orderId"
      type="sales"
      @success="handleSalesAttributionSuccess"
      @close="handleAttributionSelectorClose"
    />

    <!-- 售后归属员工选择弹窗 -->
    <after-sales-attribution-selector
      :show="showAfterSalesAttributionSelector"
      :leadId="orderId"
      @success="handleAfterSalesAttributionSuccess"
      @close="handleAttributionSelectorClose"
    />

    <!-- 签约状态选择弹窗 -->
    <u-popup :show="showContractStatusModal" mode="bottom" @close="showContractStatusModal = false">
      <view class="contract-status-modal">
        <view class="modal-header">
          <text class="modal-title">选择签约状态</text>
        </view>
        <view class="modal-content">
          <view class="contract-status-options">
            <view
              class="contract-status-option"
              :class="{ active: selectedContractStatus === status.value }"
              v-for="status in contractStatusOptions"
              :key="status.value"
              @click="selectContractStatus(status.value)"
            >
              {{ status.label }}
            </view>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 面试安排弹窗 -->
    <u-popup :show="showInterviewModal" mode="bottom" @close="showInterviewModal = false">
      <view class="interview-modal">
        <view class="modal-header">
          <text class="modal-title">安排面试</text>
        </view>
        <view class="modal-content">
          <!-- 服务人员信息 -->
          <view class="interview-staff-info">
            <text class="staff-label">服务人员</text>
            <view class="staff-details">
              <text class="staff-name">{{ currentInterviewApplication.aunt_name || '未知阿姨' }}</text>
              <text class="staff-mobile">{{ currentInterviewApplication.aunt_mobile || '手机号未知' }}</text>
            </view>
          </view>

          <!-- 预约面试时间 -->
          <view class="interview-time-section">
            <text class="time-label">预约面试时间</text>
            <u-datetime-picker
              :show="showInterviewDatePicker"
              v-model="interviewTime"
              mode="datetime"
              :min-date="new Date().getTime()"
              :max-date="new Date(new Date().getFullYear() + 1, 11, 31).getTime()"
              @confirm="onInterviewDateConfirm"
              @cancel="showInterviewDatePicker = false"
            ></u-datetime-picker>
            <view class="time-input" @click="showInterviewDatePicker = true">
              <text class="time-text">{{ interviewTimeDisplay || '选择面试时间' }}</text>
              <u-icon name="calendar" size="16" color="#999"></u-icon>
            </view>
          </view>

          <!-- 面试方式 -->
          <view class="interview-type-section">
            <text class="type-label">面试方式</text>
            <view class="type-options">
              <view
                class="type-option"
                :class="{ active: interviewType === 'offline' }"
                @click="interviewType = 'offline'"
              >
                <u-icon name="home" size="16" :color="interviewType === 'offline' ? '#fff' : '#666'"></u-icon>
                <text class="type-text">线下面试</text>
              </view>
              <view
                class="type-option"
                :class="{ active: interviewType === 'online' }"
                @click="interviewType = 'online'"
              >
                <u-icon name="movie" size="16" :color="interviewType === 'online' ? '#fff' : '#666'"></u-icon>
                <text class="type-text">视频面试</text>
              </view>
            </view>
          </view>

          <!-- 面试备注 -->
          <view class="interview-remark-section">
            <text class="remark-label">面试备注</text>
            <textarea
              class="remark-input"
              v-model="interviewRemark"
              placeholder="请输入面试相关备注信息（可选）"
              maxlength="200"
            ></textarea>
          </view>
        </view>
        <view class="modal-footer">
          <u-button type="default" @click="showInterviewModal = false" custom-style="margin-right: 12px;">取消</u-button>
          <u-button type="primary" @click="confirmScheduleInterview">确定安排</u-button>
        </view>
      </view>
    </u-popup>
    </view>
  </view>
</template>

<script>
import { getSisterBusinessDetail, updateSisterBusinessStatus, getCustomerFollowRecords, createCustomerTag } from '@/api/sister-business.js';
import { getCurrentUser } from '@/utlis/auth.js';
import SalesAttributionSelector from '@/components/sales-attribution-selector/sales-attribution-selector.vue';
import AfterSalesAttributionSelector from '@/components/after-sales-attribution-selector/after-sales-attribution-selector.vue';

export default {
  components: {
    SalesAttributionSelector,
    AfterSalesAttributionSelector
  },
  data() {
    return {
      orderId: '',
      orderDetail: {},
      loading: false,

      // 标签页相关
      currentTab: 0,
      tabs: ['发单/面试', '跟进记录', '合同记录'],

      // 跟进记录相关
      followRecords: [],
      followLoading: false,

      // 合同记录相关
      contractList: [],
      contractLoading: false,

      // 跟进记录相关
      showStatusModal: false,
      selectedStatus: '',
      followContent: '',
      nextFollowTime: null,
      showDatePicker: false,
      uploadedImages: [], // 上传的图片列表
      statusOptions: [
        { label: '待跟进', value: '1' },
        { label: '跟进中', value: '2' },
        { label: '已成交', value: '3' },
        { label: '已取消', value: '4' },
        { label: '已签约', value: '5' }
      ],

      // 标签相关
      showTagModal: false,
      availableTags: [],
      selectedTags: [],

      // 新增标签相关
      showCreateTagModal: false,
      newTagData: {
        name: '',
        color: '#1890FF'
      },
      predefinedColors: [
        '#1890FF', '#52C41A', '#FAAD14', '#F5222D',
        '#722ED1', '#13C2C2', '#EB2F96', '#FA8C16',
        '#A0D911', '#2F54EB', '#FF7A45', '#FF4D4F',
        '#9254DE', '#36CFC9', '#FFC53D', '#40A9FF'
      ],

      // 员工选择弹窗相关
      showSalesAttributionSelector: false,
      showAfterSalesAttributionSelector: false,

      // 签约状态选择相关
      showContractStatusModal: false,
      selectedContractStatus: '', // 空字符串表示显示全部
      contractStatusOptions: [
        { label: '全部', value: '' },
        { label: '待跟进', value: '1' },
        { label: '跟进中', value: '2' },
        { label: '已成交', value: '3' },
        { label: '已取消', value: '4' },
        { label: '已签约', value: '5' }
      ],

      // 面试相关
      showInterviewModal: false,
      currentInterviewApplication: {},
      interviewTime: null,
      interviewTimeSelected: false, // 标记用户是否已选择时间
      showInterviewDatePicker: false,
      interviewType: 'offline', // offline: 线下面试, online: 线上面试
      interviewRemark: '',
      interviewList: [], // 面试记录列表,

      // 发单相关
      dispatchLoading: false,
      shareUrl: '',

      // 申请接单人员相关
      applicationList: [],
      applicationsLoading: false,
    };
  },

  computed: {
    // 计算状态显示文字
    statusDisplayText() {
      console.log('计算属性 statusDisplayText 被调用');
      console.log('orderDetail:', this.orderDetail);
      console.log('orderDetail.status:', this.orderDetail.status);

      if (!this.orderDetail || !this.orderDetail.status) {
        console.log('orderDetail或status为空，返回默认值');
        return '加载中...';
      }

      return this.getStatusText(this.orderDetail.status);
    },

    // 计算创建时间显示文字
    createTimeDisplayText() {
      console.log('计算属性 createTimeDisplayText 被调用');
      console.log('orderDetail:', this.orderDetail);

      if (!this.orderDetail) {
        console.log('orderDetail为空，返回加载中');
        return '加载中...';
      }

      // 尝试多种可能的时间字段名
      const timeFields = [
        'create_time',
        'created_at',
        'createTime',
        'createdAt'
      ];

      for (const field of timeFields) {
        const timeValue = this.orderDetail[field];
        console.log(`检查字段 ${field}:`, timeValue);
        if (timeValue) {
          console.log(`使用字段 ${field} 的值:`, timeValue);
          return this.formatDate(timeValue);
        }
      }

      console.log('所有时间字段都为空');
      return '暂无数据';
    },

    // 计算更新时间显示文字
    updateTimeDisplayText() {
      console.log('计算属性 updateTimeDisplayText 被调用');
      console.log('orderDetail:', this.orderDetail);

      if (!this.orderDetail) {
        console.log('orderDetail为空，返回加载中');
        return '加载中...';
      }

      // 尝试多种可能的更新时间字段名
      const timeFields = [
        'update_time',
        'updated_at',
        'updateTime',
        'updatedAt'
      ];

      for (const field of timeFields) {
        const timeValue = this.orderDetail[field];
        console.log(`检查更新时间字段 ${field}:`, timeValue);
        if (timeValue) {
          console.log(`使用更新时间字段 ${field} 的值:`, timeValue);
          return this.formatDate(timeValue);
        }
      }

      console.log('所有更新时间字段都为空');
      return '暂无数据';
    },

    // 计算下次跟进时间显示文字
    nextFollowTimeDisplay() {
      if (!this.nextFollowTime) return '';
      return this.formatDate(this.nextFollowTime);
    },

    // 计算状态样式类
    statusClass() {
      if (!this.orderDetail || !this.orderDetail.status) {
        return '';
      }

      const statusMap = {
        '1': 'status-pending',
        '2': 'status-following',
        '3': 'status-success',
        '4': 'status-cancelled',
        '5': 'status-signed'
      };

      return statusMap[this.orderDetail.status] || '';
    },

    // 计算是否有合同记录
    hasContracts() {
      return this.contractList && Array.isArray(this.contractList) && this.contractList.length > 0;
    },

    // 计算当前状态标签显示文本
    currentStatusTagText() {
      // 根据选中的筛选状态显示标签文本
      const statusOption = this.contractStatusOptions.find(option => option.value === this.selectedContractStatus);
      const statusName = statusOption ? statusOption.label : '全部';

      // 如果有跟进记录，显示筛选后的数量
      if (this.followRecords && this.followRecords.length > 0) {
        const filteredCount = this.filteredFollowRecords.length;
        return `${statusName}(${filteredCount})↓`;
      }

      return `${statusName}↓`;
    },

    // 计算过滤后的跟进记录
    filteredFollowRecords() {
      if (!this.followRecords || this.followRecords.length === 0) {
        return [];
      }

      // 如果没有选择筛选状态，显示所有记录
      if (!this.selectedContractStatus) {
        return this.followRecords;
      }

      // 根据选择的状态筛选记录
      return this.followRecords.filter(record => {
        // 记录的状态字段可能是 status 或 status_code
        const recordStatus = record.status || record.status_code || '1';
        return recordStatus.toString() === this.selectedContractStatus.toString();
      });
    },

    // 面试时间显示
    interviewTimeDisplay() {
      // 只有用户选择了时间才显示，否则显示空字符串让输入框显示占位符
      if (!this.interviewTime || !this.interviewTimeSelected) return '';
      const date = new Date(this.interviewTime);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    }
  },

  onLoad(options) {
    if (options.id) {
      this.orderId = options.id;
      this.loadOrderDetail();
      this.loadApplications();
      this.loadInterviews(); // 添加面试记录加载
    }

    // 如果传入了tab参数，直接跳转到对应标签页
    if (options.tab !== undefined) {
      const tabIndex = parseInt(options.tab);
      if (tabIndex >= 0 && tabIndex < this.tabs.length) {
        this.currentTab = tabIndex;

        // 如果是跟进记录tab且传入了autoAdd参数，自动弹出添加跟进表单
        if (tabIndex === 1 && options.autoAdd === 'true') {
          // 延迟一下确保页面加载完成
          this.$nextTick(() => {
            setTimeout(() => {
              this.autoShowAddFollow();
            }, 300);
          });
        }
      }
    }

  },

  onShow() {
    // 检查是否需要刷新销售归属信息
    this.checkAndRefreshSalesAttribution();

    // 检查是否需要刷新合同记录
    this.checkAndRefreshContractRecords();

    // 如果当前在合同记录标签页，刷新合同记录
    if (this.currentTab === 2 && this.orderDetail.uuid) {
      console.log('页面显示时检测到在合同记录标签页，刷新合同记录');
      this.loadContractRecords();
    }

    // 自动刷新申请接单人员列表和面试记录
    if (this.orderDetail.uuid) {
      console.log('页面显示时自动刷新申请接单人员列表和面试记录');
      this.loadApplications();
      this.loadInterviews();
    }
  },



  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 获取来源显示名称（修复：统一来源映射逻辑）
    getSourceDisplayName() {
      const sourceMap = {
        '1': '线上推广',
        '2': '线下推广',
        '3': '老客户介绍',
        '4': '广告投放',
        '5': '门店咨询',
        '6': '电话咨询',
        '7': '其他渠道',
        '8': '微网站',
        '9': '广场抢单',
        '-1': '客户转介绍',
        '-2': '公司400电话',
        '-3': '系统分配',
        '-4': '公司指派'
      };
      return sourceMap[this.orderDetail.source] || '其他渠道';
    },

    // 处理生成分享链接
    async handleDispatchOrder() {
      if (!this.orderDetail.uuid) {
        uni.showToast({
          title: '线索信息不完整',
          icon: 'none'
        });
        return;
      }

      this.dispatchLoading = true;

      try {
        // 调用API生成分享链接
        const { generateShareLink } = await import('@/api/sister-business.js');
        const response = await generateShareLink({
          customer_uuid: this.orderDetail.uuid
        });

        if (response && response.share_url) {
          this.shareUrl = response.share_url;

          uni.showToast({
            title: '分享链接生成成功',
            icon: 'success'
          });
        } else {
          throw new Error('生成分享链接失败');
        }
      } catch (error) {
        console.error('生成分享链接失败:', error);
        uni.showToast({
          title: '生成分享链接失败',
          icon: 'none'
        });
      } finally {
        this.dispatchLoading = false;
      }
    },

    // 准备分享（当点击分享按钮时）
    prepareShare() {
      // 这个方法在用户点击分享按钮时调用
      // 实际的分享内容由 onShareAppMessage 方法提供
      console.log('准备分享，分享链接:', this.shareUrl);
    },

    // 加载申请接单人员列表
    async loadApplications() {
      if (!this.orderDetail.uuid) {
        return;
      }

      this.applicationsLoading = true;

      try {
        const { getCustomerApplications } = await import('@/api/sister-business.js');
        const response = await getCustomerApplications(this.orderDetail.uuid);

        if (response && Array.isArray(response)) {
          this.applicationList = response;
        } else {
          this.applicationList = [];
        }
      } catch (error) {
        console.error('加载申请记录失败:', error);
        uni.showToast({
          title: '加载申请记录失败',
          icon: 'none'
        });
        this.applicationList = [];
      } finally {
        this.applicationsLoading = false;
      }
    },



    // 拨打电话
    callAunt(mobile) {
      if (!mobile) {
        uni.showToast({
          title: '手机号不可用',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: mobile,
        fail: (error) => {
          console.error('拨打电话失败:', error);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },

    // 格式化手机号显示
    formatMobile(mobile) {
      if (!mobile) return '手机号未提供';

      // 隐藏中间4位数字
      if (mobile.length === 11) {
        return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
      }

      return mobile;
    },

    // 联系接单阿姨
    contactAunt() {
      if (!this.orderDetail.accepted_aunt_mobile) {
        uni.showToast({
          title: '阿姨手机号不可用',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: this.orderDetail.accepted_aunt_mobile,
        fail: (err) => {
          console.error('拨打电话失败:', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },

    // 联系已选择的阿姨（从订单详情头部）
    contactAcceptedAunt() {
      if (!this.orderDetail.accepted_aunt_mobile) {
        uni.showToast({
          title: '阿姨手机号不可用',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: this.orderDetail.accepted_aunt_mobile,
        fail: (err) => {
          console.error('拨打电话失败:', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },

    // 安排面试
    scheduleInterview(application) {
      this.currentInterviewApplication = application;
      // 设置默认面试时间为当前日期时间（用于时间选择器的默认值）
      const now = new Date();
      // 设置为今天的默认时间（比如上午9点）
      now.setHours(9, 0, 0, 0);
      this.interviewTime = now.getTime();
      console.log('设置的面试时间戳:', this.interviewTime, '对应日期:', new Date(this.interviewTime));
      this.interviewTimeSelected = false; // 重置选择状态
      this.interviewType = 'offline';
      this.interviewRemark = '';
      this.showInterviewModal = true;
    },

    // 面试时间确认
    onInterviewDateConfirm(value) {
      this.interviewTime = value;
      this.interviewTimeSelected = true; // 标记用户已选择时间
      this.showInterviewDatePicker = false;
    },

    // 确认安排面试
    async confirmScheduleInterview() {
      if (!this.interviewTime) {
        uni.showToast({
          title: '请选择面试时间',
          icon: 'none'
        });
        return;
      }

      try {
        uni.showLoading({
          title: '安排面试中...'
        });

        // 构建面试数据
        const interviewData = {
          customer_uuid: this.orderDetail.uuid,
          aunt_uuid: this.currentInterviewApplication.aunt_uuid,
          aunt_name: this.currentInterviewApplication.aunt_name,
          aunt_mobile: this.currentInterviewApplication.aunt_mobile,
          interview_time: this.interviewTime,
          interview_type: this.interviewType,
          interview_remark: this.interviewRemark
        };

        // 调用API创建面试记录
        const { createInterview } = await import('@/api/sister-business.js');
        const response = await createInterview(interviewData);

        if (response && response.success) {
          uni.showToast({
            title: '面试安排成功',
            icon: 'success'
          });

          // 关闭弹窗
          this.showInterviewModal = false;

          // 重新加载面试记录
          await this.loadInterviews();
        } else {
          throw new Error(response?.message || '安排面试失败');
        }
      } catch (error) {
        console.error('安排面试失败:', error);
        uni.showToast({
          title: error.message || '安排面试失败，请重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 加载面试记录
    async loadInterviews() {
      if (!this.orderDetail.uuid) {
        return;
      }

      try {
        const { getInterviews } = await import('@/api/sister-business.js');
        const response = await getInterviews(this.orderDetail.uuid);

        if (response && Array.isArray(response)) {
          this.interviewList = response;
        } else {
          this.interviewList = [];
        }
      } catch (error) {
        console.error('加载面试记录失败:', error);
        this.interviewList = [];
      }
    },

    // 获取面试状态文本
    getInterviewStatusText(status) {
      const statusMap = {
        'scheduled': '已安排',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || '未知状态';
    },

    // 格式化时间显示
    formatTime(timeStr) {
      if (!timeStr) return '';

      try {
        const date = new Date(timeStr);
        const now = new Date();
        const diff = now - date;
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (days === 0) {
          return '今天 ' + date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          });
        } else if (days === 1) {
          return '昨天 ' + date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          });
        } else if (days < 7) {
          return `${days}天前`;
        } else {
          return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          });
        }
      } catch (error) {
        console.error('时间格式化失败:', error);
        return timeStr;
      }
    },

    // 格式化面试时间显示（显示绝对时间）
    formatInterviewTime(timeStr) {
      if (!timeStr) return '';

      try {
        const date = new Date(timeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        console.error('面试时间格式化失败:', error);
        return timeStr;
      }
    },

    // 选择面试阿姨作为接单人员
    async selectInterviewAunt(interview) {
      try {
        const result = await uni.showModal({
          title: '确认选择',
          content: `确定选择 ${interview.aunt_name} 作为接单人员吗？选择后将设置为该线索的服务人员。`,
          confirmText: '确定选择',
          cancelText: '取消'
        });

        // 处理uni.showModal的返回值格式 [error, result]
        const modalResult = Array.isArray(result) ? result[1] : result;

        if (!modalResult || !modalResult.confirm) {
          return;
        }

        uni.showLoading({
          title: '处理中...'
        });

        // 调用API设置接单人员
        const { setLeadAcceptedAunt } = await import('@/api/sister-business.js');
        const response = await setLeadAcceptedAunt({
          customer_uuid: this.orderDetail.uuid,
          aunt_uuid: interview.aunt_uuid,
          aunt_name: interview.aunt_name
        });

        if (response && response.success) {
          uni.showToast({
            title: '设置成功',
            icon: 'success'
          });

          // 重新加载订单详情以更新接单人员信息
          await this.loadOrderDetail();
        } else {
          throw new Error(response?.message || '设置接单人员失败');
        }
      } catch (error) {
        console.error('设置接单人员失败:', error);
        uni.showToast({
          title: error.message || '设置失败，请重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 添加合同
    addContract() {
      // 检查必要的客户信息
      if (!this.orderDetail.name || !this.orderDetail.mobile) {
        uni.showToast({
          title: '客户姓名或手机号缺失，无法创建合同',
          icon: 'none'
        });
        return;
      }

      // 跳转到新建合同页面，并传递客户信息
      const customerName = encodeURIComponent(this.orderDetail.name);
      const customerMobile = encodeURIComponent(this.orderDetail.mobile);

      uni.navigateTo({
        url: `/pages-sister-business/create-contract?fromLead=true&customerName=${customerName}&customerMobile=${customerMobile}`
      });
    },



    // 添加标签
    addTag() {
      this.loadAvailableTags();
      this.showTagModal = true;
    },

    // 加载可用标签列表
    async loadAvailableTags() {
      try {
        const { getCustomerTags } = await import('@/api/sister-business.js');
        const response = await getCustomerTags();
        this.availableTags = response || [];

        // 初始化已选标签（基于当前客户的标签）
        this.selectedTags = [];
        if (this.orderDetail.tags && this.orderDetail.tags.length > 0) {
          this.selectedTags = this.orderDetail.tags.map(tag => tag.id);
        }
      } catch (error) {
        console.error('加载标签列表失败:', error);
        uni.showToast({
          title: '加载标签失败',
          icon: 'none'
        });
      }
    },

    // 切换标签选择状态
    toggleTagSelection(tagId) {
      const index = this.selectedTags.indexOf(tagId);
      if (index > -1) {
        this.selectedTags.splice(index, 1);
      } else {
        this.selectedTags.push(tagId);
      }
    },

    // 确认保存标签
    async confirmSaveTags() {
      try {
        const { addCustomerTags } = await import('@/api/sister-business.js');

        await addCustomerTags({
          customer_uuid: this.orderDetail.uuid,
          tag_ids: this.selectedTags
        });

        // 更新本地数据
        this.orderDetail.tags = this.availableTags.filter(tag =>
          this.selectedTags.includes(tag.id)
        );

        this.showTagModal = false;

        uni.showToast({
          title: '标签保存成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('保存标签失败:', error);
        uni.showToast({
          title: '保存标签失败',
          icon: 'none'
        });
      }
    },

    // 取消标签编辑
    cancelTagEdit() {
      this.showTagModal = false;
      this.selectedTags = [];
    },

    // 根据标签ID获取标签名称
    getTagNameById(tagId) {
      const tag = this.availableTags.find(t => t.id === tagId);
      return tag ? tag.name : `标签${tagId}`;
    },

    // 显示新增标签弹窗
    showCreateTag() {
      this.newTagData = {
        name: '',
        color: '#1890FF'
      };
      this.showCreateTagModal = true;
    },

    // 选择标签颜色
    selectColor(color) {
      this.newTagData.color = color;
    },

    // 取消新增标签
    cancelCreateTag() {
      this.showCreateTagModal = false;
      this.newTagData = {
        name: '',
        color: '#1890FF'
      };
    },

    // 确认创建标签
    async confirmCreateTag() {
      // 验证标签名称
      if (!this.newTagData.name.trim()) {
        uni.showToast({
          title: '请输入标签名称',
          icon: 'none'
        });
        return;
      }

      // 检查标签名称是否重复
      const existingTag = this.availableTags.find(tag =>
        tag.name === this.newTagData.name.trim()
      );
      if (existingTag) {
        uni.showToast({
          title: '标签名称已存在',
          icon: 'none'
        });
        return;
      }

      try {
        const response = await createCustomerTag({
          name: this.newTagData.name.trim(),
          color: this.newTagData.color
        });

        // 添加到可用标签列表
        const newTag = {
          id: response.id || Date.now(), // 使用返回的ID或临时ID
          name: this.newTagData.name.trim(),
          color: this.newTagData.color
        };
        this.availableTags.push(newTag);

        // 自动选中新创建的标签
        this.selectedTags.push(newTag.id);

        this.showCreateTagModal = false;

        uni.showToast({
          title: '标签创建成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('创建标签失败:', error);
        uni.showToast({
          title: '创建标签失败',
          icon: 'none'
        });
      }
    },

    // 跳转到客户资料详情页
    goToCustomerDetail() {
      if (!this.orderDetail.uuid) {
        uni.showToast({
          title: '客户信息不完整',
          icon: 'none'
        });
        return;
      }

      uni.navigateTo({
        url: `/pages-sister-business/customer-detail?uuid=${this.orderDetail.uuid}`
      });
    },

    // 切换标签页
    switchTab(index) {
      this.currentTab = index;

      // 如果切换到跟进记录标签，加载跟进记录
      if (index === 1 && this.orderDetail.uuid) {
        // 如果还没有加载过跟进记录，则加载
        if (this.followRecords.length === 0 && !this.followLoading) {
          this.loadFollowRecords();
        }
      }

      // 如果切换到合同记录标签，加载合同记录
      if (index === 2 && this.orderDetail.uuid) {
        // 每次切换到合同记录标签都刷新数据，确保显示最新的合同记录
        console.log('切换到合同记录标签，刷新合同记录');
        this.loadContractRecords();
      }
    },

    // 自动弹出添加跟进表单（用于从列表页面直接跳转）
    autoShowAddFollow() {
      // 确保订单详情已加载
      if (!this.orderDetail.uuid) {
        // 如果订单详情还没加载，等待加载完成后再弹出
        const checkInterval = setInterval(() => {
          if (this.orderDetail.uuid) {
            clearInterval(checkInterval);
            this.handleUpdateStatus();
          }
        }, 100);

        // 设置超时，避免无限等待
        setTimeout(() => {
          clearInterval(checkInterval);
        }, 5000);
      } else {
        this.handleUpdateStatus();
      }
    },

    // 加载订单详情
    async loadOrderDetail() {
      if (!this.orderId) return;

      this.loading = true;
      try {
        const response = await getSisterBusinessDetail(this.orderId);
        console.log('订单详情响应:', response);
        console.log('状态字段值:', {
          status: response?.status,
          status_name: response?.status_name,
          type: typeof response?.status
        });
        console.log('时间字段调试:', {
          create_time: response?.create_time,
          created_at: response?.created_at,
          createTime: response?.createTime,
          update_time: response?.update_time,
          updated_at: response?.updated_at
        });

        if (response) {
          this.orderDetail = response;
          this.selectedStatus = response.status || '1';

          // 如果当前在跟进记录标签页，加载跟进记录
          if (this.currentTab === 1) {
            this.loadFollowRecords();
          }

          // 加载申请接单人员列表和面试记录
          this.loadApplications();
          this.loadInterviews();
        }
      } catch (error) {
        console.error('加载订单详情失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载跟进记录
    async loadFollowRecords() {
      if (!this.orderDetail.uuid) return;

      this.followLoading = true;
      try {
        const response = await getCustomerFollowRecords({
          customer_uuid: this.orderDetail.uuid,
          page: 1,
          size: 20
        });

        console.log('跟进记录响应:', response);

        if (response && response.list) {
          this.followRecords = response.list;
        } else {
          this.followRecords = [];
        }
      } catch (error) {
        console.error('加载跟进记录失败:', error);
        this.followRecords = [];
        uni.showToast({
          title: '加载跟进记录失败',
          icon: 'none'
        });
      } finally {
        this.followLoading = false;
      }
    },

    // 加载合同记录（使用动态导入，避免影响其他功能）
    async loadContractRecords() {
      if (!this.orderDetail.uuid) {
        console.log('客户UUID为空，无法加载合同记录');
        return;
      }

      this.contractLoading = true;
      try {
        console.log('开始加载合同记录，客户UUID:', this.orderDetail.uuid);

        // 使用动态导入，避免在模块导入阶段就失败
        const { getContractsByCustomerUuid } = await import('@/api/sister-business.js');

        // 调用API获取合同记录
        const response = await getContractsByCustomerUuid(this.orderDetail.uuid);
        console.log('合同记录API响应:', response);

        // 处理响应数据
        if (response && Array.isArray(response)) {
          this.contractList = response;
        } else if (response && response.list && Array.isArray(response.list)) {
          this.contractList = response.list;
        } else if (response && response.data && Array.isArray(response.data)) {
          this.contractList = response.data;
        } else {
          this.contractList = [];
          console.log('合同记录响应格式不符合预期:', response);
        }

        console.log('合同记录加载完成，数量:', this.contractList.length);

        // 如果有合同记录，输出第一条记录的结构用于调试
        if (this.contractList.length > 0) {
          console.log('第一条合同记录结构:', this.contractList[0]);
        }

      } catch (error) {
        console.error('加载合同记录失败:', error);
        this.contractList = [];

        // 根据错误类型给出不同的提示
        if (error.message && error.message.includes('404')) {
          console.log('合同API接口不存在，功能暂不可用');
        } else if (error.message && error.message.includes('Network')) {
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
        } else {
          console.log('合同记录加载失败，但不影响其他功能:', error.message);
        }
      } finally {
        this.contractLoading = false;
      }
    },

    // 拨打电话
    handleCall() {
      const mobile = this.orderDetail.mobile;
      if (!mobile) {
        uni.showToast({
          title: '客户未填写手机号',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: mobile,
        fail: (err) => {
          console.error('拨打电话失败:', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },

    // 客户洞察
    handleCustomerInsight() {
      console.log('点击客户洞察按钮');
      uni.showToast({
        title: '客户洞察功能开发中',
        icon: 'none'
      });
    },

    // 家务清单
    handleHouseworkList() {
      console.log('点击家务清单按钮');
      uni.showToast({
        title: '家务清单功能开发中',
        icon: 'none'
      });
    },

















    // 处理销售归属和售后归属的点击
    handleSalesOwner(type) {
      console.log('点击归属字段:', type, '订单信息:', this.orderDetail);

      if (type === 'sales') {
        // 显示销售归属人选择弹窗
        this.showSalesAttributionSelector = true;
      } else if (type === 'afterSales') {
        // 显示售后归属人选择弹窗
        this.showAfterSalesAttributionSelector = true;
      }
    },

    // 销售归属选择成功处理
    handleSalesAttributionSuccess(data) {
      console.log('销售归属设置成功:', data);

      // 更新本地数据
      this.orderDetail.sales_owner_name = data.staff.name;
      this.orderDetail.sales_owner_uuid = data.staff.uuid;
    },

    // 售后归属选择成功处理
    handleAfterSalesAttributionSuccess(data) {
      console.log('售后归属设置成功:', data);

      // 更新本地数据
      this.orderDetail.after_sales_owner_name = data.staff.name;
      this.orderDetail.after_sales_owner_uuid = data.staff.uuid;
    },

    // 归属选择弹窗关闭处理
    handleAttributionSelectorClose() {
      this.showSalesAttributionSelector = false;
      this.showAfterSalesAttributionSelector = false;
    },



    // 检查并刷新销售归属信息
    checkAndRefreshSalesAttribution() {
      try {
        const app = getApp();
        if (app && app.globalData && app.globalData.needRefreshCustomer) {
          const refreshData = app.globalData.needRefreshCustomer;

          // 检查是否是当前客户的更新
          if (refreshData.customerId === this.orderId || refreshData.customerId === this.orderDetail.uuid) {
            console.log('检测到销售归属更新，刷新数据:', refreshData);

            // 更新本地数据
            if (refreshData.type === 'sales') {
              this.orderDetail.sales_owner_name = refreshData.salesOwnerName;
              this.orderDetail.sales_owner_uuid = refreshData.salesOwnerUuid;
            } else if (refreshData.type === 'afterSales') {
              this.orderDetail.after_sales_owner_name = refreshData.salesOwnerName;
              this.orderDetail.after_sales_owner_uuid = refreshData.salesOwnerUuid;
            }

            // 清除全局刷新标记
            app.globalData.needRefreshCustomer = null;

            console.log('销售归属已更新:', this.orderDetail);
          }
        }
      } catch (error) {
        console.log('检查销售归属更新失败:', error);
      }
    },

    // 检查并刷新合同记录
    checkAndRefreshContractRecords() {
      try {
        const app = getApp();
        if (app && app.globalData && app.globalData.needRefreshContract) {
          const refreshData = app.globalData.needRefreshContract;

          // 检查是否是当前客户的合同更新（通过客户姓名和手机号匹配）
          if (this.orderDetail.name && this.orderDetail.mobile &&
              refreshData.customerName === this.orderDetail.name &&
              refreshData.customerMobile === this.orderDetail.mobile) {

            console.log('检测到合同记录更新，刷新合同数据:', refreshData);

            // 如果有客户UUID，刷新合同记录
            if (this.orderDetail.uuid) {
              this.loadContractRecords();
            }

            // 如果当前不在合同记录标签页，自动切换到合同记录标签页
            if (this.currentTab !== 2) {
              console.log('自动切换到合同记录标签页');
              this.currentTab = 2;
            }

            // 清除全局刷新标记
            app.globalData.needRefreshContract = null;
          }
        }
      } catch (error) {
        console.log('检查合同记录更新失败:', error);
      }
    },

    // 显示跟进记录弹窗
    handleUpdateStatus() {
      this.showStatusModal = true;
    },

    // 日期选择确认
    onDateConfirm(value) {
      this.nextFollowTime = value.value;
      this.showDatePicker = false;
    },

    // 选择图片
    chooseImage() {
      const maxCount = 9 - this.uploadedImages.length;

      uni.chooseImage({
        count: maxCount,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: (res) => {
          // 同时处理 tempFilePaths 和 tempFiles，获取文件大小信息
          if (res.tempFiles && res.tempFiles.length > 0) {
            // 有 tempFiles 时，可以获取文件大小
            res.tempFiles.forEach(tempFile => {
              this.uploadImageToServer(tempFile.path, tempFile.size);
            });
          } else {
            // 没有 tempFiles 时，使用 tempFilePaths
            res.tempFilePaths.forEach(tempFilePath => {
              this.uploadImageToServer(tempFilePath, 0);
            });
          }
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
          uni.showToast({
            title: '取消选择图片',
            icon: 'none'
          });
        }
      });
    },

    // 上传图片到服务器
    async uploadImageToServer(filePath, fileSize = 0) {
      uni.showLoading({
        title: '上传中...'
      });

      try {
        const uploadResult = await new Promise((resolve) => {
          uni.uploadFile({
            url: this.$baseUrl + '/api/v1/public/upload',
            filePath: filePath,
            name: 'file',
            success: (uploadRes) => {
              try {
                const result = JSON.parse(uploadRes.data);
                if (result.code === 200 && result.data && result.data.url) {
                  // 从文件名中提取文件类型
                  const fileName = result.data.fileName || filePath.split('/').pop();
                  const fileType = result.data.fileType || fileName.split('.').pop().toLowerCase() || 'jpg';

                  resolve({
                    success: true,
                    url: result.data.url,
                    fileName: fileName,
                    fileSize: result.data.fileSize || fileSize, // 优先使用返回的文件大小
                    fileType: fileType
                  });
                } else {
                  resolve({ success: false, message: '上传失败' });
                }
              } catch (error) {
                resolve({ success: false, message: '响应解析失败' });
              }
            },
            fail: () => {
              resolve({ success: false, message: '网络请求失败' });
            }
          });
        });

        uni.hideLoading();

        if (uploadResult.success) {
          this.uploadedImages.push({
            url: uploadResult.url,
            fileName: uploadResult.fileName,
            fileSize: uploadResult.fileSize,
            fileType: uploadResult.fileType
          });

          uni.showToast({
            title: '上传成功',
            icon: 'success'
          });
        } else {
          throw new Error(uploadResult.message || '上传失败');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('上传图片失败:', error);
        uni.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
      }
    },

    // 删除图片
    removeImage(index) {
      this.uploadedImages.splice(index, 1);
    },

    // 预览图片
    previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    },

    // 预览跟进记录中的图片
    previewRecordImage(images, currentIndex) {
      const urls = images.map(img => img.url);
      uni.previewImage({
        urls: urls,
        current: currentIndex
      });
    },

    // 确认添加跟进记录
    async confirmUpdateStatus() {
      if (!this.selectedStatus) {
        uni.showToast({
          title: '请选择状态',
          icon: 'none'
        });
        return;
      }

      if (!this.followContent.trim()) {
        uni.showToast({
          title: '请输入跟进内容',
          icon: 'none'
        });
        return;
      }

      try {
        // 获取当前用户信息
        const currentUser = getCurrentUser();
        const userInfo = currentUser.user;

        console.log('=== 跟进记录提交调试信息 ===');
        console.log('完整的currentUser:', currentUser);
        console.log('用户信息userInfo:', userInfo);
        console.log('选择的状态:', this.selectedStatus);
        console.log('订单详情:', this.orderDetail);

        // 检查用户信息的各个字段
        if (userInfo) {
          console.log('用户ID字段检查:');
          console.log('  userInfo.id:', userInfo.id);
          console.log('  userInfo.user_uuid:', userInfo.user_uuid);
          console.log('  userInfo.uuid:', userInfo.uuid);
          console.log('用户名称字段检查:');
          console.log('  userInfo.name:', userInfo.name);
          console.log('  userInfo.user_name:', userInfo.user_name);
          console.log('  userInfo.nick_name:', userInfo.nick_name);
        } else {
          console.log('警告：用户信息为空！');
        }

        // 获取状态名称
        const statusName = this.getStatusText(this.selectedStatus);
        console.log('状态名称:', statusName);

        // 构建提交数据 - 注意：user_uuid 应该存储 getinfo 接口的 id 字段
        const userUuid = userInfo?.id ? String(userInfo.id) : '';
        const userName = userInfo?.name || userInfo?.user_name || userInfo?.nick_name || '系统';

        console.log('提取的用户ID (作为user_uuid):', userUuid);
        console.log('提取的用户名称:', userName);

        const data = {
          // 基本信息
          order_id: this.orderId,
          customer_uuid: this.orderDetail.uuid,

          // 用户信息
          user_uuid: userUuid,
          user_name: userName,

          // 状态信息
          status: this.selectedStatus,
          status_name: statusName,

          // 跟进内容
          follow_content: this.followContent,
          content: this.followContent, // 兼容不同字段名

          // 时间信息
          next_follow_time: this.nextFollowTime ? new Date(this.nextFollowTime).toISOString() : null,
          follow_time: new Date().toISOString(),
          create_time: new Date().toISOString(),

          // 创建人信息
          created_by: userUuid,

          // 图片信息
          files: this.uploadedImages.map(image => ({
            fileName: image.fileName,
            fileType: image.fileType,
            fileSize: image.fileSize,
            url: image.url
          }))
        };

        console.log('=== 最终提交的跟进数据 ===');
        console.log('完整数据对象:', JSON.stringify(data, null, 2));
        console.log('关键字段检查:');
        console.log('  user_uuid:', data.user_uuid);
        console.log('  user_name:', data.user_name);
        console.log('  status:', data.status);
        console.log('  status_name:', data.status_name);
        console.log('  customer_uuid:', data.customer_uuid);
        console.log('  created_by:', data.created_by);

        await updateSisterBusinessStatus(data);

        uni.showToast({
          title: '跟进记录添加成功',
          icon: 'success'
        });

        this.showStatusModal = false;
        this.followContent = '';
        this.nextFollowTime = null;
        this.uploadedImages = []; // 清空图片

        // 重新加载订单详情和跟进记录
        this.loadOrderDetail();
        this.loadFollowRecords();
      } catch (error) {
        console.error('添加跟进记录失败:', error);
        uni.showToast({
          title: '添加失败',
          icon: 'none'
        });
      }
    },

    // 获取状态显示文字
    getStatusText(status) {
      console.log('=== getStatusText 开始 ===');
      console.log('输入参数 status:', status);
      console.log('参数类型:', typeof status);
      console.log('参数是否为undefined:', status === undefined);
      console.log('参数是否为null:', status === null);
      console.log('参数是否为空字符串:', status === '');

      // 处理异常情况
      if (status === undefined || status === null) {
        console.log('状态值为undefined或null，返回默认值');
        return '状态未知';
      }

      const statusMap = {
        '1': '待跟进',
        '2': '跟进中',
        '3': '已成交',
        '4': '已取消',
        '5': '已签约'
      };

      const statusStr = String(status);
      console.log('转换为字符串后:', statusStr);

      const result = statusMap[statusStr];
      console.log('映射结果:', result);

      const finalResult = result || '未知状态';
      console.log('最终返回值:', finalResult);
      console.log('=== getStatusText 结束 ===');

      return finalResult;
    },

    // 格式化日期
    formatDate(dateString) {
      console.log('=== formatDate 开始 ===');
      console.log('输入参数 dateString:', dateString);
      console.log('参数类型:', typeof dateString);
      console.log('参数是否为空:', !dateString);

      if (!dateString) {
        console.log('日期字符串为空，返回空字符串');
        return '';
      }

      try {
        const date = new Date(dateString);
        console.log('创建的Date对象:', date);
        console.log('Date对象是否有效:', !isNaN(date.getTime()));

        if (isNaN(date.getTime())) {
          console.log('无效的日期格式，返回原始字符串');
          return dateString;
        }

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        const result = `${year}-${month}-${day} ${hours}:${minutes}`;
        console.log('格式化结果:', result);
        console.log('=== formatDate 结束 ===');

        return result;
      } catch (error) {
        console.error('formatDate 异常:', error);
        return dateString || '时间格式错误';
      }
    },

    // 格式化跟进记录时间（仿照参考样式）
    formatRecordTime(dateString) {
      if (!dateString) return '';

      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const recordDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        // 如果是今天，显示"今天 HH:MM"
        if (recordDate.getTime() === today.getTime()) {
          return `今天 ${hours}:${minutes}`;
        }

        // 如果是昨天，显示"昨天 HH:MM"
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        if (recordDate.getTime() === yesterday.getTime()) {
          return `昨天 ${hours}:${minutes}`;
        }

        // 其他日期显示"MM-DD HH:MM"
        return `${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        console.error('formatRecordTime 异常:', error);
        return dateString;
      }
    },

    // 检查是否可以编辑记录
    canEditRecord() {
      // 这里可以添加权限判断逻辑
      // 例如：只有创建者或管理员可以编辑
      return false; // 暂时禁用编辑功能
    },

    // 处理合同点击事件
    handleContractClick(contract) {
      console.log('点击合同:', contract);

      if (!contract || !contract.id) {
        console.error('合同信息不完整，无法跳转到详情页');
        uni.showToast({
          title: '合同信息不完整',
          icon: 'none'
        });
        return;
      }

      console.log('跳转到合同详情页，合同ID:', contract.id);

      // 跳转到三业务的合同详情页
      uni.navigateTo({
        url: `/pages-sister-business/contract-detail?id=${contract.id}`,
        success: () => {
          console.log('跳转合同详情页成功');
        },
        fail: (error) => {
          console.error('跳转合同详情页失败:', error);
          uni.showToast({
            title: '跳转失败',
            icon: 'none'
          });
        }
      });
    },

    // 合同相关方法暂时注释，避免影响其他功能
    /*
    // 查看合同详情
    viewContractDetail(contract) {
      console.log('查看合同详情:', contract);
      uni.showToast({
        title: '合同详情功能开发中',
        icon: 'none'
      });
    },

    // 获取合同状态样式类
    getContractStatusClass(status) {
      const statusMap = {
        '0': 'status-draft',
        '1': 'status-active',
        '2': 'status-terminated',
        '3': 'status-completed'
      };
      return statusMap[status] || 'status-unknown';
    },

    // 获取合同状态文字
    getContractStatusText(status) {
      const statusMap = {
        '0': '草稿',
        '1': '有效',
        '2': '已终止',
        '3': '已完成'
      };
      return statusMap[status] || '未知状态';
    },

    // 格式化金额显示
    formatAmount(amount) {
      if (!amount && amount !== 0) return '0.00';
      const numAmount = parseFloat(amount);
      if (isNaN(numAmount)) return '0.00';
      return numAmount.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    */



    // 显示签约状态选择器
    showContractStatusSelector() {
      // 保持当前选中的筛选状态，如果没有则默认为空（全部）
      if (this.selectedContractStatus === undefined) {
        this.selectedContractStatus = '';
      }
      this.showContractStatusModal = true;
    },

    // 选择签约状态 - 直接应用筛选
    selectContractStatus(statusValue) {
      this.selectedContractStatus = statusValue;

      // 获取状态名称
      const statusOption = this.contractStatusOptions.find(option => option.value === statusValue);
      const statusName = statusOption ? statusOption.label : '全部';

      // 关闭弹窗
      this.showContractStatusModal = false;

      // 显示筛选结果
      const filteredCount = this.filteredFollowRecords.length;
      const totalCount = this.followRecords.length;

      if (statusValue === '') {
        // 选择了"全部"
        uni.showToast({
          title: `显示全部记录 (${totalCount}条)`,
          icon: 'success',
          duration: 2000
        });
      } else {
        // 选择了具体状态
        uni.showToast({
          title: `筛选完成：${statusName} (${filteredCount}/${totalCount})`,
          icon: 'success',
          duration: 2000
        });
      }

      console.log('筛选跟进记录:', {
        selectedStatus: statusValue,
        statusName: statusName,
        filteredCount: filteredCount,
        totalCount: totalCount,
        filteredRecords: this.filteredFollowRecords
      });
    },

    // 编辑记录
    editRecord(record) {
      // 编辑记录的逻辑
      console.log('编辑记录:', record);
      uni.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      });
    },

    // 获取用于分享的职位类型名称
    getJobTypeForShare() {
      if (this.orderDetail.aunt_name) {
        // 如果有多个职位类型，取第一个用于分享标题
        const typeNames = this.orderDetail.aunt_name.split(',');
        return typeNames[0].trim();
      }

      if (this.orderDetail.aunt_type) {
        // 如果没有中文名称，使用原始编码
        const typeCodes = this.orderDetail.aunt_type.split(',');
        return typeCodes[0].trim();
      }

      return '家政服务';
    },

    // 获取用于分享的薪资文本（避免单位重复）
    getSalaryTextForShare() {
      const minSalary = this.orderDetail.min_salary;
      const maxSalary = this.orderDetail.max_salary;
      const salaryUnit = this.orderDetail.salary_unit || '';

      // 如果都没有薪资信息，返回面议
      if (!minSalary && !maxSalary) {
        return '面议';
      }

      // 构建薪资范围文本
      let salaryText = '';
      if (minSalary && maxSalary) {
        // 有最小值和最大值
        salaryText = `${minSalary}-${maxSalary}`;
      } else if (minSalary) {
        // 只有最小值
        salaryText = `${minSalary}起`;
      } else if (maxSalary) {
        // 只有最大值
        salaryText = `最高${maxSalary}`;
      }

      // 添加单位（数据库中的salary_unit已经包含了完整的单位信息，如"元/月"、"元/27天"等）
      if (salaryUnit) {
        salaryText += ` ${salaryUnit}`;
      }

      return salaryText || '面议';
    }
  },

  // 微信分享配置（当用户点击右上角分享时触发）
  onShareAppMessage() {
    if (this.shareUrl) {
      // 使用aunt_name字段（职位类型中文名称）而不是aunt_type（数字编码）
      const jobTypeName = this.getJobTypeForShare();
      const salaryText = this.getSalaryTextForShare();
      const title = `急招${jobTypeName}，薪资${salaryText}`;
      return {
        title: title,
        path: this.shareUrl,
        imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png"
      };
    }

    // 默认分享当前页面
    return {
      title: '三嫂业务线索详情',
      path: `/pages-sister-business/detail?id=${this.orderDetail.uuid}`,
      imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png"
    };
  },

  // 分享到朋友圈配置
  onShareTimeline() {
    if (this.shareUrl) {
      // 使用aunt_name字段（职位类型中文名称）而不是aunt_type（数字编码）
      const jobTypeName = this.getJobTypeForShare();
      const salaryText = this.getSalaryTextForShare();
      const title = `急招${jobTypeName}，薪资${salaryText}`;
      return {
        title: title,
        query: `token=${this.shareUrl.split('token=')[1]}`,
        imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png"
      };
    }
    return null;
  }
};
</script>

<style lang="scss" scoped>
.detail-page {
  height: 100vh;
  background: #f8f8f8;
}

// 页面加载动画
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading-spinner {
      margin-bottom: 30rpx;
    }

    .loading-text {
      font-size: 28rpx;
      color: #666;
      text-align: center;
    }
  }
}

// 主要内容容器
.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  // 添加淡入动画
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 顶部背景区域

// 顶部背景区域
.header-background-section {
  flex-shrink: 0;
  position: relative;
  padding-bottom: 20rpx;
  margin-bottom: 0;
  // 添加状态栏安全区域
  padding-top: calc(var(--status-bar-height) + 20rpx);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 20rpx;
  }
}

// 导航栏
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0 30rpx;

  .nav-left {
    width: 60rpx;
    display: flex;
    align-items: center;
  }

  .nav-title {
    flex: 1;
    text-align: center;

    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #fff;
    }
  }

  .nav-right {
    width: 60rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

// 客户信息卡片
.customer-info-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin: 0 0 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24rpx;

    .header-left {
      flex: 1;

      .source-tag {
        display: inline-block;
        background: #e6f7ff;
        color: #1890ff;
        font-size: 22rpx;
        padding: 6rpx 12rpx;
        border-radius: 8rpx;
        margin-bottom: 12rpx;
      }

      .customer-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        display: block;
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .add-contract-btn {
        background: #fdd118;
        padding: 12rpx 20rpx;
        border-radius: 8rpx;

        .btn-text {
          font-size: 24rpx;
          color: #333;
          font-weight: 500;
        }
      }

      .phone-btn {
        width: 60rpx;
        height: 60rpx;
        background: #52c41a;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }



  .address-section, .remark-section {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .address-label, .remark-label {
      font-size: 28rpx;
      color: #666;
      margin-right: 12rpx;
      flex-shrink: 0;
    }

    .address-text, .remark-text {
      font-size: 28rpx;
      color: #333;
      flex: 1;
    }
  }

  // 地址区域点击样式
  .address-section {
    cursor: pointer;
    padding: 8rpx 0;
    border-radius: 8rpx;
    transition: background-color 0.2s;

    &:active {
      background-color: rgba(253, 209, 24, 0.1);
    }

    .address-text {
      color: #1890ff;
    }
  }

  .tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin: 20rpx 0;

    .tag-item {
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .tag-text {
        font-size: 22rpx;
        color: #fff;
        font-weight: 500;
      }

      &.add-tag {
        background: #f0f0f0;
        border: 1rpx dashed #d9d9d9;

        .tag-text {
          color: #999;
        }
      }
    }
  }

  .sales-info {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 16rpx; // 减少padding降低高度
    padding-bottom: 8rpx; // 减少底部padding

    .sales-item {
      display: flex;
      align-items: center;
      padding: 12rpx 0;

      .sales-label {
        font-size: 28rpx;
        color: #666;
        margin-right: 12rpx;
        flex-shrink: 0;
      }

      .sales-value {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }
    }

    // 合并显示样式
    &.merged-sales-info {
      .sales-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8rpx 0; // 减少padding

        .sales-item-half {
          flex: 1;
          display: flex;
          align-items: center;

          &:first-child {
            margin-right: 40rpx;
          }

          .sales-label {
            font-size: 28rpx;
            color: #666;
            margin-right: 12rpx;
            flex-shrink: 0;
            width: 140rpx; // 减少标签宽度
          }

          .sales-value {
            font-size: 28rpx;
            color: #333;
            flex: 1;
          }
        }
      }
    }
  }
}

// 功能按钮区域
.function-buttons {
  display: flex;
  gap: 24rpx;
  padding: 16rpx 0 8rpx 0;
  border-top: 1rpx solid #f0f0f0;

  .function-btn {
    flex: 1;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    transition: all 0.3s ease;

    .btn-text {
      font-size: 28rpx;
      font-weight: 500;
    }

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }

    // 客户洞察按钮样式
    &.customer-insight-btn {
      background: #e3f2fd;
      border: 1rpx solid #bbdefb;

      .btn-text {
        color: #1976d2;
      }
    }

    // 家务清单按钮样式
    &.housework-list-btn {
      background: #e8f5e8;
      border: 1rpx solid #c8e6c9;

      .btn-text {
        color: #388e3c;
      }
    }
  }
}

// 标签页导航
.tab-nav {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #e0e0e0;

  .tab-item {
    flex: 1;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #666;
    position: relative;
    transition: all 0.3s ease;

    &.active {
      color: #fdd118;
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background: #fdd118;
        border-radius: 2rpx;
      }
    }
  }
}

// 主要内容
.main-content {
  height: calc(100vh - 88rpx);
  box-sizing: border-box;
}

// 标签页内容
.tab-content {
  min-height: 100%;

  // 跟进记录页面需要为底部输入区域留出空间
  &.follow-tab {
    padding-bottom: 160rpx;
  }
}

// 发单/面试页面样式
.dispatch-interview-tab {
  padding: 30rpx 20rpx 140rpx;
  background-color: #f8f8f8;
  min-height: calc(100vh - 200rpx);
}

// 区域卡片样式
.section-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;

    .empty-text {
      font-size: 28rpx;
      color: #999;
      text-align: center;
    }

    .empty-tip {
      font-size: 24rpx;
      color: #ccc;
      margin-top: 12rpx;
      text-align: center;
    }
  }
}

// 发单相关样式
.dispatch-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;

  .dispatch-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #fdd118 0%, #f0c419 100%);
    border-radius: 44rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 600;
    color: #fff;
    box-shadow: 0 4rpx 16rpx rgba(253, 209, 24, 0.3);
    transition: all 0.3s ease;

    &:disabled {
      opacity: 0.6;
      transform: none;
      box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.2);
    }

    &:not(:disabled):active {
      transform: translateY(2rpx);
      box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.4);
    }

    &.share-btn {
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
      box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.3);

      &:not(:disabled):active {
        box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4);
      }
    }
  }

  .dispatch-tip {
    font-size: 24rpx;
    color: #999;
    text-align: center;
    line-height: 1.4;
  }
}

// 申请接单人员信息样式
.accepted-aunt-info {
  .aunt-item {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    border: 1rpx solid #e9ecef;

    .aunt-avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #fdd118 0%, #f0c419 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      flex-shrink: 0;

      .avatar-text {
        font-size: 32rpx;
        font-weight: 600;
        color: #fff;
      }
    }

    .aunt-details {
      flex: 1;
      min-width: 0;

      .aunt-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }

      .aunt-mobile {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 8rpx;
      }

      .accept-time {
        font-size: 24rpx;
        color: #999;
      }
    }

    .aunt-actions {
      flex-shrink: 0;

      .contact-btn {
        padding: 16rpx 24rpx;
        background: #fdd118;
        border-radius: 20rpx;
        border: none;
        display: flex;
        align-items: center;
        gap: 8rpx;
        font-size: 24rpx;
        color: #333;
        font-weight: 500;
      }
    }
  }
}

.section-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  line-height: 1.4;
}

.empty-state {
  padding: 60rpx 20rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  line-height: 1.5;
}





.btn-text {
  font-size: 30rpx;
  font-weight: 500;
}

// 信息卡片
.info-card {
  background: #fff;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  
  .card-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f0f0f0;
  }
  
  .info-item {
    display: flex;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      font-size: 28rpx;
      color: #666;
      width: 160rpx;
      flex-shrink: 0;
    }
    
    .value {
      font-size: 28rpx;
      color: #333;
      flex: 1;
      
      &.status {
        &.pending {
          color: #856404;
        }
        
        &.processing {
          color: #0c5460;
        }
        
        &.completed {
          color: #155724;
        }

        &.cancelled {
          color: #721c24;
        }

        &.signed {
          color: #0c5460;
        }
      }
    }
  }
}

// 操作区域
.action-section {
  display: flex;
  gap: 20rpx;
  margin: 32rpx;
  
  .action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    font-size: 30rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &.primary-btn {
      background: #fdd118;
      color: #333;
      
      &:active {
        background: #f5c842;
      }
    }
    
    &.secondary-btn {
      background: #fff;
      color: #666;
      border: 1rpx solid #e0e0e0;
      
      &:active {
        background: #f8f8f8;
      }
    }
  }
}

// 状态更新弹窗
.status-modal {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 32rpx;
  
  .modal-header {
    text-align: center;
    margin-bottom: 32rpx;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }
  
  .modal-content {
    .status-options {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      margin-bottom: 32rpx;
      
      .status-option {
        padding: 16rpx 32rpx;
        border-radius: 20rpx;
        background: #f8f8f8;
        color: #666;
        font-size: 28rpx;
        transition: all 0.3s ease;
        
        &.active {
          background: #fdd118;
          color: #333;
        }
      }
    }
    
    .follow-section {
      margin-bottom: 24rpx;

      .follow-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
      }

      .follow-input {
        width: 100%;
        min-height: 120rpx;
        padding: 20rpx;
        border: 1rpx solid #e0e0e0;
        border-radius: 12rpx;
        font-size: 28rpx;
        color: #333;
        box-sizing: border-box;

        &::placeholder {
          color: #999;
        }
      }
    }

    .next-follow-section {
      .next-follow-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
      }

      .date-input {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx;
        border: 1rpx solid #e0e0e0;
        border-radius: 12rpx;
        background: #fff;

        .date-text {
          font-size: 28rpx;
          color: #333;

          &:empty::before {
            content: '选择下次跟进时间（可选）';
            color: #999;
          }
        }
      }
    }
  }

  // 图片上传区域样式
  .image-upload-section {
    margin-top: 32rpx;

    .upload-label {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;
    }

    .image-upload-container {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
    }

    .uploaded-images {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
    }

    .image-item {
      position: relative;
      width: 120rpx;
      height: 120rpx;

      .image-preview {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
        border: 1rpx solid #e0e0e0;
      }

      .image-delete {
        position: absolute;
        top: -8rpx;
        right: -8rpx;
        width: 24rpx;
        height: 24rpx;
        background: #ff4757;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .upload-btn {
      width: 120rpx;
      height: 120rpx;
      border: 2rpx dashed #ddd;
      border-radius: 8rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #fafafa;

      .upload-text {
        font-size: 20rpx;
        color: #999;
        margin-top: 8rpx;
      }
    }

    .upload-tip {
      font-size: 24rpx;
      color: #999;
      margin-top: 16rpx;
      display: block;
    }
  }

  .modal-footer {
    display: flex;
    gap: 20rpx;
    margin-top: 32rpx;
  }
}

// 跟进记录样式
.loading-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;

  .loading-text {
    font-size: 26rpx;
    color: #666;
    margin-top: 20rpx;
  }
}

// 跟进记录容器
.follow-records-container {
  background: #fff;
  margin: 0;
  padding: 0;
}

// 跟进记录项
.follow-record-item {
  background: #f8f9fa;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  position: relative;

  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20rpx;

    .record-author {
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .author-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }

      .record-time {
        font-size: 24rpx;
        color: #999;
      }
    }

    .record-actions {
      .action-btn-text {
        font-size: 24rpx;
        background: transparent;
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        border: 1rpx solid;

        // 默认样式（待跟进）
        color: #ff8c00;
        border-color: #ff8c00;
        background: rgba(255, 140, 0, 0.1);

        // 使用属性选择器为不同状态设置颜色
        &[data-status="1"] {
          color: #ff8c00;
          border-color: #ff8c00;
          background: rgba(255, 140, 0, 0.1);
        }

        &[data-status="2"] {
          color: #1890ff;
          border-color: #1890ff;
          background: rgba(24, 144, 255, 0.1);
        }

        &[data-status="3"] {
          color: #52c41a;
          border-color: #52c41a;
          background: rgba(82, 196, 26, 0.1);
        }

        &[data-status="4"] {
          color: #8c8c8c;
          border-color: #8c8c8c;
          background: rgba(140, 140, 140, 0.1);
        }

        &[data-status="5"] {
          color: #722ed1;
          border-color: #722ed1;
          background: rgba(114, 46, 209, 0.1);
        }
      }
    }
  }

  .record-content {
    margin-bottom: 20rpx;

    .content-text {
      font-size: 30rpx;
      color: #333;
      line-height: 1.6;
      word-break: break-all;
    }
  }

  .record-images {
    margin-bottom: 20rpx;

    .images-label {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 16rpx;
      display: block;
    }

    .images-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;

      .record-image-item {
        width: 120rpx;
        height: 120rpx;

        .record-image {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
          border: 1rpx solid #e0e0e0;
        }
      }
    }
  }

  .next-follow-info {
    .next-follow-text {
      font-size: 24rpx;
      color: #ff6b35;
      background: rgba(255, 107, 53, 0.1);
      padding: 8rpx 12rpx;
      border-radius: 8rpx;
      display: inline-block;
    }
  }
}

// 空状态
.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  background: #fff;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-top: 24rpx;
  }

  .empty-tip {
    font-size: 24rpx;
    color: #ccc;
    margin-top: 12rpx;
  }
}

// 底部输入区域
.bottom-input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1rpx solid #e0e0e0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  z-index: 100;

  .input-container {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 24rpx;
    padding: 16rpx 24rpx;
    gap: 16rpx;

    .input-left {
      flex-shrink: 0;

      .status-tag {
        font-size: 26rpx;
        color: #666;
        background: #fff;
        padding: 8rpx 16rpx;
        border-radius: 16rpx;
        border: 1rpx solid #e0e0e0;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 120rpx;
        transition: all 0.2s ease;

        &:active {
          background: #f0f0f0;
          transform: scale(0.98);
        }
      }
    }

    .input-center {
      flex: 1;
      padding: 8rpx 0;

      .input-placeholder {
        font-size: 28rpx;
        color: #999;
      }
    }

    .input-right {
      flex-shrink: 0;
    }
  }
}

.record-count {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

// 底部安全区域
.safe-area-bottom {
  height: 120rpx;
}

// 签约状态选择弹窗样式
.contract-status-modal {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 60vh;

  .modal-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 40rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .modal-content {
    padding: 30rpx 40rpx;

    .contract-status-options {
      display: flex;
      flex-direction: column;
      gap: 20rpx;

      .contract-status-option {
        padding: 20rpx 24rpx;
        border-radius: 12rpx;
        background: #f8f9fa;
        color: #333;
        font-size: 30rpx;
        text-align: center;
        transition: all 0.3s ease;
        border: 2rpx solid transparent;

        &:active {
          transform: scale(0.98);
        }

        &.active {
          background: #fdd118;
          color: #333;
          font-weight: 500;
          border-color: #fdd118;
        }
      }
    }
  }

  .modal-footer {
    padding: 20rpx 40rpx 40rpx;
    display: flex;
    gap: 20rpx;
  }
}

// 标签选择弹窗样式
.tag-modal {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 40rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .header-actions {
      display: flex;
      gap: 30rpx;

      .add-new-btn {
        font-size: 28rpx;
        color: #1890FF;
        font-weight: 600;
      }

      .cancel-btn {
        font-size: 28rpx;
        color: #666;
      }

      .confirm-btn {
        font-size: 28rpx;
        color: #fdd118;
        font-weight: 600;
      }
    }
  }

  .modal-content {
    padding: 30rpx 40rpx;
    max-height: 60vh;
    overflow-y: auto;

    .section-title {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      margin-bottom: 20rpx;
      display: block;
    }

    .selected-tags-section {
      margin-bottom: 40rpx;

      .selected-tags-list {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .selected-tag-item {
          display: flex;
          align-items: center;
          background: #e8f5e8;
          border: 1rpx solid #52c41a;
          border-radius: 20rpx;
          padding: 8rpx 16rpx;

          .tag-name {
            font-size: 24rpx;
            color: #52c41a;
            margin-right: 8rpx;
          }

          .remove-tag {
            width: 24rpx;
            height: 24rpx;
            background: #52c41a;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    .available-tags-section {
      .available-tags-list {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .available-tag-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #f5f5f5;
          border-radius: 20rpx;
          padding: 12rpx 20rpx;
          min-width: 120rpx;
          transition: all 0.2s ease;

          &:active {
            transform: scale(0.98);
          }

          &.selected {
            background: #1890FF;
          }

          .tag-name {
            font-size: 26rpx;
            color: #333;
            margin-right: 8rpx;
          }

          .tag-action {
            width: 20rpx;
            height: 20rpx;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}

// 新增标签弹窗样式
.create-tag-modal {
  background: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  padding: 0;

  .create-tag-header {
    padding: 40rpx 40rpx 20rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;

    .create-tag-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .create-tag-content {
    padding: 40rpx;

    .form-item {
      margin-bottom: 40rpx;

      .form-label {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 16rpx;
        display: block;
      }

      .form-input {
        width: 100%;
        height: 80rpx;
        border: 2rpx solid #e8e8e8;
        border-radius: 12rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #333;
        background: #f8f9fa;

        &:focus {
          border-color: #1890FF;
          background: #fff;
        }
      }

      .color-picker {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        gap: 16rpx;

        .color-item {
          width: 48rpx;
          height: 48rpx;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2rpx solid transparent;
          transition: all 0.2s ease;

          &:active {
            transform: scale(0.9);
          }

          &.selected {
            border-color: #fff;
            box-shadow: 0 0 0 2rpx #1890FF;
          }
        }
      }

      .tag-preview {
        .preview-tag {
          display: inline-block;
          padding: 8rpx 20rpx;
          border-radius: 20rpx;
          background: #1890FF;

          .preview-text {
            font-size: 24rpx;
            color: #fff;
            font-weight: 500;
          }
        }
      }
    }
  }

  .create-tag-buttons {
    display: flex;
    border-top: 1rpx solid #f0f0f0;

    .create-tag-btn {
      flex: 1;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:first-child {
        border-right: 1rpx solid #f0f0f0;
      }

      &:active {
        background: #f5f5f5;
      }

      .btn-text {
        font-size: 32rpx;
        color: #666;
      }

      &.confirm {
        .btn-text {
          color: #1890FF;
          font-weight: 600;
        }
      }
    }
  }
}

// 合同记录样式
.contract-records-container {
  padding: 24rpx 32rpx;

  .contract-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid #f0f0f0;
    transition: all 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &:active {
      transform: translateY(-2rpx);
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    }

    .contract-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16rpx;

      .contract-info {
        flex: 1;

        .contract-number {
          font-size: 28rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 8rpx;
        }

        .contract-status {
          display: inline-block;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
          font-size: 22rpx;

          .status-text {
            font-weight: 500;
          }

          &.contract-status-draft {
            background: #f0f0f0;
            color: #666;
          }

          &.contract-status-active {
            background: #e6f7ff;
            color: #1890ff;
          }

          &.contract-status-terminated {
            background: #fff2e8;
            color: #fa8c16;
          }

          &.contract-status-unknown {
            background: #f5f5f5;
            color: #999;
          }
        }
      }
    }

    .contract-body {
      margin-bottom: 16rpx;

      .contract-row {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .contract-label {
          font-size: 26rpx;
          color: #666;
          width: 140rpx;
          flex-shrink: 0;
        }

        .contract-value {
          font-size: 26rpx;
          color: #333;
          flex: 1;

          &.amount {
            color: #f5222d;
            font-weight: 500;
          }
        }
      }
    }

    .contract-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 12rpx;
      border-top: 1rpx solid #f0f0f0;

      .contract-time {
        font-size: 24rpx;
        color: #999;
      }

      .contract-actions {
        display: flex;
        align-items: center;
      }
    }
  }
}

// 合同记录样式
.contract-container {
  padding: 24rpx 32rpx;
}

.contract-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;

  &:last-child {
    margin-bottom: 0;
  }

  .contract-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .contract-number {
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
    }

    .contract-status {
      font-size: 24rpx;
      color: #1890ff;
      background: #e6f7ff;
      padding: 4rpx 12rpx;
      border-radius: 8rpx;
    }
  }

  .contract-body {
    margin-bottom: 16rpx;

    .contract-type,
    .contract-amount,
    .contract-time {
      display: block;
      font-size: 26rpx;
      color: #666;
      margin-bottom: 8rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .contract-amount {
      color: #f5222d;
      font-weight: 500;
    }
  }

  .contract-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12rpx;
    border-top: 1rpx solid #f0f0f0;

    .create-time {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 32rpx;

  .loading-text,
  .empty-text {
    font-size: 28rpx;
    color: #666;
    margin-top: 20rpx;
  }

  .empty-tip {
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
  }
}

// 已选择阿姨信息样式
.accepted-aunt-info {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
  border: 1rpx solid #b7eb8f;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 16rpx;

  .accepted-aunt-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .accepted-icon {
      margin-right: 12rpx;
    }

    .accepted-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #52c41a;
    }
  }

  .accepted-aunt-details {
    .aunt-info-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8rpx;

      .aunt-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        flex: 1;
      }

      .contact-aunt-btn {
        display: flex;
        align-items: center;
        gap: 8rpx;
        background: #fdd118;
        color: #333;
        padding: 12rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;

        .contact-text {
          font-size: 24rpx;
          color: #333;
        }
      }
    }

    .accepted-time {
      font-size: 24rpx;
      color: #666;
    }
  }
}

// 申请接单人员样式
.applications-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;

  .refresh-btn {
    width: 60rpx;
    height: 60rpx;
    background: #f8f9fa;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;

    &:disabled {
      opacity: 0.6;
    }
  }
}

.applications-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #666;
  font-size: 28rpx;

  text {
    margin-left: 16rpx;
  }
}

.applications-list {
  .application-item {
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &.accepted {
      background: #f6ffed;
      border-color: #b7eb8f;
    }

    .application-info {
      margin-bottom: 20rpx;

      .aunt-basic {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12rpx;

        .aunt-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }

        .aunt-mobile {
          font-size: 26rpx;
          color: #666;
        }
      }

      .aunt-details {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        text {
          font-size: 24rpx;
          color: #999;
          margin-right: 16rpx;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .application-time {
        font-size: 24rpx;
        color: #999;
      }
    }

    .application-actions {
      .action-buttons {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 16rpx;



        .interview-btn {
          width: 120rpx;
          height: 64rpx;
          background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
          color: #fff;
          border: none;
          border-radius: 32rpx;
          font-size: 24rpx;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6rpx;
          cursor: pointer;

          .btn-text {
            font-size: 24rpx;
            font-weight: 500;
            color: #fff;
          }
        }

        .contact-btn {
          width: 120rpx;
          height: 64rpx;
          background: #fdd118;
          color: #333;
          border: none;
          border-radius: 32rpx;
          font-size: 24rpx;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6rpx;
          cursor: pointer;

          .btn-text {
            font-size: 24rpx;
            font-weight: 500;
            color: #333;
          }
        }
      }

      .status-accepted {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .status-tag {
          display: flex;
          align-items: center;
          gap: 8rpx;
          font-size: 24rpx;
          font-weight: 500;

          &.accepted {
            color: #52c41a;
          }
        }

        .contact-btn {
          width: 140rpx;
          height: 64rpx;
          background: #fdd118;
          color: #333;
          border: none;
          border-radius: 32rpx;
          font-size: 26rpx;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8rpx;
          cursor: pointer;

          .btn-text {
            font-size: 26rpx;
            font-weight: 500;
            color: #333;
          }
        }
      }

      .status-rejected {
        .status-tag {
          display: flex;
          align-items: center;
          gap: 8rpx;
          font-size: 24rpx;
          color: #999;

          &.rejected {
            color: #999;
          }
        }
      }
    }
  }
}

.applications-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  color: #999;

  text {
    font-size: 28rpx;
    margin-top: 16rpx;

    &.empty-tip {
      font-size: 24rpx;
      color: #ccc;
      margin-top: 8rpx;
    }
  }
}

// 面试记录样式
.interview-list {
  .interview-item {
    background: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    border: 1rpx solid #f0f0f0;

    .interview-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;

      .aunt-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .interview-status {
        padding: 8rpx 16rpx;
        border-radius: 16rpx;
        font-size: 24rpx;
        font-weight: 500;

        &.scheduled {
          background: #e6f7ff;
          color: #1890ff;
        }

        &.completed {
          background: #f6ffed;
          color: #52c41a;
        }

        &.cancelled {
          background: #fff2e8;
          color: #fa8c16;
        }
      }
    }

    .interview-details {
      margin-bottom: 20rpx;

      .interview-time,
      .interview-type,
      .interview-remark {
        display: block;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 12rpx;
        line-height: 1.5;
        padding: 4rpx 0;
      }

      .interview-remark {
        color: #999;
        margin-bottom: 0;
      }
    }

    .interview-actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 12rpx;

      .select-btn {
        padding: 12rpx 20rpx;
        background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
        color: #fff;
        border: none;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8rpx;
        cursor: pointer;

        .btn-text {
          font-size: 24rpx;
          font-weight: 500;
          color: #fff;
        }
      }

      .contact-btn {
        padding: 12rpx 20rpx;
        background: #fdd118;
        color: #333;
        border: none;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8rpx;
        cursor: pointer;

        .btn-text {
          font-size: 24rpx;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }
}

// 面试安排弹窗样式
.interview-modal {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  max-height: 80vh;

  .modal-header {
    text-align: center;
    margin-bottom: 32rpx;

    .modal-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .modal-content {
    .interview-staff-info {
      margin-bottom: 32rpx;
      padding: 24rpx;
      background: #f8f9fa;
      border-radius: 12rpx;

      .staff-label {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 12rpx;
        display: block;
      }

      .staff-details {
        .staff-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
          display: block;
        }

        .staff-mobile {
          font-size: 28rpx;
          color: #666;
        }
      }
    }

    .interview-time-section,
    .interview-type-section,
    .interview-remark-section {
      margin-bottom: 32rpx;

      .time-label,
      .type-label,
      .remark-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
        display: block;
        font-weight: 500;
      }

      .time-input {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        border: 1rpx solid #e8e8e8;

        .time-text {
          font-size: 28rpx;
          color: #333;
        }
      }

      .type-options {
        display: flex;
        gap: 16rpx;

        .type-option {
          flex: 1;
          padding: 24rpx;
          background: #f8f9fa;
          border: 1rpx solid #e8e8e8;
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12rpx;
          transition: all 0.3s;

          &.active {
            background: #1890ff;
            border-color: #1890ff;

            .type-text {
              color: #fff;
            }
          }

          .type-text {
            font-size: 28rpx;
            color: #666;
            font-weight: 500;
          }
        }
      }

      .remark-input {
        width: 100%;
        min-height: 120rpx;
        padding: 24rpx;
        background: #f8f9fa;
        border: 1rpx solid #e8e8e8;
        border-radius: 12rpx;
        font-size: 28rpx;
        color: #333;
        line-height: 1.4;
        resize: none;
      }
    }
  }

  .modal-footer {
    display: flex;
    gap: 16rpx;
    margin-top: 32rpx;
  }
}
</style>
