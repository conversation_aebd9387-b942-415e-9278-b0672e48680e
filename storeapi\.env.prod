# -------- 应用配置 --------
# 应用运行环境
APP_ENV = 'prod'
# 应用名称
APP_NAME = 'JgAdmin-StoreFastApi'
# 应用代理路径
APP_ROOT_PATH = '/prod-api'
# 应用主机
APP_HOST = '0.0.0.0'
# 应用端口
APP_PORT = 9099
# 应用版本
APP_VERSION= '1.6.2'
# 应用是否开启热重载
APP_RELOAD = false
# 应用是否开启IP归属区域查询
APP_IP_LOCATION_QUERY = true
# 应用是否允许账号同时登录
APP_SAME_TIME_LOGIN = true

# -------- Jwt配置 --------
# Jwt秘钥
JWT_SECRET_KEY = 'b01c66dc2c58dc6a0aabfe2144256be36226de378bf87f72c0c795dda67f4d55'
# Jwt算法
JWT_ALGORITHM = 'HS256'
# 令牌过期时间（一个月 = 30天 * 24小时 * 60分钟 = 43200分钟）
JWT_EXPIRE_MINUTES = 43200
# redis中令牌过期时间（一个月 = 43200分钟）
JWT_REDIS_EXPIRE_MINUTES = 43200


# -------- 数据库配置 --------
# 数据库类型，可选的有'mysql'、'postgresql'，默认为'mysql'
DB_TYPE = 'mysql'
# 数据库主机
DB_HOST = 'rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com'
# 数据库端口
DB_PORT = 3306
# 数据库用户名
DB_USERNAME = 'jingang'
# 数据库密码
DB_PASSWORD = 'jg$5170701'
# 数据库名称
DB_DATABASE = 'jingangdata'

# -------- 华为云OBS配置 --------
OBS_ACCESS_KEY = "YF0GHXVNGB6PYFR8MFN9"  # 华为云OBS访问密钥
OBS_SECRET_KEY = "iUFWW4pT9M9hXDlvU7HKAiBEvikPwKDySTyrXdLY"  # 华为云OBS密钥
OBS_BUCKET = "jingang"  # 存储桶名称
OBS_ENDPOINT = "obs.cn-east-3.myhuaweicloud.com"  # 终端节点
OBS_REGION = "cn-east-3"  # 区域
OBS_URL_PREFIX = "https://jingang.obs.cn-east-3.myhuaweicloud.com"  # URL前缀
OBS_DIRECTORY = "jgstore"  # 存储目录
# 是否开启sqlalchemy日志
DB_ECHO = true
# 允许溢出连接池大小的最大连接数
DB_MAX_OVERFLOW = 10
# 连接池大小，0表示连接数无限制
DB_POOL_SIZE = 50
# 连接回收时间（单位：秒）
DB_POOL_RECYCLE = 3600
# 连接池中没有线程可用时，最多等待的时间（单位：秒）
DB_POOL_TIMEOUT = 30

# -------- Redis配置 -- ------
# Redis主机
REDIS_HOST = 'r-wz9b82d6941522a4pd.redis.rds.aliyuncs.com'
# Redis端口
REDIS_PORT = 6379
# Redis用户名
REDIS_USERNAME = ''
# Redis密码
REDIS_PASSWORD = 'Xyjrds#1811517%!$'
# Redis数据库
REDIS_DATABASE = 9




# -------- 阿里云短信配置 --------
# 阿里云短信AccessKey ID
SMS_ACCESS_KEY_ID = 'LTAIfTID1WDWpmTL'
# 阿里云短信AccessKey Secret
SMS_ACCESS_KEY_SECRET = 'S7SQEdtiAcuZDTK4ATYSTooSfrVs4H'
# 阿里云短信签名名称
SMS_SIGN_NAME = '金刚到家'
# 阿里云短信API地址
SMS_API_URL = 'https://dysmsapi.aliyuncs.com'
# 阿里云短信验证码模板ID
SMS_VERIFICATION_TEMPLATE_CODE = 'SMS_485335604'
# 短信验证码有效期（分钟）
SMS_EXPIRE_MINUTES = 5

# -------- 微信小程序配置 --------
# 微信小程序AppID
WECHAT_APP_ID = 'wx3aa06d66a59ba971'
# 微信小程序AppSecret（需要从微信公众平台获取）
WECHAT_APP_SECRET = '951def82a5162069ac96a258a824f748'
# 微信API基础URL
WECHAT_API_URL = 'https://api.weixin.qq.com'

# -------- 微信公众号配置 --------
# 微信公众号AppID（需要从微信公众平台获取）
WECHAT_OFFICIAL_APP_ID = 'wx07bc9d280d8ba946'
# 微信公众号AppSecret（需要从微信公众平台获取）
WECHAT_OFFICIAL_APP_SECRET = 'dd0ca4a6a9890aec675b1fd4afa5bc14'
# 微信公众号Token（自定义，需要在微信后台配置）
WECHAT_OFFICIAL_TOKEN = 'jingangai'
# 微信公众号EncodingAESKey（43位字符，用于消息加解密）
WECHAT_OFFICIAL_ENCODING_AES_KEY = 'zBBXBdQkQa8WIbYdBnG6X3RT7RAMwyVO93wlx7lffHj'

# -------- 百度语音识别配置 --------
# 百度语音识别API Key（需要从百度智能云控制台获取）
BAIDU_VOICE_API_KEY = '25b8CStEtahAY5AMQzmR2vg0'
# 百度语音识别Secret Key（需要从百度智能云控制台获取）
BAIDU_VOICE_SECRET_KEY = 'pL8xbpR5Jj1ZQys1FmXDStZxvZkgt5hB'

# -------- 百度OCR识别配置 --------
# 百度OCR API Key（需要从百度智能云控制台获取）
BAIDU_OCR_API_KEY = 'mrRrOTYQxlFsKjngdvrE4XMw'
# 百度OCR Secret Key（需要从百度智能云控制台获取）
BAIDU_OCR_SECRET_KEY = 'o6kIJqIgXkVgDLVoUNsaukjNLfxqXDKk'
# 百度OCR App ID（需要从百度智能云控制台获取）
BAIDU_OCR_APP_ID = '118514956'

# -------- 易宝支付配置 --------
# 易宝支付API地址
YEEPAY_API_URL = 'https://api.kuaijie-pay.com/forward/pay/txn/v2/wxpay/pay/miniapp'
# 易宝支付客户代码（生产环境）
YEEPAY_CUSTOMER_CODE = 'C0024309C0001'
# 易宝支付版本号
YEEPAY_VERSION = '2.0'
# 易宝支付请求超时时间（秒）
YEEPAY_TIMEOUT = 30
# 易宝支付商户私钥（PKCS1格式，不含头尾）
YEEPAY_MERCHANT_PRIVATE_KEY = 'MIIEpQIBAAKCAQEA18MtlS4IC8cByCztmmBr1UKfRwmv4j/q6VgXoFkbqOomLgYdS3TDLXnLQEHnxr6XkWAQWFCx9P/sceiIfNY3J+wUiFGq0vcRfrGg1I5hIC3TnA8iu1ou1/u3FBzL383BMOFdwY5Seh+J//6XqnL73c4LyOL/cMLpHu3qGFpR0O5DT00bnqkiTdr+uekTyNM91k+6/0ebFaC47+X7y6lzXaDXy7MevZMaBJ2KfVIp62vphxL1RFS8Bem0bmyQbmclxjxLflpJuFmZUmIWQtHkXrLoJ7x2HrMa5QuVrefljbVYbaZGBBU6Xo1AcdZONB4aA+HaoZjNO4OkDHaO/JAt5wIDAQABAoIBAEc+Y2bG9FEvPR9j9nDrNbANd5p1UGZcxKyYMfT4d3GnH0RDMJkhzPDPLpXhLS8q79FI7xy6SIilmOVj6u2Nrli4U29PyCAxBM3YyUpmWRvPBfiZ5tYGSUF4FFPHzmnv/sKtJ9AptoIst9yXrGzT2ua6ipmimsYZFfDojkvMRwe9ibOE048sd0ej36qh92K1ztwm38rcHBpUNOveFxQhTdqRQ/YdNH0ShAJ28pO5XcDM95yFfXMwkLNjGh4ORrg/GNZ63v9QBAqA0nVIu9PSRtozDKj4Oay608YXc2D6eHw8B4n8ACgx0VKx0KoujmpeTk6nVEZBFDAfH7lXC/bhm4ECgYEA7hq5Kt9NATEeRmgZtoAU7NVm4E4nzUGCjgxe/9qFSYhGW+lE7dl0uLolSGEi93FLJZo7svCWQinkJcNix5JfOLpjypfXFtCRfL3PLgmXYb6zsavAPNxQhELCJ8yAPlJ5bqFw3RCpJgRiOvJFS8I2wW0w2S4l8Qzq//OiLAMJv+8CgYEA5/qUzZbCW71+E6oW41VE4BNL5S0miSOESuBGHwbnYZ/8tqm6QBnivovs46DShw8nMimiPNR6MeL65H4UbCFVO9Cm8TY17uylOcBy5+LAHBPQMMImZtEkl/KxOz5FkNEvL9q9wp6aDm5iQFEbjgb/8cBcXP76i1h7fmzKGlDn+YkCgYEA46bhS8Bcm7seuyplCzzCxP30cpnHLFRqQu5JFk8UCQ4mV1Jc+RxkoQo5A078Y6EKvFUOpzOoNnJfFEwPccjdChwLtXQfzkx0DdRc9iNtfaMbo9AW6RmsUgiSIgNAylWIjOIGSxbW4uZmFP32i0yRlbaUcdHRrPsw5K9IatQsyjkCgYEAr3t3JS5uSEfIH8gDGGVgfMEcIHlPNV6QrLOLV905GziTSvqJ0gPC6rGeuXLmzxQ/akrkV2/EIAi8Eizt9f4hI+Zq4v1HPz30nwNd6Nw2xwaV7sxvkEeVjvwf5djtdudG8sRd1+NyVohZ+p1gYsgYxR/mvtKDZPXCNZjGKbIaSUECgYEAiEdKClmu3JoVL6/fwIrIcyo/hL6lVbMmy7xCTekQhqrzdK8SXgU/Qnc0itfk6YCu6he6RLwrbhGci0EjZ954MxxaRStX9GJ8+GVGRMlzg6602Vhre5rDdJbDD13wmhy9Z1Ux4AdLBpIdv1tZNBaii3ewG/ZfGh+bfmBjBqt+h+4='
# 易宝支付平台公钥（不含头尾）
YEEPAY_PLATFORM_PUBLIC_KEY = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr7kN3GycZjK0McKi350AZsn8Djm23S2Z+OOrDxaa9DhEStT1aBJjS1DRCi/O4f30mGOq/isi/FXR14CXvTepP4AAcyxvLUn9R4N18Pyzo/CDqXEVXOtNvYe5/tNUkpQ0ZDl+tMN91xH5unNmAgGeTcQnl0quzD7dPl/+8P8BT50ovid3Osnh6iVa5I9mqXhpF+PUUnvjAZQGwMBtjL0HQdnNWmNwVA8UtnwQCFPjt9JmLHm4+bvZqLCUsbagfD3jTZF9Ula4FXARwzEZ+mHHXllEGGy8JOohqZBREeAwUn5Us1sUtCrhIxkYdm48USW/tdZjWzljjd/bkUu5Jj5IDQIDAQAB'