import { post, get } from '../utlis/require.js'

/**
 * 公告管理相关API
 */

// 获取公告列表
export const getAnnouncementList = (params = {}) => {
  return get('/api/v1/announcement/list', params)
}

// 获取公告详情
export const getAnnouncementDetail = (announcementId) => {
  return get(`/api/v1/announcement/detail/${announcementId}`)
}

// 获取最新公告（用于首页点击跳转）
export const getLatestAnnouncement = () => {
  return get('/api/v1/announcement/latest')
}

// 获取首页公告列表（滚动播报用）
export const getHomeAnnouncements = (params = {}) => {
  return get('/api/v1/announcement/home', params)
}

export default {
  getAnnouncementList,
  getAnnouncementDetail,
  getLatestAnnouncement,
  getHomeAnnouncements
}
