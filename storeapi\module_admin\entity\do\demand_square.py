from sqlalchemy import Column, String, Integer, DECIMAL, DateTime, Text, BigInteger
from config.database import Base
from datetime import datetime

class DemandSquare(Base):
    """需求广场实体类"""
    
    __tablename__ = 'demand_square'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    uuid = Column(String(64), nullable=False, unique=True, index=True, comment='需求唯一标识符')
    platform_order_number = Column(String(100), nullable=True, unique=False, index=True, comment='云平台订单号')
    jingang_product_id = Column(String(64), nullable=True, comment='金刚到家关联产品ID')
    jingang_order_number = Column(String(100), nullable=True, comment='金刚到家订单号')
    business_type = Column(Integer, nullable=False, default=1, index=True, comment='业务类型：1-到家，2-到店')
    business_status = Column(Integer, nullable=False, default=1, comment='业务状态：1-实单，0-虚单')
    commission_amount = Column(DECIMAL(10, 2), nullable=False, default=0.00, index=True, comment='佣金金额（如果为0就是面议）')
    service_project = Column(String(200), nullable=False, comment='服务项目')
    service_address = Column(String(500), nullable=False, comment='服务地址')
    service_time = Column(DateTime, nullable=False, index=True, comment='服务时间')
    lng = Column(String(20), nullable=True, comment='经度')
    lat = Column(String(20), nullable=True, comment='纬度')
    demand_status = Column(Integer, nullable=False, default=1, index=True, comment='线索状态：-1-已过期，1-待抢单，2-已抢单')
    grab_store_uuid = Column(String(64), nullable=True, index=True, comment='抢单门店UUID')
    grab_store_name = Column(String(100), nullable=True, comment='抢单门店名称')
    grab_user_uuid = Column(String(64), nullable=True, comment='抢单用户UUID')
    grab_user_name = Column(String(50), nullable=True, comment='抢单用户姓名')
    grab_time = Column(DateTime, nullable=True, index=True, comment='抢单时间')
    expire_time = Column(DateTime, nullable=False, index=True, comment='过期时间')
    customer_name = Column(String(50), nullable=True, comment='客户姓名')
    customer_phone = Column(String(20), nullable=True, comment='客户电话')
    customer_address = Column(String(500), nullable=True, comment='客户地址')
    service_requirements = Column(Text, nullable=True, comment='服务要求详情')
    source_platform = Column(String(50), nullable=True, comment='来源平台')
    platform_fee = Column(DECIMAL(10, 2), nullable=True, default=0.00, comment='平台费用')
    remark = Column(Text, nullable=True, comment='备注信息')
    created_order_number = Column(String(64), nullable=True, comment='生成的订单编号（抢单成功后）')
    source_order_id = Column(BigInteger, nullable=True, comment='原始订单ID（共享订单专用）')
    original_seller_store_uuid = Column(String(50), nullable=True, comment='原始卖方门店UUID（防止自抢单）')
    is_delete = Column(Integer, nullable=True, default=0, comment='是否删除：0-未删除，1-已删除')
    create_time = Column(DateTime, nullable=False, default=datetime.now, index=True, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    def __repr__(self):
        return f"<DemandSquare(id='{self.id}', uuid='{self.uuid}', service_project='{self.service_project}')>"
