"""
公告管理控制器
"""
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from module_admin.service.announcement_service import AnnouncementService
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import BusinessException, QueryException
from config.get_db import get_db
from utils.log_util import logger

router = APIRouter(prefix='/api/v1/announcement')


@router.get("/list")
async def get_announcement_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    status: Optional[int] = Query(None, description="状态筛选"),
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取公告列表
    
    支持分页、关键词搜索和状态筛选
    """
    try:
        result = await AnnouncementService.get_announcement_list_service(
            query_db, page, size, keyword, status
        )
        
        return ResponseUtil.success(
            msg="获取公告列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"获取公告列表查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取公告列表业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取公告列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@router.get("/detail/{announcement_id}")
async def get_announcement_detail(
    announcement_id: int,
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取公告详情
    """
    try:
        result = await AnnouncementService.get_announcement_detail_service(
            query_db, announcement_id
        )
        
        return ResponseUtil.success(
            msg="获取公告详情成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"获取公告详情查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取公告详情业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取公告详情异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@router.get("/latest")
async def get_latest_announcement(
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取最新公告（用于首页展示）
    """
    try:
        result = await AnnouncementService.get_latest_announcement_service(query_db)
        
        return ResponseUtil.success(
            msg="获取最新公告成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"获取最新公告查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取最新公告业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取最新公告异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])


@router.get("/home")
async def get_home_announcements(
    limit: int = Query(5, ge=1, le=10, description="获取数量"),
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取首页公告列表（滚动播报用）
    """
    try:
        result = await AnnouncementService.get_home_announcements_service(
            query_db, limit
        )
        
        return ResponseUtil.success(
            msg="获取首页公告列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"获取首页公告列表查询异常: {e.message}")
        return ResponseUtil.database_error(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"获取首页公告列表业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取首页公告列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
