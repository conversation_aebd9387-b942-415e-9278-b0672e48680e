"""
产品相关的VO模型
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from decimal import Decimal


class ProductModel(BaseModel):
    """产品模型"""
    id: str = Field(..., description="产品ID")
    name: str = Field(..., description="产品名称")
    category: str = Field(..., description="产品分类")


class PurchasableProductModel(BaseModel):
    """可购买产品模型"""
    id: int = Field(..., description="产品ID")
    uuid: Optional[str] = Field(None, description="产品UUID")
    product_name: str = Field(..., description="产品名称")
    service_skill_name: str = Field(..., description="服务技能名称")
    service_skill_main_name: Optional[str] = Field(None, description="主要服务技能名称")
    type: str = Field(..., description="产品类型")
    type_name: str = Field(..., description="产品类型名称")
    img_id: Optional[str] = Field(None, description="图片ID")
    details: Optional[str] = Field(None, description="产品详情")
    min_number: int = Field(0, description="最小数量")
    max_number: int = Field(0, description="最大数量")
    product_status: int = Field(0, description="产品状态")
    company_uuid: str = Field(..., description="所属公司UUID")
    is_own_product: bool = Field(..., description="是否为本公司产品")
    buy_notes: Optional[str] = Field(None, description="购买须知")
    buy_agreement: Optional[str] = Field(None, description="购买协议")


class ProductQueryModel(BaseModel):
    """产品查询参数模型"""
    company_id: Optional[str] = Field(None, description="公司ID")


class PurchasableProductQueryModel(BaseModel):
    """可购买产品查询参数模型"""
    company_uuid: str = Field(..., description="公司UUID")
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
    search_keyword: Optional[str] = Field(None, description="搜索关键词")
    service_skill_id: Optional[str] = Field(None, description="服务技能ID筛选")


class ProductResponseModel(BaseModel):
    """产品响应模型"""
    is_success: bool = Field(True, description="是否成功")
    message: str = Field("获取产品列表成功", description="响应消息")
    result: List[ProductModel] = Field([], description="产品列表")


class PurchasableProductResponseModel(BaseModel):
    """可购买产品响应模型"""
    is_success: bool = Field(True, description="是否成功")
    message: str = Field("获取可购买产品列表成功", description="响应消息")
    result: dict = Field(..., description="产品列表数据")

    class Config:
        json_encoders = {
            Decimal: str
        }


class SkuInfoModel(BaseModel):
    """SKU信息模型"""
    name: str = Field(..., description="SKU名称", min_length=1, max_length=100)
    now_price: float = Field(..., description="现价", gt=0)
    vip_price: float = Field(..., description="会员价", gt=0)
    duration: int = Field(..., description="服务时长(分钟)", gt=0)
    type_price_unit: str = Field(..., description="价格单位")
    define_commission: float = Field(..., description="提成价格", ge=0)
    commission_type: int = Field(0, description="提成类型")


class CreateProductRequestModel(BaseModel):
    """创建产品请求模型"""
    product_name: str = Field(..., description="产品名称", min_length=1, max_length=100)
    service_skill_id: int = Field(..., description="服务技能ID")
    service_skill_name: str = Field(..., description="服务技能名称")
    service_skill_main_id: int = Field(..., description="主服务技能ID")
    main_image_id: Optional[int] = Field(None, description="主图ID")
    main_image_url: Optional[str] = Field(None, description="主图URL（用于AI生成的图片）")
    detail_image_ids: Optional[List[int]] = Field(default_factory=list, description="详情图ID列表")
    min_number: int = Field(1, ge=1, description="最小购买数量")
    max_number: int = Field(10, ge=1, description="最大购买数量")
    buy_notes: Optional[str] = Field("", description="购买须知")
    buy_agreement: Optional[str] = Field("", description="购买协议")
    business_hours: Optional[str] = Field("08:00-18:00", description="可预约时间", pattern=r"^([0-1]?[0-9]|2[0-3]):[0-5][0-9]-([0-1]?[0-9]|2[0-3]):[0-5][0-9]$")
    sku_info: SkuInfoModel = Field(..., description="SKU信息")


class CreateProductResponseModel(BaseModel):
    """创建产品响应模型"""
    product_uuid: str = Field(..., description="产品UUID")
    product_name: str = Field(..., description="产品名称")
    main_image_id: int = Field(..., description="主图ID")
    detail_image_ids: List[int] = Field(..., description="详情图ID列表")
