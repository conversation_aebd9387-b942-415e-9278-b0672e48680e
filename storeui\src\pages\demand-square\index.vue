<template>
  <view class="demand-square-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="nav-content">
        <view class="nav-left" @click="goBack">
          <u-icon name="arrow-left" color="#333" size="20"></u-icon>
        </view>
        <view class="nav-title">家政人广场</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 业务类型切换选项卡 -->
    <view class="tab-header">
      <view
        class="tab-item"
        :class="{ active: activeTab === 'home' }"
        @click="switchTab('home')"
      >
        到家
        <view class="tab-line" v-if="activeTab === 'home'"></view>
      </view>
      <view
        class="tab-item"
        :class="{ active: activeTab === 'store' }"
        @click="switchTab('store')"
      >
        到店
        <view class="tab-line" v-if="activeTab === 'store'"></view>
      </view>
    </view>

    <!-- 统计信息容器 -->
    <view class="stats-container">
      <view class="stats-card" :class="{ 'two-column': activeTab === 'store' }">
        <view class="stats-item">
          <view class="stats-number">{{ totalCount }}</view>
          <view class="stats-label">可抢线索</view>
        </view>
        <view class="stats-divider" v-if="activeTab === 'home'"></view>
        <view class="stats-item" v-if="activeTab === 'home'">
          <view class="stats-number">¥{{ totalAmount }}</view>
          <view class="stats-label">总金额</view>
        </view>
        <view class="stats-divider" v-if="activeTab === 'home'"></view>
        <view class="stats-item" v-if="activeTab === 'home'">
          <view class="stats-number">¥{{ userBalance }}</view>
          <view class="stats-label">我的余额</view>
        </view>
        <view class="stats-item" v-if="activeTab === 'store'">
          <view class="stats-number">{{ todayCount }}</view>
          <view class="stats-label">今日新增</view>
        </view>
      </view>
    </view>

    <!-- 订单列表容器 -->
    <scroll-view
      scroll-y
      class="order-list-container"
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <!-- 订单列表 -->
      <view class="order-list">
        <view
          class="order-item"
          :class="{ 'store-compact': activeTab === 'store' }"
          v-for="(order, index) in orderList"
          :key="index"
          @click="order.isExpired ? null : (activeTab === 'store' ? purchaseStoreLead(order) : grabOrder(order))"
        >
          <!-- 订单头部 -->
          <view class="order-header">
            <view class="order-info">
              <text class="service-name">{{ order.serviceName }}</text>
              <text class="order-time">{{ formatTime(order.createTime) }}</text>
            </view>
            <view class="order-status">
              <view class="status-tag" :class="{ 'expired': order.isExpired }">
                {{ order.statusText }}
              </view>
              <view class="countdown-container" v-if="!order.isExpired && order.remainingTime > 0">
                <view class="countdown-label">剩余时间</view>
                <view class="countdown-time">{{ formatCountdown(order.remainingTime) }}</view>
              </view>
              <view class="expired-notice" v-if="order.isExpired">
                <text class="expired-text">已过期，无法抢单</text>
              </view>
            </view>
          </view>

          <!-- 订单内容 -->
          <view class="order-content">
            <!-- 到家页面：保持原来的显示方式 -->
            <template v-if="activeTab === 'home'">
              <view class="customer-info">
                <view class="info-row">
                  <u-icon name="account" color="#333" size="16"></u-icon>
                  <text class="info-text">{{ order.customerName }}</text>
                </view>
                <view class="info-row">
                  <u-icon name="map" color="#333" size="16"></u-icon>
                  <text class="info-text">{{ order.address }}</text>
                </view>
                <view class="info-row" v-if="order.serviceTime">
                  <u-icon name="clock" color="#333" size="16"></u-icon>
                  <text class="info-text service-time-text">{{ order.serviceTime }}</text>
                </view>
              </view>

              <!-- 服务详情 -->
              <view class="service-details">
                <text class="service-desc">{{ order.serviceDesc }}</text>
                <view class="price-info">
                  <text class="price">¥{{ order.price }}</text>
                </view>
              </view>
            </template>

            <!-- 到店页面：新的显示方式 -->
            <template v-if="activeTab === 'store'">
              <!-- 服务信息 -->
              <view class="service-info">
                <view class="info-row">
                  <u-icon name="star" color="#fdd118" size="16"></u-icon>
                  <text class="info-text service-name">{{ order.serviceName }}</text>
                </view>
              </view>

              <view class="customer-info">
                <view class="info-row">
                  <u-icon name="map" color="#333" size="16"></u-icon>
                  <text class="info-text">{{ getAreaText(order.address) }}</text>
                </view>
                <view class="info-row" v-if="order.serviceTime">
                  <u-icon name="clock" color="#333" size="16"></u-icon>
                  <text class="info-text service-time-text">{{ order.serviceTime }}</text>
                </view>
              </view>

              <!-- 服务详情 -->
              <view class="service-details">
                <view class="price-info">
                  <text class="price">¥{{ order.price }}</text>
                </view>
              </view>
            </template>
          </view>

          <!-- 订单底部操作 -->
          <view class="order-footer">
            <view class="order-tags">
              <text class="tag business-type" v-if="activeTab === 'home'">到家服务</text>
              <text class="tag distance" v-if="order.distance">{{ order.distance }}km</text>
            </view>
            <view
              class="grab-btn"
              :class="{ 'disabled': order.isExpired }"
              @click.stop="order.isExpired ? null : (activeTab === 'store' ? purchaseStoreLead(order) : grabOrder(order))"
            >
              <text>{{ order.isExpired ? '已过期' : (activeTab === 'store' ? '购买线索' : '立即抢单') }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && orderList.length === 0">
        <u-empty mode="order" text="暂无可抢订单"></u-empty>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="loading">
        <u-loading-icon mode="circle" color="#fdd118" size="20"></u-loading-icon>
        <text class="load-text">加载中...</text>
      </view>
    </scroll-view>

    <!-- 员工选择弹窗 -->
    <view v-if="showStaffSelector" class="staff-selector-mask" @click="cancelStaffSelection">
      <view class="staff-selector-popup" @click.stop>
        <view class="staff-selector">
          <view class="header">
            <text class="title">选择服务人员</text>
            <text class="subtitle">单选</text>
          </view>

          <scroll-view scroll-y class="staff-list">
            <view
              class="staff-item"
              :class="{ selected: selectedStaffId === staff.id }"
              v-for="staff in availableStaffList"
              :key="staff.id"
              @click="selectStaff(staff)"
            >
              <view class="staff-info">
                <text class="staff-name">{{ staff.real_name || staff.user_name }}</text>
                <text class="staff-level">{{ staff.star_level }}星</text>
              </view>
              <view class="staff-stats">
                <text>服务{{ staff.service_cnt || 0 }}次</text>
              </view>
              <view v-if="selectedStaffId === staff.id" class="check-mark">
                <u-icon name="checkbox-mark" color="#fff" size="14"></u-icon>
              </view>
            </view>
          </scroll-view>

          <view class="footer">
            <button class="cancel-btn" @click="cancelStaffSelection">取消</button>
            <button class="confirm-btn" @click="confirmStaffSelection" :disabled="!selectedStaffId">确认</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDemandList, getDemandStats, grabDemand, getAvailableStaffList, selectServiceStaff, cancelDemandGrab, purchaseStoreLead, getUserBalance } from '@/api/demand-square.js';
import { ErrorHandler, SuccessHandler } from '@/utils/errorHandler.js';

export default {
  name: 'DemandSquare',
  data() {
    return {
      statusBarHeight: 0,
      activeTab: 'home', // 'home' | 'store'
      orderList: [],
      loading: false,
      isRefreshing: false,
      page: 1,
      pageSize: 20,
      hasMore: true,
      // 统计数据
      totalCount: 0,
      totalAmount: '0.00',
      todayCount: 0,
      // 倒计时定时器
      countdownTimer: null,
      // 员工选择相关
      showStaffSelector: false,
      availableStaffList: [],
      selectedStaffId: null,
      currentGrabbedOrder: null, // 当前抢单成功的订单
      // 用户余额
      userBalance: 0
    };
  },
  
  onLoad() {
    this.initPage();
    this.getUserBalance();
  },

  onShow() {
    // 页面显示时启动倒计时
    this.startCountdown();
  },

  onHide() {
    // 页面隐藏时停止倒计时
    this.stopCountdown();
  },

  onUnload() {
    // 页面卸载时停止倒计时
    this.stopCountdown();
  },

  methods: {
    // 获取用户余额
    async getUserBalance() {
      try {
        const response = await getUserBalance();
        if (response && response.balance !== undefined) {
          this.userBalance = response.balance || 0;
          console.log('余额更新:', this.userBalance);
        }
        return response;
      } catch (error) {
        console.error('获取余额失败:', error);
        return null;
      }
    },

    // 检查线索购买状态
    checkPurchaseStatus(order) {
      // 检查线索是否已被购买（状态为3）
      if (order.status === 3 || order.statusText === '已售出') {
        // 检查是否为自己门店购买的线索
        const userInfo = uni.getStorageSync('userInfo');
        if (userInfo && userInfo.store_uuid && order.grabStoreUuid === userInfo.store_uuid) {
          // 自己门店购买的线索，不在广场显示，但不阻止操作
          return {
            canPurchase: false,
            reason: '您已购买此线索，请在线索管理中查看'
          };
        } else {
          // 其他门店购买的线索
          return {
            canPurchase: false,
            reason: '该线索已被其他门店购买'
          };
        }
      }

      // 检查线索是否已过期
      if (order.isExpired) {
        return {
          canPurchase: false,
          reason: '该线索已过期'
        };
      }

      return {
        canPurchase: true,
        reason: ''
      };
    },

    // 初始化页面
    initPage() {
      // 获取状态栏高度
      const systemInfo = uni.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight;
      
      // 加载订单列表
      this.loadOrderList(true);
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 切换业务类型
    switchTab(tab) {
      if (this.activeTab === tab) return;

      this.activeTab = tab;
      this.page = 1;
      this.hasMore = true;
      this.loadOrderList(true);
    },

    // 加载订单列表
    async loadOrderList(refresh = false) {
      if (this.loading) return;

      this.loading = true;

      try {
        // 构建查询参数
        const params = {
          page: refresh ? 1 : this.page,
          size: this.pageSize,
          business_type: this.activeTab === 'home' ? 1 : 2,
          demand_status: 1 // 只查询待抢单的需求
        };

        // 调用真实API
        const response = await getDemandList(params);

        if (response) {
          const { list, total, pages } = response;

          // 转换数据格式
          const formattedList = list.map(item => this.formatDemandItem(item));

          if (refresh) {
            this.orderList = formattedList;
            this.page = 1;
          } else {
            this.orderList = [...this.orderList, ...formattedList];
          }

          this.hasMore = this.page < pages;

          // 更新统计数据
          await this.loadStats();

          // 计算额外统计数据（基于当前订单列表）
          this.calculateAdditionalStats();

          // 启动倒计时
          this.startCountdown();
        }

      } catch (error) {
        console.error('加载订单列表失败:', error);
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.isRefreshing = false;
      }
    },

    // 加载统计数据
    async loadStats() {
      try {
        const params = {
          business_type: this.activeTab === 'home' ? 1 : 2
        };

        const response = await getDemandStats(params);

        if (response) {
          this.totalCount = response.total_count || 0;
          this.totalAmount = parseFloat(response.total_amount || 0).toFixed(2);
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
      }
    },

    // 计算额外的统计数据
    calculateAdditionalStats() {
      if (!this.orderList || this.orderList.length === 0) {
        this.todayCount = 0;
        return;
      }

      // 计算今日新增线索数量
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      this.todayCount = this.orderList.filter(order => {
        const createDate = new Date(order.createTime);
        createDate.setHours(0, 0, 0, 0);
        return createDate.getTime() === today.getTime();
      }).length;
    },

    // 格式化需求数据
    formatDemandItem(item) {
      return {
        id: item.id,
        uuid: item.uuid,
        serviceName: this.formatServiceType(item.service_project),
        customerName: item.customer_name || '客户',
        address: item.service_address,
        serviceTime: item.service_time ? this.formatServiceTime(item.service_time) : null,
        serviceDesc: item.service_requirements || item.service_project,
        price: parseFloat(item.commission_amount || 0),
        status: item.demand_status,
        remainingTime: item.remaining_seconds || 0,
        distance: null, // 暂时不计算距离
        createTime: new Date(item.create_time).getTime(),
        isExpired: item.demand_status === -1, // 是否已过期
        expireTime: item.expire_time, // 过期时间
        statusText: item.status_text || '未知', // 状态文本
        grabStoreUuid: item.grab_store_uuid, // 线索归属门店UUID
        grabStoreName: item.grab_store_name, // 线索归属门店名称
        grabUserUuid: item.grab_user_uuid,   // 线索归属用户UUID
        grabUserName: item.grab_user_name    // 线索归属用户名称
      };
    },

    // 格式化服务时间
    formatServiceTime(timeStr) {
      try {
        const date = new Date(timeStr);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return timeStr;
      }
    },

    // 下拉刷新
    onRefresh() {
      this.isRefreshing = true;
      this.page = 1;
      this.hasMore = true;
      this.loadOrderList(true);
    },

    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading) return;

      this.page++;
      this.loadOrderList(false);
    },

    // 刷新订单列表
    refreshOrderList() {
      this.page = 1;
      this.hasMore = true;
      this.loadOrderList(true);
    },

    // 购买到店线索
    purchaseStoreLead(order) {
      console.log('开始购买线索验证:', {
        orderUuid: order.uuid,
        orderPrice: order.price,
        grabStoreUuid: order.grabStoreUuid
      });

      // 0. 检查线索购买状态
      const statusCheck = this.checkPurchaseStatus(order);
      if (!statusCheck.canPurchase) {
        uni.showToast({
          title: statusCheck.reason,
          icon: 'none'
        });
        return;
      }

      // 1. 门店归属验证
      const userInfo = uni.getStorageSync('userInfo');
      if (!userInfo || !userInfo.store_uuid) {
        console.error('用户信息获取失败:', userInfo);
        uni.showToast({
          title: '获取门店信息失败',
          icon: 'none'
        });
        return;
      }

      console.log('用户门店信息:', {
        userStoreUuid: userInfo.store_uuid,
        grabStoreUuid: order.grabStoreUuid
      });

      // 检查是否为自己门店的线索
      if (order.grabStoreUuid === userInfo.store_uuid) {
        console.warn('尝试购买自己门店的线索');
        uni.showModal({
          title: '无法购买',
          content: '不能购买自己门店的线索',
          showCancel: false
        });
        return;
      }

      // 2. 余额验证
      console.log('开始余额验证');
      this.getUserBalance().then(() => {
        console.log('余额验证结果:', {
          currentBalance: this.userBalance,
          requiredAmount: order.price,
          sufficient: this.userBalance >= order.price
        });

        // 检查余额是否充足
        if (this.userBalance < order.price) {
          uni.showModal({
            title: '余额不足',
            content: `当前余额：¥${this.userBalance}，需要：¥${order.price}，请先充值`,
            showCancel: false,
            confirmText: '知道了'
          });
          return;
        }

        // 3. 购买确认
        uni.showModal({
          title: '确认购买',
          content: `购买金额：¥${order.price}\n当前余额：¥${this.userBalance}\n购买后余额：¥${(this.userBalance - order.price).toFixed(2)}`,
          success: (res) => {
            if (res.confirm) {
              console.log('用户确认购买，开始执行购买');
              this.performPurchaseStoreLead(order);
            } else {
              console.log('用户取消购买');
            }
          }
        });
      }).catch(error => {
        console.error('获取余额失败:', error);
        uni.showToast({
          title: '获取余额失败，请重试',
          icon: 'none'
        });
      });
    },

    // 执行购买到店线索
    async performPurchaseStoreLead(order) {
      try {
        uni.showLoading({
          title: '购买中...',
          mask: true
        });

        const response = await purchaseStoreLead({
          demand_uuid: order.uuid
        });

        uni.hideLoading();

        if (response && response.success) {
          SuccessHandler.handleSuccess('购买线索');

          // 更新余额（使用接口返回的余额，避免重复查询）
          if (response.remaining_balance !== undefined) {
            this.userBalance = response.remaining_balance;
            console.log('购买成功，余额已更新:', this.userBalance);
          } else {
            // 只有在接口没有返回余额时才重新查询
            this.getUserBalance();
          }

          // 刷新列表（移除已购买的线索）
          this.refreshOrderList();

          // 跳转到线索详情
          setTimeout(() => {
            uni.navigateTo({
              url: `/pages-sister-business/detail?id=${response.new_customer_uuid}`
            });
          }, 2000);
        } else {
          // 使用统一错误处理
          ErrorHandler.handleBusinessError(response, '购买线索');
        }
      } catch (error) {
        uni.hideLoading();

        // 使用统一错误处理
        const errorMsg = ErrorHandler.handleApiError(error, '购买线索');

        // 只有在余额相关错误时才刷新余额
        if (errorMsg.includes('余额') || errorMsg.includes('不足')) {
          this.getUserBalance();
        }
      }
    },

    // 抢单操作
    grabOrder(order) {
      uni.showModal({
        title: '确认抢单',
        content: `确定要抢取这个${this.activeTab === 'home' ? '到家' : '到店'}订单吗？`,
        success: (res) => {
          if (res.confirm) {
            this.performGrabOrder(order);
          }
        }
      });
    },

    // 执行抢单
    async performGrabOrder(order) {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '抢单中...',
          mask: true
        });

        // 获取当前用户的门店信息
        const userInfo = uni.getStorageSync('userInfo');
        if (!userInfo || !userInfo.store_uuid) {
          uni.hideLoading();
          uni.showToast({
            title: '获取门店信息失败',
            icon: 'none'
          });
          return;
        }

        // 调用抢单API
        const response = await grabDemand({
          demand_uuid: order.uuid,
          store_uuid: userInfo.store_uuid
        });

        uni.hideLoading();

        if (response && response.success) {
          console.log('抢单成功，响应数据:', response);
          console.log('need_staff_selection:', response.need_staff_selection);

          // 检查是否需要选择员工（共享订单）
          if (response.need_staff_selection) {
            console.log('进入员工选择流程');
            // 共享订单抢单成功，需要选择员工
            this.currentGrabbedOrder = order;
            this.loadAvailableStaff();
          } else {
            console.log('普通订单，不需要选择员工');
            // 普通订单抢单成功
            uni.showToast({
              title: response.message || '抢单成功！',
              icon: 'success',
              duration: 2000
            });

            // 抢单成功后刷新列表
            setTimeout(() => {
              this.loadOrderList(true);
            }, 1000);

            // 根据业务类型跳转到不同页面
            if (order.business_type === 1) {
              // 到家订单：跳转到派单页面
              setTimeout(() => {
                uni.switchTab({
                  url: '/pages/dispatch/index'
                });
              }, 2000);
            } else if (order.business_type === 2) {
              // 到店订单：跳转到姐妹业务页面
              setTimeout(() => {
                uni.switchTab({
                  url: '/pages/sister-business/index'
                });
              }, 2000);
            }
          }

        } else {
          uni.showToast({
            title: response.message || '抢单失败',
            icon: 'none',
            duration: 3000
          });
        }

      } catch (error) {
        uni.hideLoading();
        console.error('抢单失败:', error);

        // 显示具体的错误信息
        const errorMsg = error.message || error.msg || '抢单失败，请重试';
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      }
    },

    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date;
      
      if (diff < 60000) {
        return '刚刚';
      } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`;
      } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`;
      } else {
        return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
      }
    },

    // 格式化服务类型显示
    formatServiceType(serviceProject) {
      if (!serviceProject) return '未知服务';

      // 完整的服务类型映射表（与数据库sys_dict_data保持一致）
      const serviceTypeMap = {
        '1001': '保姆',
        '1002': '产后修复师',
        '1003': '小儿推拿师',
        '1004': '催乳师',
        '1005': '保安',
        '1006': '中医师',
        '1007': '家教',
        '1008': '心理理疗师',
        '1009': '指导师',
        '1010': '家政员',
        '1011': '管家',
        '1012': '厨师',
        '1013': '月嫂',
        '1014': '育儿嫂',
        '1015': '老年护理',
        '1016': '小时工',
        '1017': '白班阿姨',
        '1018': '别墅家务',
        '1019': '病人护理',
        '1020': '育婴师',
        '1021': '早教',
        '1022': '其他',
        '1023': '单餐保姆'
      };

      // 处理多个服务类型（用逗号分隔）
      if (serviceProject.includes(',')) {
        const types = serviceProject.split(',');
        const typeNames = types.map(type => serviceTypeMap[type.trim()] || type.trim());
        return typeNames.join(',');
      }

      return serviceTypeMap[serviceProject] || serviceProject;
    },

    // 获取区域信息（适度隐藏具体地址）
    getAreaText(address) {
      if (!address) return '未知区域';

      // 提取区域信息，保留一定的地址信息但隐藏具体门牌号
      const areaMatch = address.match(/(.*?[市县区].*?[路街道])/);
      if (areaMatch) {
        return areaMatch[1];
      }

      // 如果没有匹配到，返回前面部分
      return address.length > 15 ? address.substring(0, 15) + '...' : address;
    },



    // 格式化倒计时
    formatCountdown(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;

      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '待抢单',
        2: '进行中',
        3: '即将过期',
        '-1': '已过期'
      };
      return statusMap[status] || '未知';
    },

    // 启动倒计时
    startCountdown() {
      this.stopCountdown(); // 先停止之前的定时器
      this.countdownTimer = setInterval(() => {
        this.updateCountdown();
      }, 1000);
    },

    // 停止倒计时
    stopCountdown() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
    },

    // 更新倒计时
    updateCountdown() {
      const currentTime = Date.now();
      let hasExpiredOrders = false;

      this.orderList.forEach(order => {
        if (!order.isExpired && order.remainingTime > 0) {
          // 计算新的剩余时间
          const expireTime = new Date(order.expireTime).getTime();
          const newRemainingTime = Math.max(0, Math.floor((expireTime - currentTime) / 1000));

          // 更新剩余时间
          order.remainingTime = newRemainingTime;

          // 如果时间到了，标记为过期
          if (newRemainingTime <= 0) {
            order.isExpired = true;
            order.status = -1;
            order.statusText = '已过期';
            hasExpiredOrders = true;
          }
        }
      });

      // 如果有订单过期，强制更新视图
      if (hasExpiredOrders) {
        this.$forceUpdate();
      }
    },

    // 加载可用员工列表
    async loadAvailableStaff() {
      try {
        console.log('开始加载员工列表');
        uni.showLoading({
          title: '加载员工列表...'
        });

        // 获取当前门店的可用员工
        const userInfo = uni.getStorageSync('userInfo');
        console.log('用户信息:', userInfo);

        const response = await getAvailableStaffList({
          store_uuid: userInfo.store_uuid
        });

        console.log('员工列表响应:', response);
        uni.hideLoading();

        if (response && response.list) {
          console.log('员工列表数量:', response.list.length);
          this.availableStaffList = response.list;
          this.showStaffSelector = true;
          console.log('显示员工选择器:', this.showStaffSelector);
        } else {
          console.log('员工列表为空或响应异常');
          uni.showToast({
            title: '获取员工列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('加载员工列表失败:', error);
        uni.showToast({
          title: '加载员工列表失败',
          icon: 'none'
        });
      }
    },



    // 选择员工
    selectStaff(staff) {
      this.selectedStaffId = staff.id;
    },

    // 取消员工选择
    async cancelStaffSelection() {
      try {
        // 显示确认弹窗
        uni.showModal({
          title: '确认取消',
          content: '取消后将退回需求广场，其他门店可以继续抢单，确定要取消吗？',
          success: async (res) => {
            if (res.confirm) {
              // 用户确认取消，调用取消抢单API
              uni.showLoading({
                title: '取消中...'
              });

              try {
                const response = await this.cancelDemandGrab({
                  demand_uuid: this.currentGrabbedOrder.uuid
                });

                uni.hideLoading();

                if (response && response.success !== false) {
                  uni.showToast({
                    title: '已取消抢单',
                    icon: 'success',
                    duration: 2000
                  });

                  // 关闭弹窗并清理状态
                  this.showStaffSelector = false;
                  this.selectedStaffId = null;
                  this.currentGrabbedOrder = null;
                  this.availableStaffList = [];

                  // 刷新需求列表
                  setTimeout(() => {
                    this.loadOrderList(true);
                  }, 1000);
                } else {
                  uni.showToast({
                    title: response.msg || '取消失败，请重试',
                    icon: 'none',
                    duration: 2000
                  });
                }
              } catch (error) {
                uni.hideLoading();
                console.error('取消抢单失败:', error);
                uni.showToast({
                  title: error.msg || error.message || '取消失败，请重试',
                  icon: 'none',
                  duration: 2000
                });
              }
            }
            // 如果用户点击取消，什么都不做，保持弹窗打开
          }
        });
      } catch (error) {
        console.error('取消员工选择操作失败:', error);
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },

    // 取消抢单API调用
    async cancelDemandGrab(data) {
      return await cancelDemandGrab(data);
    },

    // 选择员工
    selectStaff(staff) {
      this.selectedStaffId = staff.id;
    },

    // 确认员工选择
    async confirmStaffSelection() {
      if (!this.selectedStaffId || !this.currentGrabbedOrder) {
        return;
      }

      try {
        uni.showLoading({
          title: '处理中...'
        });

        // 调用员工选择API
        const response = await selectServiceStaff({
          demand_uuid: this.currentGrabbedOrder.uuid,
          selected_staff_id: this.selectedStaffId
        });

        uni.hideLoading();

        if (response && response.success !== false) {
          this.showStaffSelector = false;

          uni.showToast({
            title: '员工选择成功！您的员工已分配给该订单',
            icon: 'success',
            duration: 2000
          });

          // 刷新需求广场列表
          setTimeout(() => {
            this.loadOrderList(true);
          }, 500);

          // 共享订单接单成功后不跳转到派单页面
          // 因为接单门店只是提供员工服务，不需要管理订单
        } else {
          uni.showToast({
            title: response.msg || '员工选择失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('员工选择失败:', error);
        uni.showToast({
          title: '员工选择失败，请重试',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.demand-square-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

// 自定义导航栏
.custom-navbar {
  background-color: #fff;
  border-bottom: 1rpx solid #efefef;
  
  .nav-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    padding: 0 32rpx;
    
    .nav-left, .nav-right {
      width: 80rpx;
    }
    
    .nav-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #222222;
    }
  }
}

// 切换选项卡
.tab-header {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #efefef;
  
  .tab-item {
    flex: 1;
    height: 88rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #999999;
    position: relative;
    
    &.active {
      color: #fdd118;
      font-weight: 600;
    }
    
    .tab-line {
      position: absolute;
      bottom: 0;
      width: 60rpx;
      height: 4rpx;
      background-color: #fdd118;
      border-radius: 2rpx;
    }
  }
}

// 统计信息容器
.stats-container {
  padding: 20rpx;
  background-color: #f5f5f5;

  .stats-card {
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 16rpx;
    padding: 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    box-shadow: 0 8rpx 24rpx rgba(253, 209, 24, 0.3);

    // 到店页面两列布局
    &.two-column {
      justify-content: space-evenly;

      .stats-item {
        flex: 1;
      }
    }

    .stats-item {
      text-align: center;
      color: #fff;

      .stats-number {
        font-size: 48rpx;
        font-weight: 600;
        line-height: 1.2;
        margin-bottom: 8rpx;
      }

      .stats-label {
        font-size: 28rpx;
        opacity: 0.9;
      }
    }

    .stats-divider {
      width: 2rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 1rpx;
    }
  }
}

// 订单列表容器
.order-list-container {
  height: calc(100vh - 320rpx);
  padding: 20rpx;
}

// 订单列表
.order-list {
  .order-item {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    }
  }
}

// 到店页面紧凑样式
.order-list .order-item.store-compact {
  padding: 16rpx 20rpx;
  margin-bottom: 16rpx;

  .order-header {
    margin-bottom: 12rpx;
  }

  .order-content {
    margin-bottom: 12rpx;

    .service-info {
      margin-bottom: 8rpx;

      .info-row {
        margin-bottom: 6rpx;

        .service-name {
          font-weight: 500;
          color: #333;
        }
      }
    }

    .customer-info {
      margin-bottom: 8rpx;

      .info-row {
        margin-bottom: 6rpx;
      }
    }
  }

  .order-footer {
    margin-top: 8rpx;
  }
}

// 订单头部
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  
  .order-info {
    flex: 1;
    
    .service-name {
      display: block;
      font-size: 36rpx;
      font-weight: 600;
      color: #222222;
      margin-bottom: 8rpx;
    }

    .order-time {
      font-size: 26rpx;
      color: #999999;
    }
  }
  
  .order-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    
    .status-tag {
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      color: #fff;
      font-size: 24rpx;
      padding: 6rpx 14rpx;
      border-radius: 12rpx;
      margin-bottom: 8rpx;

      &.expired {
        background: #ff4757;
        color: #fff;
      }
    }

    .expired-notice {
      margin-top: 8rpx;

      .expired-text {
        font-size: 24rpx;
        color: #ff4757;
        font-weight: 500;
      }
    }

    .countdown-container {
      background: rgba(254, 64, 63, 0.1);
      border: 1rpx solid #fe403f;
      border-radius: 8rpx;
      padding: 8rpx 12rpx;
      text-align: center;
      min-width: 120rpx;

      .countdown-label {
        font-size: 20rpx;
        color: #fe403f;
        line-height: 1;
        margin-bottom: 4rpx;
      }

      .countdown-time {
        font-size: 26rpx;
        color: #fe403f;
        font-weight: 600;
        line-height: 1;
        font-family: 'Courier New', monospace;
      }
    }
  }
}

// 订单内容
.order-content {
  margin-bottom: 16rpx;

  // 到家页面的原始样式
  .customer-info {
    margin-bottom: 12rpx;

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .info-text {
        font-size: 30rpx;
        color: #333333;
        margin-left: 8rpx;
        font-weight: 500;

        &.service-time-text {
          font-size: 32rpx;
        }
      }
    }
  }



  .service-details {
    .service-desc {
      display: block;
      font-size: 30rpx;
      color: #333333;
      line-height: 1.5;
      margin-bottom: 16rpx;
    }

    .price-info {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .price-label {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
        margin-right: 16rpx;
      }

      .price {
        font-size: 40rpx;
        font-weight: 600;
        color: #fe403f;
      }
    }


  }
}

// 订单底部
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .order-tags {
    display: flex;
    gap: 12rpx;
    
    .tag {
      font-size: 24rpx;
      padding: 6rpx 12rpx;
      border-radius: 10rpx;

      &.business-type {
        background: rgba(253, 209, 24, 0.1);
        color: #fdd118;
      }

      &.distance {
        background: rgba(9, 190, 137, 0.1);
        color: #09be89;
      }
    }
  }
  
  .grab-btn {
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    color: #fff;
    font-size: 30rpx;
    font-weight: 600;
    padding: 14rpx 28rpx;
    border-radius: 26rpx;
    box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.3);

    &:active {
      transform: scale(0.95);
    }

    &.disabled {
      background: #cccccc;
      color: #999999;
      box-shadow: none;

      &:active {
        transform: none;
      }
    }
  }
}

// 空状态
.empty-state {
  padding: 120rpx 0;
  text-align: center;
}

// 加载更多
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;

  .load-text {
    font-size: 30rpx;
    color: #999999;
    margin-left: 16rpx;
  }
}

// 员工选择弹窗样式
.staff-selector {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  height: 100%;
  max-height: 70vh;
  display: flex;
  flex-direction: column;

  .header {
    flex-shrink: 0;
    padding: 40rpx 30rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
    text-align: center;

    .title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }

    .subtitle {
      font-size: 28rpx;
      color: #999;
    }
  }

  .staff-list {
    flex: 1;
    min-height: 0;
    max-height: 40vh;
    overflow-y: auto;
    padding: 20rpx 30rpx;

    .staff-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 20rpx;
      margin-bottom: 16rpx;
      background-color: #f8f9fa;
      border-radius: 16rpx;
      transition: all 0.3s ease;

      &.selected {
        background-color: #fff3e0;
        border: 2rpx solid #fdd118;
      }

      &:active {
        transform: scale(0.98);
      }

      .staff-info {
        flex: 1;

        .staff-name {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 8rpx;
        }

        .staff-level {
          font-size: 26rpx;
          color: #fdd118;
          font-weight: 500;
        }
      }

      .staff-stats {
        margin-right: 20rpx;

        text {
          font-size: 26rpx;
          color: #666;
        }
      }

      .check-mark {
        width: 40rpx;
        height: 40rpx;
        background-color: #fdd118;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .footer {
    flex-shrink: 0;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    display: flex;
    gap: 20rpx;

    .cancel-btn, .confirm-btn {
      flex: 1;
      height: 88rpx;
      border-radius: 44rpx;
      font-size: 32rpx;
      font-weight: 500;
      border: none;
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: #333;
    }

    .confirm-btn {
      background-color: #007AFF;
      color: white;

      &:disabled {
        background-color: #ccc;
        color: #999;
      }
    }
  }
}

/* 员工选择弹窗样式 */
.staff-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}

.staff-selector-popup {
  width: 100%;
  max-height: 70vh;
  background-color: white;
  border-radius: 20rpx 20rpx 0 0;
  animation: slideUp 0.3s ease-out;
  display: flex;
  flex-direction: column;
}



@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style>
