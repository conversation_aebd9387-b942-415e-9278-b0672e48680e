"""
产品数据模型
用于产品信息的数据库映射
"""
from datetime import datetime
from sqlalchemy import Column, String, Integer, Text, DECIMAL, DateTime
from config.database import Base


class Product(Base):
    """产品表"""

    __tablename__ = 'product'

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='产品ID')
    
    # 关联信息
    company_uuid = Column(String(100), nullable=True, comment='公司UUID')
    
    # 基本信息
    product_name = Column(String(100), nullable=False, comment='产品名称')
    img_id = Column(String(255), nullable=True, comment='图片ID')
    serve_type_name = Column(String(50), nullable=True, comment='服务类型名称')
    
    # 服务技能信息
    service_skill_id = Column(String(20), nullable=False, comment='服务技能ID')
    service_skill_name = Column(String(50), nullable=False, comment='服务技能名称')
    service_skill_main_id = Column(String(20), nullable=True, comment='主服务技能ID')
    service_skill_main_name = Column(String(50), nullable=True, comment='主服务技能名称')
    
    # 门店信息
    online_store_num = Column(Integer, nullable=False, default=0, comment='在线门店数量')
    is_all_support_store = Column(Integer, nullable=True, default=1, comment='是否所有门店支持')
    sum_num = Column(Integer, nullable=False, default=0, comment='总数量')
    
    # 状态信息
    is_delete = Column(Integer, nullable=False, default=0, comment='是否删除')
    is_open_service_phone = Column(Integer, nullable=False, default=0, comment='是否开启服务电话')
    product_status = Column(Integer, nullable=True, default=0, comment='产品状态')
    
    # 类型信息
    type = Column(String(20), nullable=False, comment='类型')
    type_name = Column(String(50), nullable=False, comment='类型名称')
    
    # 显示配置
    display_edit = Column(Integer, nullable=True, default=1, comment='显示编辑')
    display_delete = Column(Integer, nullable=True, default=1, comment='显示删除')
    display_detail = Column(Integer, nullable=True, default=1, comment='显示详情')
    display_edit_product_detail = Column(Integer, nullable=True, default=1, comment='显示编辑产品详情')
    
    # 其他配置
    is_gaode_line = Column(Integer, nullable=True, default=0, comment='是否高德线路')
    details = Column(String(2000), nullable=True, comment='详情')
    min_number = Column(Integer, nullable=False, default=0, comment='最小数量')
    max_number = Column(Integer, nullable=False, default=0, comment='最大数量')
    video_id = Column(String(64), nullable=True, comment='视频ID')
    uuid = Column(String(64), nullable=True, comment='UUID')
    
    # 系统字段
    op_user_name = Column(String(50), nullable=True, comment='操作用户名')
    op_time = Column(DateTime, nullable=True, comment='操作时间')
    
    def __repr__(self):
        return f"<Product(id={self.id}, product_name='{self.product_name}', company_uuid='{self.company_uuid}')>"
