import {post,get} from '../utlis/require.js'
import store from '../vuex/index.js'

// 获取公司列表
export const getCompanyList = data => get('/api/v1/company/getCompanyList', data)

// 获取公司详情
export const getCompanyDetail = data => get('/api/v1/company/getCompanyDetail', { company_id: data.company_id })

// 保存公司信息
export const saveCompany = data => post('/api/v1/company/saveCompany', data)

// 删除公司
export const deleteCompany = data => post('/api/v1/company/deleteCompany', { company_id: data.company_id })

// 获取公司账户
export const getCompanyAccount = data => get('/api/v1/company/getCompanyAccount', { company_id: data.company_id })

// 检查版本过期状态
export const checkVersionExpiry = () => get('/api/v1/company/check-version-expiry')

// 获取公司提现配置
export const getCompanyWithdrawalConfig = () => get('/api/v1/company/withdrawal-config')

// 获取公司产品
export const getCompanyProduct = data => get('/api/v1/company/getCompanyProduct', { company_id: data.company_id })

export default {
  getCompanyList,
  getCompanyDetail,
  saveCompany,
  deleteCompany,
  getCompanyAccount,
  getCompanyProduct
}
