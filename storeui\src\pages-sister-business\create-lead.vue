<template>
  <view class="create-lead-page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 顶部导航 -->
    <view class="header-section">
      <view class="header-background"></view>
      <view class="header-content">
        <view class="nav-bar">
          <view class="nav-left" @click="handleBack">
            <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
          </view>
          <view class="nav-center">
            <text class="nav-title">新建线索</text>
          </view>
          <view class="nav-right"></view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-section">
      <!-- 服务类型选择 -->
      <view class="service-type-section">
        <view class="section-title">
          <text class="title-text">选择服务类型</text>
          <text class="title-desc">请选择您需要的服务类型</text>
        </view>
        
        <!-- 服务类型网格 -->
        <view class="service-grid">
          <view
            v-for="(serviceType, index) in serviceTypeList"
            :key="index"
            class="service-card"
            :class="{ 'selected': selectedServiceType && selectedServiceType.dictValue === serviceType.dictValue }"
            @click="selectServiceType(serviceType)"
          >
            <text class="service-name">{{ serviceType.dictLabel }}</text>
          </view>
        </view>
      </view>

      <!-- 基本信息填写 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">基本信息</text>
          <text class="title-desc">请填写客户基本信息</text>
        </view>

        <!-- 姓名输入 -->
        <view class="form-item">
          <view class="form-label">
            <text class="label-text">客户姓名</text>
            <text class="required">*</text>
          </view>
          <input 
            class="form-input" 
            v-model="formData.customerName" 
            placeholder="请输入客户姓名"
            maxlength="20"
          />
        </view>

        <!-- 手机号输入 -->
        <view class="form-item">
          <view class="form-label">
            <text class="label-text">手机号码</text>
            <text class="required">*</text>
          </view>
          <input 
            class="form-input" 
            v-model="formData.mobile" 
            placeholder="请输入手机号码"
            type="number"
            maxlength="11"
          />
        </view>

        <!-- 地址选择 -->
        <view class="form-item">
          <view class="form-label">
            <text class="label-text">服务地址</text>
            <text class="required">*</text>
          </view>
          <view class="address-selector" @click="selectAddress">
            <text class="address-text" :class="{ 'placeholder': !selectedLocation.address }">
              {{ selectedLocation.address || '请选择服务地址' }}
            </text>
            <u-icon name="map" color="#999" size="16"></u-icon>
          </view>
        </view>


      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-action">
      <button class="create-btn" @click="handleCreateLead" :disabled="!canSubmit">
        立即新建
      </button>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import { getDictData, createLead } from '@/api/sister-business.js';
import { getGPS, chooseLocation, checkLocationPermission } from '@/utlis/common.js';
import { reverseGeocodeLocation } from '@/api/common.js';

export default {
  data() {
    return {
      // 服务类型列表
      serviceTypeList: [],
      selectedServiceType: null,
      
      // 表单数据
      formData: {
        customerName: '',
        mobile: ''
      },
      
      // 地址信息
      selectedLocation: {
        address: '',
        latitude: '',
        longitude: '',
        city: ''
      },
      
      // 页面状态
      loading: false
    };
  },
  
  computed: {
    ...mapState(['StatusBar']),
    
    // 是否可以提交
    canSubmit() {
      return this.selectedServiceType && 
             this.formData.customerName.trim() && 
             this.formData.mobile.trim() && 
             this.selectedLocation.address;
    }
  },
  
  mounted() {
    this.loadServiceTypes();
  },
  
  methods: {
    // 返回上一页
    handleBack() {
      uni.navigateBack();
    },
    
    // 加载服务类型
    async loadServiceTypes() {
      try {
        console.log('开始获取服务类型，dict_type: aunt_type');
        const response = await getDictData('aunt_type');
        console.log('服务类型API响应:', response);
        
        let dataArray = [];
        if (Array.isArray(response)) {
          dataArray = response;
        } else if (response && response.data && Array.isArray(response.data)) {
          dataArray = response.data;
        }
        
        if (dataArray.length > 0) {
          this.serviceTypeList = dataArray;
          console.log('处理后的服务类型列表:', this.serviceTypeList);
        } else {
          console.warn('服务类型数据为空');
          uni.showToast({
            title: '暂无服务类型数据',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取服务类型失败:', error);
        uni.showToast({
          title: '获取服务类型失败',
          icon: 'none'
        });
      }
    },
    
    // 选择服务类型
    selectServiceType(serviceType) {
      this.selectedServiceType = serviceType;
      console.log('选择的服务类型:', serviceType);
    },

    // 通过经纬度获取城市信息
    async getCityFromLocation(latitude, longitude) {
      try {
        console.log('获取城市信息，位置坐标:', `${latitude},${longitude}`);

        // 调用后端逆地理编码接口
        const response = await reverseGeocodeLocation(latitude, longitude);

        console.log('后端逆地理编码API响应:', response);

        // 检查响应状态
        if (response && response.result && response.result.address_components) {
          const city = response.result.address_components.city;
          console.log('获取到的城市信息:', city);

          // 保存城市信息到数据中
          this.selectedLocation.city = city;

          return city;
        } else {
          console.warn('获取城市信息失败:', response?.message || '未知错误');
          console.log('完整响应数据:', response);
          return null;
        }

      } catch (error) {
        console.error('获取城市信息失败:', error);
        return null;
      }
    },

    // 选择地址
    async selectAddress() {
      try {
        // 检查位置权限
        const hasPermission = await checkLocationPermission();
        if (!hasPermission) {
          uni.showToast({
            title: '需要位置权限',
            icon: 'none'
          });
          return;
        }
        
        // 获取当前位置
        let latitude = 24.485261; // 厦门默认坐标
        let longitude = 118.179266;
        
        try {
          const gpsInfo = await getGPS();
          if (gpsInfo.latitude && gpsInfo.longitude) {
            latitude = gpsInfo.latitude;
            longitude = gpsInfo.longitude;
          }
        } catch (gpsError) {
          console.log('获取GPS失败，使用默认坐标:', gpsError);
        }
        
        // 打开地图选择位置
        const location = await chooseLocation({
          latitude,
          longitude
        });
        
        console.log('选择的位置信息:', location);

        // 更新选中的位置信息
        this.selectedLocation = {
          address: location.address,
          latitude: location.latitude,
          longitude: location.longitude,
          city: ''
        };

        // 获取城市信息
        await this.getCityFromLocation(location.latitude, location.longitude);

        uni.showToast({
          title: '地址选择成功',
          icon: 'success'
        });
        
      } catch (error) {
        console.error('地址选择失败:', error);
        if (error.message !== '用户取消了选择') {
          uni.showToast({
            title: '地址选择失败',
            icon: 'none'
          });
        }
      }
    },
    
    // 创建线索
    handleCreateLead() {
      // 验证表单
      if (!this.validateForm()) {
        return;
      }
      
      // 显示确认对话框
      uni.showModal({
        title: '确认创建',
        content: `确认创建${this.selectedServiceType.dictLabel}服务线索吗？`,
        success: (res) => {
          if (res.confirm) {
            this.submitLead();
          }
        }
      });
    },
    
    // 验证表单
    validateForm() {
      if (!this.selectedServiceType) {
        uni.showToast({
          title: '请选择服务类型',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.customerName.trim()) {
        uni.showToast({
          title: '请输入客户姓名',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.mobile.trim()) {
        uni.showToast({
          title: '请输入手机号码',
          icon: 'none'
        });
        return false;
      }
      
      // 验证手机号格式
      const mobileReg = /^1[3-9]\d{9}$/;
      if (!mobileReg.test(this.formData.mobile)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.selectedLocation.address) {
        uni.showToast({
          title: '请选择服务地址',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 提交线索
    async submitLead() {
      this.loading = true;
      
      try {
        // 构建提交数据
        const submitData = {
          service_type: this.selectedServiceType.dictValue,
          service_type_name: this.selectedServiceType.dictLabel,
          customer_name: this.formData.customerName.trim(),
          mobile: this.formData.mobile.trim(),
          address: this.selectedLocation.address,
          latitude: this.selectedLocation.latitude,
          longitude: this.selectedLocation.longitude,
          city: this.selectedLocation.city
        };
        
        console.log('提交的线索数据:', submitData);

        // 调用创建线索API
        const result = await createLead(submitData);

        console.log('创建线索API响应:', result);

        uni.showToast({
          title: '线索创建成功',
          icon: 'success'
        });

        // 返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
        
      } catch (error) {
        console.error('创建线索失败:', error);
        uni.showToast({
          title: '创建失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.create-lead-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

// 顶部导航区域
.header-section {
  position: relative;
  padding-top: calc(var(--status-bar-height) + 20rpx);
  
  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
  }
  
  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 30rpx;
  }
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  
  .nav-left {
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  
  .nav-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .nav-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #fff;
    }
  }
  
  .nav-right {
    width: 80rpx;
  }
}

// 主要内容区域
.content-section {
  flex: 1;
  padding: 40rpx 30rpx 120rpx;
}

.section-title {
  margin-bottom: 30rpx;
  
  .title-text {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
  }
  
  .title-desc {
    display: block;
    font-size: 26rpx;
    color: #666;
  }
}

// 服务类型选择区域
.service-type-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.service-card {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  min-height: 88rpx;

  &.selected {
    background: #fff5e6;
    border-color: #fdd118;
    box-shadow: 0 4rpx 16rpx rgba(253, 209, 24, 0.2);
  }

  .service-name {
    font-size: 28rpx;
    color: #333;
    text-align: center;
    font-weight: 500;
  }
}

// 表单区域
.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.form-item {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  
  .label-text {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }
  
  .required {
    color: #ff4757;
    margin-left: 8rpx;
    font-size: 28rpx;
  }
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  font-size: 28rpx;
  color: #333;
  
  &:focus {
    border-color: #fdd118;
    background: #fff;
  }
}



.address-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  
  .address-text {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    
    &.placeholder {
      color: #999;
    }
  }
}

// 底部按钮
.bottom-action {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.create-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  border-radius: 44rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:disabled {
    background: #e5e5e5;
    color: #999;
  }
  
  &:not(:disabled):active {
    transform: scale(0.98);
  }
}
</style>
