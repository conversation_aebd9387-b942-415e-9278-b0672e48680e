/**
 * 需求广场模块接口
 */
import { post, get } from '../utlis/require.js';

/**
 * 获取需求列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页数量
 * @param {number} params.business_type - 业务类型：1-到家，2-到店
 * @param {number} params.demand_status - 线索状态：1-待抢单，2-已抢单
 * @returns {Promise} - 返回需求列表
 */
export const getDemandList = (params) => {
  return get('/api/v1/demand-square/list', params).catch(error => {
    console.error('Get Demand List Error:', error);
    throw error;
  });
};

/**
 * 获取需求统计信息
 * @param {Object} params - 查询参数
 * @param {number} params.business_type - 业务类型：1-到家，2-到店
 * @returns {Promise} - 返回统计信息
 */
export const getDemandStats = (params = {}) => {
  return get('/api/v1/demand-square/stats', params).catch(error => {
    console.error('Get Demand Stats Error:', error);
    throw error;
  });
};

/**
 * 抢单
 * @param {Object} data - 抢单数据
 * @param {string} data.demand_uuid - 需求UUID
 * @param {string} data.store_uuid - 门店UUID
 * @returns {Promise} - 返回抢单结果
 */
export const grabDemand = (data) => {
  // 手动将对象转换为JSON字符串
  const jsonData = JSON.stringify(data);
  console.log('抢单请求JSON数据:', jsonData);

  return post('/api/v1/demand-square/grab', jsonData, {
    contentType: 'application/json'
  }).catch(error => {
    console.error('Grab Demand Error:', error);
    throw error;
  });
};

/**
 * 共享订单到家政广场
 * @param {Object} data - 共享订单数据
 * @param {number} data.order_id - 订单ID
 * @param {string} data.order_number - 订单号
 * @param {number} data.commission_amount - 佣金金额
 * @param {number} data.expire_hours - 过期小时数
 * @returns {Promise} - 返回共享结果
 */
export const shareOrderToDemandSquare = (data) => {
  return post('/api/v1/demand-square/share-order', data).catch(error => {
    console.error('Share Order To Demand Square Error:', error);
    throw error;
  });
};

/**
 * 获取门店可用员工列表
 * @param {Object} params - 查询参数
 * @param {string} params.store_uuid - 门店UUID
 * @returns {Promise} - 返回员工列表
 */
export const getAvailableStaffList = (params) => {
  return get('/api/v1/demand-square/available-staff', params).catch(error => {
    console.error('Get Available Staff List Error:', error);
    throw error;
  });
};

/**
 * 选择服务员工
 * @param {Object} data - 员工选择数据
 * @param {string} data.demand_uuid - 需求UUID
 * @param {number} data.selected_staff_id - 选中的员工ID
 * @returns {Promise} - 返回选择结果
 */
export const selectServiceStaff = (data) => {
  return post('/api/v1/demand-square/select-staff', data).catch(error => {
    console.error('Select Service Staff Error:', error);
    throw error;
  });
};

/**
 * 取消抢单
 * @param {Object} data - 取消抢单数据
 * @param {string} data.demand_uuid - 需求UUID
 * @returns {Promise} - 返回取消结果
 */
export const cancelDemandGrab = (data) => {
  return post('/api/v1/demand-square/cancel-grab', data).catch(error => {
    console.error('Cancel Demand Grab Error:', error);
    throw error;
  });
};

/**
 * 调整共享订单佣金
 * @param {Object} data - 调整佣金数据
 * @param {number} data.order_id - 订单ID
 * @param {number} data.new_commission_amount - 新佣金金额
 * @returns {Promise} - 返回调整结果
 */
export const adjustSharedOrderCommission = (data) => {
  return post('/api/v1/demand-square/adjust-commission', data).catch(error => {
    console.error('Adjust Shared Order Commission Error:', error);
    throw error;
  });
};

/**
 * 检查订单共享状态
 * @param {number} orderId - 订单ID
 * @returns {Promise} - 返回共享状态信息
 */
export const checkOrderSharedStatus = (orderId) => {
  return get(`/api/v1/demand-square/check-order-shared/${orderId}`).catch(error => {
    console.error('Check Order Shared Status Error:', error);
    throw error;
  });
};

/**
 * 取消共享订单
 * @param {Object} data - 取消共享数据
 * @param {number} data.order_id - 订单ID
 * @returns {Promise} - 返回操作结果
 */
export const expireSharedOrder = (data) => {
  return post('/api/v1/demand-square/expire-shared-order', data).catch(error => {
    console.error('Expire Shared Order Error:', error);
    throw error;
  });
};

/**
 * 共享到店线索到家政广场
 * @param {Object} data - 共享线索数据
 * @param {string} data.customer_uuid - 线索UUID
 * @param {number} data.commission_amount - 佣金金额
 * @param {number} data.expire_hours - 过期小时数
 * @returns {Promise} - 返回共享结果
 */
export const shareStoreLeadToDemandSquare = (data) => {
  return post('/api/v1/demand-square/share-store-lead', data).catch(error => {
    console.error('Share Store Lead To Demand Square Error:', error);
    throw error;
  });
};

/**
 * 购买到店线索
 * @param {Object} data - 购买线索数据
 * @param {string} data.demand_uuid - 需求UUID
 * @returns {Promise} - 返回购买结果
 */
export const purchaseStoreLead = (data) => {
  return post('/api/v1/demand-square/purchase-lead', data).catch(error => {
    console.error('Purchase Store Lead Error:', error);
    throw error;
  });
};

/**
 * 查询用户余额
 * @returns {Promise} - 返回余额信息
 */
export const getUserBalance = () => {
  return get('/api/v1/demand-square/balance').catch(error => {
    console.error('Get User Balance Error:', error);
    throw error;
  });
};

/**
 * 余额充值
 * @param {Object} data - 充值数据
 * @param {number} data.amount - 充值金额
 * @returns {Promise} - 返回充值结果
 */
export const rechargeBalance = (data) => {
  return post('/api/v1/demand-square/recharge', data).catch(error => {
    console.error('Recharge Balance Error:', error);
    throw error;
  });
};

// 导出所有接口函数
export default {
  getDemandList,
  getDemandStats,
  grabDemand,
  shareOrderToDemandSquare,
  getAvailableStaffList,
  selectServiceStaff,
  cancelDemandGrab,
  checkOrderSharedStatus,
  expireSharedOrder,
  shareStoreLeadToDemandSquare,
  purchaseStoreLead,
  getUserBalance,
  rechargeBalance
};
