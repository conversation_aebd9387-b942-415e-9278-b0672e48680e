"""
公司提现申请数据模型
"""
from datetime import datetime
from sqlalchemy import Column, BigInteger, String, Integer, DECIMAL, DateTime, Text
from config.database import Base


class CompanyWithdrawal(Base):
    """公司提现申请表"""
    
    __tablename__ = 'company_withdrawal'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 基本信息
    company_uuid = Column(String(64), nullable=False, comment='公司UUID')
    withdrawal_no = Column(String(64), nullable=False, unique=True, comment='提现申请单号')
    withdrawal_type = Column(Integer, nullable=False, comment='提现类型：1-自行开票，2-零工提现')
    withdrawal_type_name = Column(String(50), comment='提现类型名称')
    
    # 金额信息
    apply_amount = Column(DECIMAL(15, 2), nullable=False, comment='申请提现金额')
    fee_rate = Column(DECIMAL(5, 4), comment='手续费率')
    fee_amount = Column(DECIMAL(15, 2), comment='手续费金额')
    actual_amount = Column(DECIMAL(15, 2), nullable=False, comment='实际到账金额')
    
    # 银行信息
    bank_name = Column(String(100), comment='银行名称')
    bank_account = Column(String(50), comment='银行账号')
    account_holder = Column(String(100), comment='开户人姓名')
    
    # 申请信息
    invoice_info = Column(Text, comment='开票图片URL')
    apply_reason = Column(String(500), comment='申请原因')
    status = Column(String(20), nullable=False, comment='状态：PENDING-待审核，APPROVED-已审核，PROCESSING-处理中，COMPLETED-已完成，REJECTED-已拒绝')
    
    # 申请人信息
    applicant_id = Column(String(64), nullable=False, comment='申请人ID')
    applicant_name = Column(String(100), nullable=False, comment='申请人姓名')
    apply_time = Column(DateTime, nullable=False, comment='申请时间')
    
    # 审核信息
    reviewer_id = Column(String(64), comment='审核人ID')
    reviewer_name = Column(String(100), comment='审核人姓名')
    review_time = Column(DateTime, comment='审核时间')
    review_comment = Column(String(500), comment='审核意见')
    
    # 处理信息
    processor_id = Column(String(64), comment='处理人ID')
    processor_name = Column(String(100), comment='处理人姓名')
    process_time = Column(DateTime, comment='处理时间')
    completion_time = Column(DateTime, comment='完成时间')
    
    # 交易信息
    transaction_id = Column(String(100), comment='银行流水号')
    remark = Column(Text, comment='备注')
    
    # 系统字段
    created_by = Column(String(64), nullable=False, comment='创建人')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_by = Column(String(64), comment='更新人')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    def __repr__(self):
        return f"<CompanyWithdrawal(id={self.id}, withdrawal_no='{self.withdrawal_no}', status='{self.status}')>"
