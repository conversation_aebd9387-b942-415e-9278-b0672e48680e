"""
提现账户数据模型
用于存储提现账户信息（个人/对公）
"""
from datetime import datetime
from sqlalchemy import Column, BigInteger, String, Integer, DateTime, Text, Boolean
from config.database import Base


class WithdrawalAccount(Base):
    """提现账户表"""
    
    __tablename__ = 'withdrawal_account'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联信息
    company_uuid = Column(String(64), nullable=False, comment='公司UUID')
    user_id = Column(String(64), nullable=False, comment='创建用户ID')
    
    # 账户基本信息
    account_name = Column(String(100), nullable=False, comment='账户名称')
    account_type = Column(Integer, nullable=False, comment='账户类型：1-企业对公，2-个人')
    
    # 银行信息
    bank_name = Column(String(100), nullable=False, comment='银行名称')
    bank_branch = Column(String(100), comment='银行分行')
    bank_account = Column(String(50), nullable=False, comment='银行账号')
    account_holder = Column(String(100), nullable=False, comment='开户人姓名')
    
    # 附加信息
    remark = Column(Text, comment='备注')
    
    # 状态信息
    is_default = Column(Boolean, nullable=False, default=False, comment='是否默认账户')
    status = Column(String(20), nullable=False, default='active', comment='状态：active-正常，inactive-停用')
    
    # 系统字段
    created_by = Column(String(64), nullable=False, comment='创建人')
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_by = Column(String(64), comment='更新人')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    is_delete = Column(String(2), nullable=False, default='0', comment='是否删除(1:已删除,0:未删除)')

    def __repr__(self):
        return f"<WithdrawalAccount(id={self.id}, account_name='{self.account_name}', account_type={self.account_type})>"
