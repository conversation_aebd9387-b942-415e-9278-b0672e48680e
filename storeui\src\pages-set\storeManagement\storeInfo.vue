<template>
  <view class="store-info-page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <u-icon name="arrow-left" color="#333" size="20"></u-icon>
        </view>
        <view class="navbar-title">门店详情</view>
        <view class="navbar-right"></view>
      </view>
    </view>



    <!-- 门店详情内容 -->
    <view class="detail-content" v-if="!loading && !error">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title">
          <view class="title-left">
            <u-icon name="info-circle" color="#fdd118" size="18"></u-icon>
            <text class="title-text">基本信息</text>
          </view>
          <view v-if="!isEditing" class="edit-btn" @click="startEdit">
            <u-icon name="edit-pen" color="#666" size="16"></u-icon>
            <text class="edit-text">编辑</text>
          </view>
        </view>
        <view class="info-list">
          <!-- 负责人 -->
          <view class="info-item" v-if="storeDetail.manager || isEditing">
            <text class="label">负责人</text>
            <view v-if="!isEditing" class="value">{{ storeDetail.manager }}</view>
            <input v-else class="edit-input" v-model="editForm.manager" placeholder="请输入负责人姓名" />
          </view>

          <!-- 联系电话 -->
          <view class="info-item" v-if="storeDetail.phone || isEditing">
            <text class="label">联系电话</text>
            <view v-if="!isEditing" class="value-with-action">
              <text class="value">{{ storeDetail.phone }}</text>
              <view class="action-btn" @click="callStore">
                <u-icon name="phone" color="#07c160" size="16"></u-icon>
              </view>
            </view>
            <input v-else class="edit-input" v-model="editForm.phone" placeholder="请输入联系电话" />
          </view>

          <!-- 门店电话 -->
          <view class="info-item" v-if="storeDetail.mobile || isEditing">
            <text class="label">门店电话</text>
            <view v-if="!isEditing" class="value-with-action">
              <text class="value">{{ storeDetail.mobile }}</text>
              <view class="action-btn" @click="callStoreMobile">
                <u-icon name="phone" color="#07c160" size="16"></u-icon>
              </view>
            </view>
            <input v-else class="edit-input" v-model="editForm.mobile" placeholder="请输入门店电话" />
          </view>

          <!-- 邮箱地址 -->
          <view class="info-item" v-if="storeDetail.email || isEditing">
            <text class="label">邮箱地址</text>
            <view v-if="!isEditing" class="value">{{ storeDetail.email }}</view>
            <input v-else class="edit-input" v-model="editForm.email" placeholder="请输入邮箱地址" />
          </view>

          <!-- 门店地址 -->
          <view class="info-item" v-if="storeDetail.address || isEditing">
            <text class="label">门店地址</text>
            <view v-if="!isEditing" class="value">{{ storeDetail.address }}</view>
            <input v-else class="edit-input" v-model="editForm.address" placeholder="请输入门店地址" />
          </view>

          <!-- 营业时间 -->
          <view class="info-item" v-if="storeDetail.business_hours || isEditing">
            <text class="label">营业时间</text>
            <view v-if="!isEditing" class="value">{{ storeDetail.business_hours }}</view>
            <view v-else class="edit-select" @click="showBusinessHoursPicker">
              <text class="select-text" :class="{ 'placeholder': !editForm.business_hours }">
                {{ editForm.business_hours || '请选择营业时间' }}
              </text>
              <u-icon name="arrow-down" color="#999" size="14"></u-icon>
            </view>
          </view>

          <!-- 简介 -->
          <view class="info-item" v-if="storeDetail.introduce || isEditing">
            <text class="label">简介</text>
            <view v-if="!isEditing" class="value">{{ storeDetail.introduce }}</view>
            <textarea v-else class="edit-textarea" v-model="editForm.introduce" placeholder="请输入门店简介" />
          </view>

          <!-- 备注 -->
          <view class="info-item" v-if="storeDetail.remark || isEditing">
            <text class="label">备注</text>
            <view v-if="!isEditing" class="value">{{ storeDetail.remark }}</view>
            <textarea v-else class="edit-textarea" v-model="editForm.remark" placeholder="请输入备注信息" />
          </view>
        </view>
      </view>

      <!-- 门店状态 -->
      <view class="info-section">
        <view class="section-title">
          <u-icon name="setting" color="#fdd118" size="18"></u-icon>
          <text class="title-text">门店状态</text>
        </view>
        <view class="status-grid">
          <view class="status-item">
            <view class="status-icon" :class="storeDetail.status === 1 ? 'success' : 'warning'">
              <u-icon :name="storeDetail.status === 1 ? 'checkmark' : 'close'" color="#fff" size="16"></u-icon>
            </view>
            <text class="status-label">营业状态</text>
            <text class="status-value" :class="storeDetail.status === 1 ? 'success' : 'warning'">
              {{ storeDetail.status === 1 ? '营业中' : '已关闭' }}
            </text>
          </view>
          <view class="status-item" v-if="storeDetail.level">
            <view class="status-icon level">
              <u-icon name="star" color="#fff" size="16"></u-icon>
            </view>
            <text class="status-label">门店等级</text>
            <text class="status-value">{{ storeDetail.level }}</text>
          </view>
          <view class="status-item" v-if="storeDetail.is_xyj !== null">
            <view class="status-icon" :class="storeDetail.is_xyj ? 'info' : 'default'">
              <u-icon name="home" color="#fff" size="16"></u-icon>
            </view>
            <text class="status-label">小羽佳关联</text>
            <text class="status-value">{{ storeDetail.is_xyj ? '是' : '否' }}</text>
          </view>
        </view>
      </view>

      <!-- 运营信息 -->
      <view class="info-section" v-if="hasOperationInfo">
        <view class="section-title">
          <u-icon name="calendar" color="#fdd118" size="18"></u-icon>
          <text class="title-text">运营信息</text>
        </view>
        <view class="info-list">
          <view class="info-item" v-if="storeDetail.dredge_date">
            <text class="label">开通日期</text>
            <text class="value">{{ formatDate(storeDetail.dredge_date) }}</text>
          </view>
          <view class="info-item" v-if="storeDetail.expiry_date">
            <text class="label">到期日期</text>
            <text class="value">{{ formatDate(storeDetail.expiry_date) }}</text>
          </view>
          <view class="info-item" v-if="storeDetail.user_count">
            <text class="label">用户数量</text>
            <text class="value">{{ storeDetail.user_count }}人</text>
          </view>
          <view class="info-item" v-if="storeDetail.insurance_company">
            <text class="label">保险公司</text>
            <text class="value">{{ storeDetail.insurance_company }}</text>
          </view>
        </view>
      </view>

      <!-- 系统信息 -->
      <view class="info-section">
        <view class="section-title">
          <u-icon name="clock" color="#fdd118" size="18"></u-icon>
          <text class="title-text">系统信息</text>
        </view>
        <view class="info-list">
          <view class="info-item">
            <text class="label">创建时间</text>
            <text class="value">{{ formatDateTime(storeDetail.create_time) }}</text>
          </view>
          <view class="info-item">
            <text class="label">更新时间</text>
            <text class="value">{{ formatDateTime(storeDetail.update_time) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <u-loading-icon mode="spinner"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view class="error-container" v-if="error">
      <u-empty text="加载失败" mode="error">
        <template #button>
          <u-button type="primary" size="small" @click="loadStoreDetail">重新加载</u-button>
        </template>
      </u-empty>
    </view>

    <!-- 编辑模式操作按钮 -->
    <view v-if="isEditing" class="edit-actions">
      <view class="action-buttons">
        <u-button
          type="default"
          size="large"
          :loading="false"
          @click="cancelEdit"
          class="cancel-btn"
        >
          取消
        </u-button>
        <u-button
          type="primary"
          size="large"
          :loading="saving"
          @click="saveEdit"
          class="save-btn"
        >
          {{ saving ? '保存中...' : '保存' }}
        </u-button>
      </view>
    </view>

    <!-- 营业时间选择器 -->
    <u-picker
      ref="businessHoursPicker"
      :show="showPicker"
      :columns="businessHoursOptions"
      @confirm="onBusinessHoursConfirm"
      @cancel="showPicker = false"
      title="选择营业时间"
      :default-index="[8, 18]"
    ></u-picker>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { get } from '@/utlis/require.js'

export default {
  data() {
    return {
      storeUuid: '', // 门店UUID
      storeDetail: {}, // 门店详情
      loading: false,
      error: false,
      isEditing: false, // 是否处于编辑模式
      saving: false, // 是否正在保存
      editForm: { // 编辑表单数据
        manager: '',
        phone: '',
        mobile: '',
        email: '',
        address: '',
        business_hours: '',
        introduce: '',
        remark: ''
      },
      originalData: {}, // 原始数据备份
      showPicker: false, // 是否显示选择器
      businessHoursOptions: [
        // 开始时间
        [
          '00:00', '01:00', '02:00', '03:00', '04:00', '05:00',
          '06:00', '07:00', '08:00', '09:00', '10:00', '11:00',
          '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
          '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
        ],
        // 结束时间
        [
          '00:00', '01:00', '02:00', '03:00', '04:00', '05:00',
          '06:00', '07:00', '08:00', '09:00', '10:00', '11:00',
          '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
          '18:00', '19:00', '20:00', '21:00', '22:00', '23:00', '23:59'
        ]
      ]
    }
  },

  computed: {
    ...mapState(['StatusBar']),
    
    // 是否有运营信息
    hasOperationInfo() {
      return this.storeDetail.dredge_date || 
             this.storeDetail.expiry_date || 
             this.storeDetail.user_count || 
             this.storeDetail.insurance_company
    }
  },

  onLoad(options) {
    if (options.store_uuid) {
      this.storeUuid = options.store_uuid
      this.loadStoreDetail()
    } else {
      uni.showToast({
        title: '门店信息不存在',
        icon: 'none'
      })
      setTimeout(() => {
        this.goBack()
      }, 1500)
    }
  },

  methods: {
    // 加载门店详情
    async loadStoreDetail() {
      if (!this.storeUuid) return
      
      this.loading = true
      this.error = false
      
      try {
        const response = await get('/api/v1/store/detail', {
          store_uuid: this.storeUuid
        })
        
        this.storeDetail = response
        console.log('门店详情:', this.storeDetail)
      } catch (error) {
        console.error('加载门店详情失败:', error)
        this.error = true
        uni.showToast({
          title: '加载门店详情失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 拨打门店手机
    callStoreMobile() {
      if (this.storeDetail.mobile) {
        this.makeCall(this.storeDetail.mobile)
      }
    },

    // 拨打联系电话
    callStore() {
      if (this.storeDetail.phone) {
        this.makeCall(this.storeDetail.phone)
      }
    },

    // 拨打电话
    makeCall(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        fail: (err) => {
          console.error('拨打电话失败:', err)
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          })
        }
      })
    },

    // 开始编辑
    startEdit() {
      this.isEditing = true

      // 备份原始数据
      this.originalData = {
        manager: this.storeDetail.manager || '',
        phone: this.storeDetail.phone || '',
        mobile: this.storeDetail.mobile || '',
        email: this.storeDetail.email || '',
        address: this.storeDetail.address || '',
        business_hours: this.storeDetail.business_hours || '',
        introduce: this.storeDetail.introduce || '',
        remark: this.storeDetail.remark || ''
      }

      // 初始化编辑表单
      this.editForm = { ...this.originalData }
    },

    // 取消编辑
    cancelEdit() {
      this.isEditing = false
      this.editForm = {}
      this.originalData = {}
    },

    // 保存编辑
    async saveEdit() {
      // 验证必填字段（只验证核心必填字段）
      if (!this.editForm.phone || !this.editForm.phone.trim()) {
        uni.showToast({
          title: '请输入联系电话',
          icon: 'none'
        })
        return
      }

      if (!this.editForm.address || !this.editForm.address.trim()) {
        uni.showToast({
          title: '请输入门店地址',
          icon: 'none'
        })
        return
      }

      // 验证邮箱格式（如果填写了邮箱）
      if (this.editForm.email && this.editForm.email.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(this.editForm.email.trim())) {
          uni.showToast({
            title: '请输入正确的邮箱格式',
            icon: 'none'
          })
          return
        }
      }

      // 检查是否有变更
      const hasChanges = Object.keys(this.editForm).some(key => {
        return this.editForm[key].trim() !== this.originalData[key].trim()
      })

      if (!hasChanges) {
        uni.showToast({
          title: '没有修改内容',
          icon: 'none'
        })
        this.cancelEdit()
        return
      }

      this.saving = true

      try {
        // 构建更新数据，只包含修改的字段
        const updateData = {
          store_uuid: this.storeUuid
        }

        Object.keys(this.editForm).forEach(key => {
          if (this.editForm[key].trim() !== this.originalData[key].trim()) {
            updateData[key] = this.editForm[key].trim()
          }
        })

        console.log('更新门店信息:', updateData)

        // 调用更新API，使用JSON格式
        const [error, response] = await uni.request({
          url: require('@/setConfig.js').host + '/api/v1/order/updateStoreInfo',
          method: 'POST',
          header: {
            'content-type': 'application/json',
            'Authorization': this.$store.state.token ? `Bearer ${this.$store.state.token}` : ''
          },
          data: updateData
        })

        console.log('API响应:', response)

        if (error) {
          throw new Error('网络请求失败')
        }

        if (response.statusCode !== 200) {
          throw new Error('网络请求失败')
        }

        // 检查业务状态码
        if (response.data && response.data.code && response.data.code !== 200) {
          throw new Error(response.data.msg || response.data.message || '更新失败')
        }

        // 更新本地数据
        Object.keys(this.editForm).forEach(key => {
          this.storeDetail[key] = this.editForm[key].trim()
        })

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 退出编辑模式
        this.cancelEdit()

      } catch (error) {
        console.error('保存门店信息失败:', error)
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        })
      } finally {
        this.saving = false
      }
    },

    // 显示营业时间选择器
    showBusinessHoursPicker() {
      this.showPicker = true
    },

    // 营业时间选择确认
    onBusinessHoursConfirm(value) {
      const startTime = value.value[0]
      const endTime = value.value[1]

      // 验证时间逻辑
      if (startTime >= endTime) {
        uni.showToast({
          title: '结束时间必须晚于开始时间',
          icon: 'none'
        })
        return
      }

      this.editForm.business_hours = `${startTime}-${endTime}`
      this.showPicker = false
    },

    // 返回上一页
    goBack() {
      uni.navigateBack({
        fail: () => {
          uni.navigateTo({
            url: '/pages-set/storeManagement/storeList'
          })
        }
      })
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      
      return `${year}-${month}-${day}`
    },

    // 格式化日期时间
    formatDateTime(dateStr) {
      if (!dateStr) return ''
      
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.store-info-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #f5f5f5 100%);
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, $xyj-theme 0%, $xyj-decorate2 100%);
  padding-top: calc(var(--status-bar-height) + 20px);
  position: relative;
  z-index: 10;
  box-shadow: 0 4px 20px rgba(253, 209, 24, 0.15);

  .navbar-content {
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;

    .navbar-left {
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 22px;
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.25);
        transform: scale(0.95);
      }
    }

    .navbar-title {
      font-size: 18px;
      font-weight: 700;
      color: #fff;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .navbar-right {
      width: 44px;
      height: 44px;
    }
  }
}



/* 优化详情内容 */
.detail-content {
  padding: 20px 20px 30px;
}

/* 信息区块 */
.info-section {
  background: #fff;
  border-radius: 16px;
  margin-bottom: 15px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;

  .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 20px 15px;
    border-bottom: 1px solid #f0f0f0;
    background: rgba(253, 209, 24, 0.05);

    .title-left {
      display: flex;
      align-items: center;

      .title-text {
        font-size: 16px;
        font-weight: 600;
        color: $text-title;
        margin-left: 8px;
      }
    }

    .edit-btn {
      display: flex;
      align-items: center;
      padding: 6px 12px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 16px;
      border: 1px solid #e9ecef;
      transition: all 0.2s ease;

      &:active {
        background: rgba(253, 209, 24, 0.1);
        border-color: $xyj-theme;
      }

      .edit-text {
        font-size: 12px;
        color: #666;
        margin-left: 4px;
        font-weight: 500;
      }
    }
  }
}

/* 信息列表 */
.info-list {
  padding: 0 20px 20px;

  .info-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-size: 14px;
      color: $text-main;
      width: 80px;
      flex-shrink: 0;
      font-weight: 500;
    }

    .value {
      font-size: 14px;
      color: $text-title;
      flex: 1;
      line-height: 1.4;
      font-weight: 500;
    }

    .edit-input {
      flex: 1;
      font-size: 14px;
      color: $text-title;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 12px 16px;
      line-height: 1.5;
      font-weight: 500;
      transition: all 0.2s ease;
      min-height: 44px;
      box-sizing: border-box;

      &:focus {
        background: #fff;
        border-color: $xyj-theme;
        box-shadow: 0 0 0 2px rgba(253, 209, 24, 0.1);
        outline: none;
      }

      &::placeholder {
        color: #999;
        font-weight: 400;
      }
    }

    .edit-textarea {
      flex: 1;
      font-size: 14px;
      color: $text-title;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 12px 16px;
      line-height: 1.5;
      font-weight: 500;
      transition: all 0.2s ease;
      min-height: 80px;
      max-height: 120px;
      box-sizing: border-box;
      resize: none;

      &:focus {
        background: #fff;
        border-color: $xyj-theme;
        box-shadow: 0 0 0 2px rgba(253, 209, 24, 0.1);
        outline: none;
      }

      &::placeholder {
        color: #999;
        font-weight: 400;
      }
    }

    .edit-select {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      color: $text-title;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 12px 16px;
      line-height: 1.5;
      font-weight: 500;
      transition: all 0.2s ease;
      min-height: 44px;
      box-sizing: border-box;

      &:active {
        background: #fff;
        border-color: $xyj-theme;
        box-shadow: 0 0 0 2px rgba(253, 209, 24, 0.1);
      }

      .select-text {
        flex: 1;

        &.placeholder {
          color: #999;
          font-weight: 400;
        }
      }
    }

    .value-with-action {
      display: flex;
      align-items: center;
      flex: 1;

      .value {
        flex: 1;
      }

      .action-btn {
        width: 28px;
        height: 28px;
        background: rgba(9, 190, 137, 0.1);
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 8px;
        border: 1px solid rgba(9, 190, 137, 0.2);
        transition: background-color 0.2s ease;

        &:active {
          background: $xyj-decorate3;
        }
      }
    }
  }
}

/* 状态网格 */
.status-grid {
  display: flex;
  padding: 20px;
  gap: 15px;

  .status-item {
    flex: 1;
    text-align: center;

    .status-icon {
      width: 40px;
      height: 40px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 8px;

      &.success {
        background: $xyj-decorate3;
      }

      &.warning {
        background: $xyj-decorate2;
      }

      &.info {
        background: $xyj-theme;
      }

      &.level {
        background: $xyj-theme;
      }

      &.default {
        background: $text-main;
      }
    }

    .status-label {
      font-size: 12px;
      color: $text-main;
      margin-bottom: 4px;
      display: block;
      font-weight: 500;
    }

    .status-value {
      font-size: 14px;
      font-weight: 600;
      color: $text-title;

      &.success {
        color: $xyj-decorate3;
      }

      &.warning {
        color: $xyj-decorate2;
      }
    }
  }
}

/* 优化加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: #fff;
  margin: 15px 20px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);

  .loading-text {
    margin-top: 20px;
    font-size: 14px;
    color: $text-main;
    font-weight: 500;
  }
}

/* 编辑操作按钮 */
.edit-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;

  .action-buttons {
    display: flex;
    gap: 15px;

    .cancel-btn,
    .save-btn {
      flex: 1;
      height: 48px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
    }

    .cancel-btn {
      background: #f8f9fa;
      color: $text-main;
      border: 1px solid #e9ecef;

      &:active {
        background: #e9ecef;
      }
    }

    .save-btn {
      background: linear-gradient(135deg, $xyj-theme 0%, $xyj-decorate2 100%);
      color: #fff;
      border: none;

      &:active {
        opacity: 0.8;
      }

      &:disabled {
        opacity: 0.6;
      }
    }
  }
}
</style>
