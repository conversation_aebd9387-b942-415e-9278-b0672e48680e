/**
 * AI图片生成相关API
 */
import { post } from '../utlis/require.js'

/**
 * 生成产品主图
 * @param {Object} data - 请求数据
 * @param {string} data.service_name - 服务名称/产品名称
 * @returns {Promise} - 返回生成结果
 */
export const generateProductImage = (data) => {
  console.log('发起AI图片生成请求:', data);

  return post('/api/v1/ai-image/generate-product-image', data, {
    contentType: 'application/json',
    timeout: 35000  // 35秒超时，比后端的30秒稍长一点
  }).then(res => {
    console.log('生成产品主图响应:', res);

    // 验证响应格式
    if (!res || typeof res !== 'object') {
      throw new Error('服务器响应格式异常');
    }

    return res;
  }).catch(error => {
    console.error('生成产品主图失败:', error);

    // 统一错误处理
    if (error.message && error.message.includes('timeout')) {
      throw new Error('AI图片生成超时，请稍后重试');
    } else if (error.message && error.message.includes('network')) {
      throw new Error('网络连接失败，请检查网络');
    } else {
      throw error;
    }
  });
};

/**
 * AI图片生成服务健康检查
 * @returns {Promise} - 返回服务状态
 */
export const checkAIImageHealth = () => {
  return post('/api/v1/ai-image/health', {}, {
    contentType: 'application/json'
  }).then(res => {
    console.log('AI图片生成服务健康检查响应:', res);
    return res;
  }).catch(error => {
    console.error('AI图片生成服务健康检查失败:', error);
    throw error;
  });
};
