"""
易宝支付入网控制器
"""
import logging
from typing import Dict, Any
from fastapi import APIRouter, Depends, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from utils.response_util import ResponseUtil
from exceptions.exception import ValidationException, BusinessException
from module_admin.service.yeepay_innet_service import YeepayInnetService
from module_admin.entity.vo.yeepay_innet_vo import (
    EnterpriseInnetRequestVO,
    MicroInnetRequestVO,
    InnetQueryRequestVO,
    FileUploadRequestVO
)

logger = logging.getLogger(__name__)

# 创建路由器
yeepay_innet_controller = APIRouter(prefix="/api/v1/yeepay/innet", tags=["易宝支付入网"])


@yeepay_innet_controller.post('/enterprise', summary="企业/个体户入网")
async def enterprise_innet(
    request_data: EnterpriseInnetRequestVO,
    query_db: AsyncSession = Depends(get_db)
):
    """企业/个体户入网接口
    
    提交企业或个体工商户的入网资料
    
    Args:
        request_data: 入网请求数据
        query_db: 数据库会话
        
    Returns:
        入网结果
    """
    try:
        logger.info(f"=== 企业/个体户入网请求 ===")
        logger.info(f"请求数据: {request_data.dict()}")
        
        # 调用入网服务
        result = await YeepayInnetService.enterprise_innet_service(
            query_db, 
            request_data.dict()
        )
        
        logger.info(f"企业/个体户入网结果: {result}")
        
        # 检查响应码
        rsp_code = result.get("rspCode")
        if rsp_code == "0000":
            return ResponseUtil.success(
                data=result,
                msg="入网申请提交成功"
            )
        else:
            return ResponseUtil.business_error(
                msg=result.get("rspMsg", "入网申请失败"),
                data=result
            )
            
    except ValidationException as e:
        logger.error(f"企业/个体户入网参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"企业/个体户入网业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"企业/个体户入网异常: {str(e)}")
        return ResponseUtil.error(msg=f"入网申请失败: {str(e)}", code=500)


@yeepay_innet_controller.post('/micro', summary="小微/个人入网")
async def micro_innet(
    request_data: MicroInnetRequestVO,
    query_db: AsyncSession = Depends(get_db)
):
    """小微/个人入网接口
    
    提交小微企业或个人的入网资料
    
    Args:
        request_data: 入网请求数据
        query_db: 数据库会话
        
    Returns:
        入网结果
    """
    try:
        logger.info(f"=== 小微/个人入网请求 ===")
        logger.info(f"请求数据: {request_data.dict()}")
        
        # 调用入网服务
        result = await YeepayInnetService.micro_innet_service(
            query_db, 
            request_data.dict()
        )
        
        logger.info(f"小微/个人入网结果: {result}")
        
        # 检查响应码
        rsp_code = result.get("rspCode")
        if rsp_code == "0000":
            return ResponseUtil.success(
                data=result,
                msg="入网申请提交成功"
            )
        else:
            return ResponseUtil.business_error(
                msg=result.get("rspMsg", "入网申请失败"),
                data=result
            )
            
    except ValidationException as e:
        logger.error(f"小微/个人入网参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"小微/个人入网业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"小微/个人入网异常: {str(e)}")
        return ResponseUtil.error(msg=f"入网申请失败: {str(e)}", code=500)


@yeepay_innet_controller.post('/query', summary="入网结果查询")
async def query_innet_result(
    request_data: InnetQueryRequestVO,
    query_db: AsyncSession = Depends(get_db)
):
    """入网结果查询接口

    查询入网申请的处理结果

    Args:
        request_data: 查询请求数据
        query_db: 数据库会话

    Returns:
        查询结果
    """
    try:
        logger.info(f"=== 入网结果查询请求 ===")
        logger.info(f"查询参数: {request_data.dict()}")

        # 调用查询服务
        result = await YeepayInnetService.query_innet_result_service(
            query_db,
            request_data.origTxnDate,
            request_data.origCusTraceNo,
            request_data.origSysTraceNo
        )

        logger.info(f"入网结果查询结果: {result}")

        # 检查响应码
        rsp_code = result.get("rspCode")
        if rsp_code == "0000":
            return ResponseUtil.success(
                data=result,
                msg="查询成功"
            )
        else:
            return ResponseUtil.business_error(
                msg=result.get("rspMsg", "查询失败"),
                data=result
            )
            
    except ValidationException as e:
        logger.error(f"入网结果查询参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"入网结果查询业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"入网结果查询异常: {str(e)}")
        return ResponseUtil.error(msg=f"查询失败: {str(e)}", code=500)


@yeepay_innet_controller.post('/upload', summary="文件上传")
async def upload_file(
    file: UploadFile = File(..., description="上传的文件"),
    file_type: str = Form(..., description="文件类型"),
    query_db: AsyncSession = Depends(get_db)
):
    """文件上传接口
    
    上传入网所需的证件照片等文件
    
    Args:
        file: 上传的文件
        file_type: 文件类型
        query_db: 数据库会话
        
    Returns:
        上传结果，包含文件ID
    """
    try:
        logger.info(f"=== 文件上传请求 ===")
        logger.info(f"文件名: {file.filename}, 文件类型: {file_type}")
        
        # 验证文件
        if not file.filename:
            raise ValidationException(message="文件名不能为空")
        
        # 读取文件内容
        file_content = await file.read()
        if not file_content:
            raise ValidationException(message="文件内容不能为空")
        
        # 验证文件大小（限制为5MB）
        max_size = 5 * 1024 * 1024  # 5MB
        if len(file_content) > max_size:
            raise ValidationException(message="文件大小不能超过5MB")
        
        # 验证文件类型
        allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']
        if file.content_type not in allowed_types:
            raise ValidationException(message=f"不支持的文件类型: {file.content_type}")
        
        # 调用文件上传服务
        result = await YeepayInnetService.upload_file_service(
            file_content,
            file.filename,
            file_type
        )
        
        logger.info(f"文件上传结果: {result}")
        
        # 检查响应码
        rsp_code = result.get("rspCode")
        if rsp_code == "0000":
            return ResponseUtil.success(
                data=result,
                msg="文件上传成功"
            )
        else:
            return ResponseUtil.business_error(
                msg=result.get("rspMsg", "文件上传失败"),
                data=result
            )
            
    except ValidationException as e:
        logger.error(f"文件上传参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"文件上传业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"文件上传异常: {str(e)}")
        return ResponseUtil.error(msg=f"文件上传失败: {str(e)}", code=500)


@yeepay_innet_controller.get('/constants', summary="获取入网常量")
async def get_innet_constants():
    """获取入网相关常量
    
    返回入网过程中需要用到的各种常量值
    
    Returns:
        常量数据
    """
    try:
        from module_admin.entity.vo.yeepay_innet_vo import InnetConstants
        
        constants = {
            "ownerTypes": {
                InnetConstants.OWNER_TYPE_ENTERPRISE: "企业",
                InnetConstants.OWNER_TYPE_INDIVIDUAL: "个体工商户"
            },
            "certTypes": {
                InnetConstants.CERT_TYPE_BUSINESS_LICENSE: "营业执照",
                InnetConstants.CERT_TYPE_ID_CARD: "身份证"
            },
            "accountTypes": {
                InnetConstants.ACCOUNT_TYPE_PERSONAL_DEBIT: "个人借记卡",
                InnetConstants.ACCOUNT_TYPE_CORPORATE_GENERAL: "对公一般户",
                InnetConstants.ACCOUNT_TYPE_ENTERPRISE_SETTLEMENT: "企业单位结算卡"
            },
            "settleCycles": {
                InnetConstants.SETTLE_CYCLE_T0: "T+0",
                InnetConstants.SETTLE_CYCLE_T1: "T+1"
            },
            "innetStatuses": {
                InnetConstants.INNET_STATUS_PENDING: "待审核",
                InnetConstants.INNET_STATUS_APPROVED: "审核通过",
                InnetConstants.INNET_STATUS_REJECTED: "审核拒绝"
            }
        }
        
        return ResponseUtil.success(
            data=constants,
            msg="获取常量成功"
        )
        
    except Exception as e:
        logger.error(f"获取入网常量异常: {str(e)}")
        return ResponseUtil.error(msg=f"获取常量失败: {str(e)}", code=500)
