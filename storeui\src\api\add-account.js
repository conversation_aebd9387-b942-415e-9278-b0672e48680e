import { post, get } from '../utlis/require.js'

/**
 * 加账号相关API
 */

// 获取公司账号信息
export const getCompanyAccountInfo = () => {
  return get('/api/v1/account/company-info')
}

// 获取公司账号列表
export const getCompanyAccountList = (params = {}) => {
  return get('/api/v1/account/list', params)
}

// 创建加账号订单
export const createAddAccountOrder = (data) => {
  return post('/api/v1/account/create-order', data, { contentType: 'application/json' })
}

// 处理加账号支付成功
export const processAddAccountPaymentSuccess = (data) => {
  return post('/api/v1/account/payment-success', data, { contentType: 'application/json' })
}

export default {
  getCompanyAccountInfo,
  getCompanyAccountList,
  createAddAccountOrder,
  processAddAccountPaymentSuccess
}
