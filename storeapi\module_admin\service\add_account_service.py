from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from typing import List, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal

from module_admin.dao.add_account_dao import AddAccountDao
from module_admin.entity.vo.add_account_vo import AddAccountRequest, AddAccountResponse
from exceptions.exception import BusinessException, ValidationException
from utils.log_util import logger
import uuid


class AddAccountService:
    """加账号服务层"""

    @classmethod
    async def get_company_account_info_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str
    ) -> Dict[str, Any]:
        """
        获取公司账号信息服务

        :param query_db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 公司账号信息
        """
        try:
            # 获取账号数量统计
            account_count = await AddAccountDao.get_company_account_count(query_db, company_uuid)
            
            return {
                "total_count": account_count["total_count"],
                "paid_count": account_count["paid_count"],
                "free_count": account_count["free_count"]
            }

        except Exception as e:
            logger.error(f"获取公司账号信息失败: {str(e)}")
            raise BusinessException(message=f"获取公司账号信息失败: {str(e)}")

    @classmethod
    async def create_add_account_order_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        company_name: str,
        store_id: str,
        store_uuid: str,
        store_name: str,
        operator_id: str,
        operator_name: str,
        request_data: AddAccountRequest
    ) -> AddAccountResponse:
        """
        创建加账号订单服务

        :param query_db: 数据库会话
        :param company_uuid: 公司UUID
        :param company_name: 公司名称
        :param store_id: 门店ID
        :param store_uuid: 门店UUID
        :param store_name: 门店名称
        :param operator_id: 操作人ID
        :param operator_name: 操作人姓名
        :param request_data: 请求数据
        :return: 订单创建结果
        """
        try:
            account_list = [{"name": acc.name, "mobile": acc.mobile} for acc in request_data.account_list]
            unit_price = float(request_data.unit_price)

            # 1. 检查账号信息是否已存在
            existing_info = await AddAccountDao.check_account_exists(query_db, account_list)
            error_messages = []

            if existing_info["existing_phones"]:
                error_messages.append(f"以下手机号已存在: {', '.join(existing_info['existing_phones'])}")

            if existing_info["existing_names"]:
                error_messages.append(f"以下姓名已存在: {', '.join(existing_info['existing_names'])}")

            if error_messages:
                raise ValidationException(message="; ".join(error_messages))

            # 2. 计算总金额
            account_count = len(account_list)
            total_amount = account_count * unit_price

            # 3. 生成订单号
            order_number = cls._generate_order_number()

            # 4. 直接使用余额扣费（参考查询信用的模式）
            payment_result = await cls._process_balance_payment(
                query_db, company_uuid, operator_id, total_amount,
                account_count, account_list
            )

            # 5. 支付成功后直接创建账号
            if payment_result.get("status") == "SUCCESS":
                # 计算有效期（1年）
                from datetime import datetime, timedelta
                expire_time = datetime.now() + timedelta(days=365)

                # 批量创建账号
                created_accounts = await AddAccountDao.create_accounts_batch(
                    query_db, company_uuid, company_name, store_id, store_uuid, store_name,
                    account_list, expire_time, operator_id
                )

                # 记录交易流水
                await AddAccountDao.record_add_account_transaction(
                    query_db, payment_result.get("transaction_no"), company_uuid, operator_id, operator_name,
                    account_list, total_amount, payment_result.get("balance_before"), payment_result.get("balance_after")
                )

                # 提交事务
                await query_db.commit()

                # 6. 构建响应数据
                response = AddAccountResponse(
                    order_number=payment_result.get("transaction_no"),
                    total_amount=Decimal(str(total_amount)),
                    account_count=account_count,
                    account_list=request_data.account_list
                )

                return response
            else:
                raise BusinessException(message=payment_result.get("message", "支付失败"))

        except ValidationException as e:
            logger.error(f"创建加账号订单验证失败: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"创建加账号订单失败: {str(e)}")
            raise BusinessException(message=f"创建加账号订单失败: {str(e)}")



    @classmethod
    async def get_company_account_list_service(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        page: int = 1,
        size: int = 20
    ) -> Dict[str, Any]:
        """
        获取公司账号列表服务

        :param query_db: 数据库会话
        :param company_uuid: 公司UUID
        :param page: 页码
        :param size: 每页数量
        :return: 账号列表数据
        """
        try:
            # 获取账号列表
            result = await AddAccountDao.get_company_account_list(
                query_db, company_uuid, page, size
            )

            # 获取账号统计
            account_count = await AddAccountDao.get_company_account_count(query_db, company_uuid)

            # 合并数据
            result.update(account_count)

            return result

        except Exception as e:
            logger.error(f"获取公司账号列表失败: {str(e)}")
            raise BusinessException(message=f"获取公司账号列表失败: {str(e)}")

    @staticmethod
    def _generate_order_number() -> str:
        """生成订单号"""
        import time
        timestamp = str(int(time.time() * 1000))
        random_str = str(uuid.uuid4()).replace('-', '')[:8].upper()
        return f"ADD_ACCOUNT_{timestamp}_{random_str}"

    @classmethod
    async def _process_balance_payment(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        user_id: str,
        amount: float,
        account_count: int,
        account_list: List[Dict[str, str]]
    ) -> Dict[str, Any]:
        """
        处理余额支付（参考查询信用的模式）

        :param query_db: 数据库会话
        :param company_uuid: 公司UUID
        :param user_id: 用户ID
        :param amount: 金额
        :param account_count: 账号数量
        :param account_list: 账号列表
        :return: 支付结果
        """
        try:
            # 1. 检查公司余额
            balance_query = text("""
                SELECT balance FROM company
                WHERE id = :company_id
            """)

            balance_result = await query_db.execute(balance_query, {"company_id": company_uuid})
            balance_row = balance_result.fetchone()

            if not balance_row:
                raise BusinessException(message="公司信息不存在")

            current_balance = float(balance_row.balance)

            if current_balance < amount:
                raise BusinessException(message=f"余额不足，当前余额: {current_balance}元，需要: {amount}元")

            # 2. 生成交易流水号
            import time
            import uuid
            timestamp = int(time.time() * 1000)
            transaction_no = f"TXN{timestamp}{str(uuid.uuid4()).replace('-', '')[:8].upper()}"

            # 3. 扣除余额
            new_balance = current_balance - amount

            update_balance_query = text("""
                UPDATE company
                SET balance = :new_balance
                WHERE id = :company_id
            """)

            await query_db.execute(update_balance_query, {
                "new_balance": new_balance,
                "company_id": company_uuid
            })

            logger.info(f"余额扣费成功: {current_balance} -> {new_balance}, 扣费金额: {amount}")

            return {
                "transaction_no": transaction_no,
                "amount": amount,
                "balance_before": current_balance,
                "balance_after": new_balance,
                "status": "SUCCESS",
                "message": "支付成功"
            }

        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"余额支付失败: {str(e)}")
            raise BusinessException(message=f"支付失败: {str(e)}")

    @staticmethod
    async def _get_order_info(query_db: AsyncSession, order_number: str) -> Dict[str, Any]:
        """获取订单信息"""
        # 这里需要根据实际的订单表结构实现
        # 暂时返回模拟数据
        import json
        return {
            "order_number": order_number,
            "account_info": json.dumps([
                {"name": "张三", "mobile": "***********"},
                {"name": "李四", "mobile": "***********"}
            ], ensure_ascii=False),
            "total_amount": 400.00,
            "status": "pending"
        }

    @staticmethod
    async def _update_order_status(query_db: AsyncSession, order_number: str, status: str) -> bool:
        """更新订单状态"""
        # 这里需要根据实际的订单表结构实现
        # 暂时返回True
        return True
