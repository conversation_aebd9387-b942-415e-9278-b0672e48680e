from sqlalchemy import Column, String, DateTime, Integer
from datetime import datetime
from config.database import Base


class ServiceProduct(Base):
    """服务产品关联表"""

    __tablename__ = 'service_product'

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='自增主键')
    
    # 关联字段
    staff_id = Column(Integer, nullable=True, comment='员工ID')
    productid = Column(Integer, nullable=True, comment='产品ID')
    store_uuid = Column(String(64), nullable=True, comment='门店UUID')
    company_uuid = Column(String(64), nullable=True, comment='公司UUID')
    
    # 时间字段
    create_time = Column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    def __repr__(self):
        return f"<ServiceProduct(id={self.id}, staff_id={self.staff_id}, productid={self.productid}, store_uuid='{self.store_uuid}')>"
