from fastapi import APIRouter, Depends, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from config.get_db import get_db
from module_admin.service.training_service import TrainingService
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, ResourceNotFoundException, BusinessException

# 公开培训API路由 - 不需要认证
public_training_controller = APIRouter(prefix='/api/v1/public/training')

@public_training_controller.get('/courses', summary="获取公开培训课程列表")
async def get_public_training_courses(
    request: Request,
    page: int = Query(1, description="页码"),
    size: int = Query(20, description="每页数量"),
    keyword: Optional[str] = Query(None, description="关键词"),
    category_id: Optional[str] = Query(None, description="分类ID"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    limit: Optional[int] = Query(None, description="限制返回数量（用于首页展示）"),
    query_db: AsyncSession = Depends(get_db)
):
    """获取公开培训课程列表接口（无需认证）
    
    支持分页获取培训课程列表，支持按关键词、分类、时间范围筛选
    兼容商家端首页展示需求
    """
    try:
        # 如果指定了limit参数，使用商家培训课程接口逻辑
        if limit:
            result = await TrainingService.get_merchant_training_courses_service(query_db, limit)
        else:
            # 使用培训列表接口逻辑
            result = await TrainingService.get_training_list_service(
                query_db, page, size, keyword, category_id, start_date, end_date
            )
        
        return ResponseUtil.success(
            msg="获取公开培训课程列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"获取公开培训课程列表查询异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except BusinessException as e:
        logger.error(f"获取公开培训课程列表业务异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"获取公开培训课程列表系统异常: {str(e)}")
        return ResponseUtil.error(msg="获取培训课程列表失败")

@public_training_controller.get('/categories', summary="获取公开培训分类列表")
async def get_public_training_categories(
    request: Request,
    query_db: AsyncSession = Depends(get_db)
):
    """获取公开培训分类列表接口（无需认证）"""
    try:
        result = await TrainingService.get_training_category_service(query_db)
        
        return ResponseUtil.success(
            msg="获取公开培训分类列表成功",
            data=result
        )
    except QueryException as e:
        logger.error(f"获取公开培训分类列表查询异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except BusinessException as e:
        logger.error(f"获取公开培训分类列表业务异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"获取公开培训分类列表系统异常: {str(e)}")
        return ResponseUtil.error(msg="获取培训分类列表失败")

@public_training_controller.get('/course/{course_id}', summary="获取公开培训课程详情")
async def get_public_training_course_detail(
    request: Request,
    course_id: str,
    query_db: AsyncSession = Depends(get_db)
):
    """获取公开培训课程详情接口（无需认证）"""
    try:
        # 这里需要实现获取单个课程详情的服务方法
        # 可以复用现有的课程详情逻辑
        result = await TrainingService.get_course_one_service(query_db, course_id)
        
        return ResponseUtil.success(
            msg="获取公开培训课程详情成功",
            data=result
        )
    except ResourceNotFoundException as e:
        logger.error(f"公开培训课程不存在: {e.message}")
        return ResponseUtil.error(msg="课程不存在")
    except QueryException as e:
        logger.error(f"获取公开培训课程详情查询异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except BusinessException as e:
        logger.error(f"获取公开培训课程详情业务异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"获取公开培训课程详情系统异常: {str(e)}")
        return ResponseUtil.error(msg="获取课程详情失败")
