import { post, get } from '../utlis/require.js'

/**
 * 支付相关API
 */

// 获取用户余额
export const getUserBalance = () => {
  return get('/api/v1/payment/balance')
}

// 获取余额流水
export const getBalanceFlow = (params = {}) => {
  return get('/api/v1/payment/balance/flow', params)
}

// 创建充值订单
export const createRecharge = (data) => {
  return post('/api/v1/payment/recharge', data, { contentType: 'application/json' })
}

// 创建二维码充值订单
export const createQrcodeRecharge = (data) => {
  return post('/api/v1/payment/recharge/qrcode', data, { contentType: 'application/json' })
}

// 易宝支付回调
export const yeepayNotify = (data) => {
  return post('/api/v1/payment/yeepay/notify', data)
}

// 查询支付订单状态
export const queryPaymentOrderStatus = (orderNo) => {
  return get(`/api/v1/payment/order/status/${orderNo}`)
}

// 创建提现申请
export const createWithdrawalApplication = (data) => {
  return post('/api/v1/payment/withdrawal/apply', data, { contentType: 'application/json' })
}

// 订单扫码支付
export const createOrderQrcodePayment = (data) => {
  return post('/api/v1/order/qrcode-payment', data, { contentType: 'application/json' })
}

// 检查账户状态
export const checkAccountStatus = (mobile) => {
  return get(`/api/v1/account/application/status/check/${mobile}`)
}

export default {
  getUserBalance,
  getBalanceFlow,
  createRecharge,
  createQrcodeRecharge,
  yeepayNotify,
  queryPaymentOrderStatus,
  createWithdrawalApplication,
  createOrderQrcodePayment,
  checkAccountStatus
}
