"""
提现账户数据访问层
"""
from sqlalchemy import select, and_, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from module_admin.entity.do.withdrawal_account import WithdrawalAccount
from exceptions.exception import QueryException, DatabaseException
from utils.log_util import logger


class WithdrawalAccountDao:
    """提现账户数据访问层"""
    
    @staticmethod
    async def get_withdrawal_accounts_by_company(
        db: AsyncSession, 
        company_uuid: str
    ) -> List[WithdrawalAccount]:
        """
        根据公司UUID获取提现账户列表
        
        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 提现账户列表
        """
        try:
            query = select(WithdrawalAccount).where(
                and_(
                    WithdrawalAccount.company_uuid == company_uuid,
                    WithdrawalAccount.is_delete == '0',
                    WithdrawalAccount.status == 'active'
                )
            ).order_by(WithdrawalAccount.is_default.desc(), WithdrawalAccount.created_at.desc())
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"查询提现账户列表失败: {str(e)}")
            raise QueryException(message=f"查询提现账户列表失败: {str(e)}")
    
    @staticmethod
    async def get_withdrawal_account_by_id(
        db: AsyncSession, 
        account_id: int, 
        company_uuid: str
    ) -> Optional[WithdrawalAccount]:
        """
        根据账户ID和公司UUID获取提现账户详情
        
        :param db: 数据库会话
        :param account_id: 账户ID
        :param company_uuid: 公司UUID
        :return: 提现账户信息
        """
        try:
            query = select(WithdrawalAccount).where(
                and_(
                    WithdrawalAccount.id == account_id,
                    WithdrawalAccount.company_uuid == company_uuid,
                    WithdrawalAccount.is_delete == '0'
                )
            )
            
            result = await db.execute(query)
            return result.scalars().first()
            
        except Exception as e:
            logger.error(f"查询提现账户详情失败: {str(e)}")
            raise QueryException(message=f"查询提现账户详情失败: {str(e)}")
    
    @staticmethod
    async def create_withdrawal_account(
        db: AsyncSession, 
        account_data: WithdrawalAccount
    ) -> WithdrawalAccount:
        """
        创建提现账户
        
        :param db: 数据库会话
        :param account_data: 账户数据
        :return: 创建的账户信息
        """
        try:
            db.add(account_data)
            await db.commit()
            await db.refresh(account_data)
            return account_data
            
        except Exception as e:
            await db.rollback()
            logger.error(f"创建提现账户失败: {str(e)}")
            raise DatabaseException(message=f"创建提现账户失败: {str(e)}")
    
    @staticmethod
    async def update_withdrawal_account(
        db: AsyncSession, 
        account_id: int, 
        company_uuid: str,
        update_data: Dict[str, Any]
    ) -> bool:
        """
        更新提现账户信息
        
        :param db: 数据库会话
        :param account_id: 账户ID
        :param company_uuid: 公司UUID
        :param update_data: 更新数据
        :return: 是否更新成功
        """
        try:
            query = update(WithdrawalAccount).where(
                and_(
                    WithdrawalAccount.id == account_id,
                    WithdrawalAccount.company_uuid == company_uuid,
                    WithdrawalAccount.is_delete == '0'
                )
            ).values(**update_data)
            
            result = await db.execute(query)
            await db.commit()
            
            return result.rowcount > 0
            
        except Exception as e:
            await db.rollback()
            logger.error(f"更新提现账户失败: {str(e)}")
            raise DatabaseException(message=f"更新提现账户失败: {str(e)}")
    
    @staticmethod
    async def check_account_exists(
        db: AsyncSession, 
        company_uuid: str,
        bank_account: str,
        exclude_id: Optional[int] = None
    ) -> bool:
        """
        检查银行账号是否已存在
        
        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :param bank_account: 银行账号
        :param exclude_id: 排除的账户ID（用于更新时检查）
        :return: 是否存在
        """
        try:
            conditions = [
                WithdrawalAccount.company_uuid == company_uuid,
                WithdrawalAccount.bank_account == bank_account,
                WithdrawalAccount.is_delete == '0'
            ]
            
            if exclude_id:
                conditions.append(WithdrawalAccount.id != exclude_id)
            
            query = select(WithdrawalAccount).where(and_(*conditions))
            result = await db.execute(query)
            
            return result.scalars().first() is not None
            
        except Exception as e:
            logger.error(f"检查账户是否存在失败: {str(e)}")
            raise QueryException(message=f"检查账户是否存在失败: {str(e)}")
    
    @staticmethod
    async def set_default_account(
        db: AsyncSession,
        account_id: int,
        company_uuid: str
    ) -> bool:
        """
        设置默认账户（先取消其他默认账户，再设置当前账户为默认）

        :param db: 数据库会话
        :param account_id: 账户ID
        :param company_uuid: 公司UUID
        :return: 是否设置成功
        """
        try:
            # 先取消该公司所有账户的默认状态
            await db.execute(
                update(WithdrawalAccount).where(
                    and_(
                        WithdrawalAccount.company_uuid == company_uuid,
                        WithdrawalAccount.is_delete == '0'
                    )
                ).values(is_default=False)
            )

            # 设置指定账户为默认
            result = await db.execute(
                update(WithdrawalAccount).where(
                    and_(
                        WithdrawalAccount.id == account_id,
                        WithdrawalAccount.company_uuid == company_uuid,
                        WithdrawalAccount.is_delete == '0'
                    )
                ).values(is_default=True)
            )

            await db.commit()
            return result.rowcount > 0

        except Exception as e:
            await db.rollback()
            logger.error(f"设置默认账户失败: {str(e)}")
            raise DatabaseException(message=f"设置默认账户失败: {str(e)}")

    @staticmethod
    async def delete_withdrawal_account(
        db: AsyncSession,
        account_id: int,
        company_uuid: str,
        updated_by: str
    ) -> bool:
        """
        删除提现账户（软删除）

        :param db: 数据库会话
        :param account_id: 账户ID
        :param company_uuid: 公司UUID
        :param updated_by: 更新人
        :return: 是否删除成功
        """
        try:
            from datetime import datetime

            result = await db.execute(
                update(WithdrawalAccount).where(
                    and_(
                        WithdrawalAccount.id == account_id,
                        WithdrawalAccount.company_uuid == company_uuid,
                        WithdrawalAccount.is_delete == '0'
                    )
                ).values(
                    is_delete='1',
                    updated_by=updated_by,
                    updated_at=datetime.now()
                )
            )

            await db.commit()
            return result.rowcount > 0

        except Exception as e:
            await db.rollback()
            logger.error(f"删除提现账户失败: {str(e)}")
            raise DatabaseException(message=f"删除提现账户失败: {str(e)}")
