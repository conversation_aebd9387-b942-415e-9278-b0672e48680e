<template>
  <view class="page">
    <appHead left fixed title="编辑产品"></appHead>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="spinner" size="40" color="#fdd118"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <scroll-view v-else scroll-y style="height: calc(100vh - 88rpx);">
      <!-- 基本信息 -->
      <view class="section">
        <view class="section-title">基本信息</view>
        <view class="form-container">
          <!-- 产品名称 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>产品名称</text>
            </view>
            <input
              class="form-input"
              v-model="formData.productName"
              placeholder="请输入产品名称"
              maxlength="50"
            />
          </view>

          <!-- 服务分类 -->
          <view class="form-item clickable" @click="handleSkillClick">
            <view class="form-label">
              <text class="required">*</text>
              <text>服务分类</text>
            </view>
            <view class="form-value">
              <text class="value-text" :class="{ placeholder: !selectedSkill }">
                {{ selectedSkill ? selectedSkill.name : '请选择服务分类' }}
              </text>
              <u-icon name="arrow-right" size="16" color="#c8c9cc" />
            </view>
          </view>
        </view>
      </view>

      <!-- 购买设置 -->
      <view class="section">
        <view class="section-title">购买设置</view>
        <view class="form-container">
          <!-- 最低购买量 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>最低购买量</text>
            </view>
            <input
              class="form-input number-input"
              v-model.number="formData.minNumber"
              placeholder="请输入最低购买量"
              type="number"
              :min="1"
            />
          </view>

          <!-- 最高购买量 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>最高购买量</text>
            </view>
            <input
              class="form-input number-input"
              v-model.number="formData.maxNumber"
              placeholder="请输入最高购买量"
              type="number"
              :min="1"
            />
          </view>

          <!-- 可预约时间 -->
          <view class="form-item">
            <view class="form-label">
              <text>可预约时间</text>
            </view>
            <view class="form-value">
              <view class="time-range-simple">
                <picker
                  mode="time"
                  :value="formData.startTime"
                  @change="onStartTimeChange"
                  class="time-picker-simple"
                >
                  <text class="time-text">{{ formData.startTime }}</text>
                </picker>
                <text class="time-separator">-</text>
                <picker
                  mode="time"
                  :value="formData.endTime"
                  @change="onEndTimeChange"
                  class="time-picker-simple"
                >
                  <text class="time-text">{{ formData.endTime }}</text>
                </picker>
              </view>
              <u-icon name="arrow-right" size="14" color="#c8c9cc" />
            </view>
          </view>
        </view>
      </view>

      <!-- 详细信息 -->
      <view class="section">
        <view class="section-title">详细信息</view>
        <view class="form-container">
          <!-- 现价 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>现价</text>
            </view>
            <input
              class="form-input number-input"
              v-model.number="formData.nowPrice"
              placeholder="请输入现价"
              type="digit"
            />
          </view>

          <!-- 会员价 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>会员价</text>
            </view>
            <input
              class="form-input number-input"
              v-model.number="formData.vipPrice"
              placeholder="请输入会员价"
              type="digit"
            />
          </view>

          <!-- 服务时长 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>服务时长(分钟)</text>
            </view>
            <input
              class="form-input number-input"
              v-model.number="formData.duration"
              placeholder="请输入服务时长"
              type="number"
            />
          </view>

          <!-- 价格单位 -->
          <view class="form-item clickable" @click="showPriceUnitPicker = true">
            <view class="form-label">
              <text class="required">*</text>
              <text>价格单位</text>
            </view>
            <view class="form-value">
              <text class="value-text" :class="{ placeholder: !formData.priceUnit }">
                {{ formData.priceUnit || '请选择价格单位' }}
              </text>
              <u-icon name="arrow-right" size="14" color="#c8c9cc" />
            </view>
          </view>

          <!-- 提成类型 -->
          <view class="form-item clickable" @click="showCommissionTypePicker = true">
            <view class="form-label">
              <text class="required">*</text>
              <text>提成类型</text>
            </view>
            <view class="form-value">
              <text class="value-text" :class="{ placeholder: formData.commissionType === null }">
                {{ getCommissionTypeText() }}
              </text>
              <u-icon name="arrow-right" size="14" color="#c8c9cc" />
            </view>
          </view>

          <!-- 提成数值 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>{{ getCommissionLabel() }}</text>
            </view>
            <view class="form-input-container">
              <input
                class="form-input number-input"
                v-model.number="formData.defineCommission"
                :placeholder="getCommissionPlaceholder()"
                type="digit"
              />
              <text class="input-unit">{{ getCommissionUnit() }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 图片设置 -->
      <view class="section">
        <view class="section-title">图片设置</view>
        <view class="form-container">
          <!-- 产品主图 -->
          <view class="form-item image-item">
            <view class="form-label image-label">
              <view class="label-text">
                <text class="required">*</text>
                <text>产品主图</text>
              </view>
              <view class="generate-btn" :class="{ generating: generating }" @click="handleGenerateImage">
                <text class="generate-btn-text">{{ generating ? '正在生成中...' : '一键生成' }}</text>
              </view>
            </view>
            <view class="image-upload-container">
              <view v-if="formData.mainImage" class="image-preview main-image" @click="previewImage(formData.mainImage)">
                <image :src="formData.mainImage" mode="aspectFill" />
                <view class="image-delete" @click.stop="removeMainImage">
                  <u-icon name="close" size="16" color="#fff" />
                </view>
              </view>
              <view v-else class="upload-btn main-upload" @click="uploadMainImage">
                <u-icon name="plus" size="40" color="#c8c9cc" />
                <text class="upload-text">上传主图</text>
              </view>
            </view>
          </view>

          <!-- 产品详情图 -->
          <view class="form-item image-item">
            <view class="form-label">
              <text>产品详情图</text>
            </view>
            <view class="detail-images-container">
              <view class="image-upload-list">
                <view
                  v-for="(image, index) in formData.detailImages"
                  :key="index"
                  class="image-preview detail-image"
                  @click="previewImage(image)"
                >
                  <image :src="image" mode="aspectFill" />
                  <view class="image-delete" @click.stop="removeDetailImage(index)">
                    <u-icon name="close" size="16" color="#fff" />
                  </view>
                </view>
                <view v-if="formData.detailImages.length < 5" class="upload-btn detail-upload" @click="uploadDetailImage">
                  <u-icon name="plus" size="30" color="#c8c9cc" />
                  <text class="upload-text small">添加图片</text>
                </view>
              </view>
              <text class="form-tip">最多上传5张详情图</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部提交按钮 -->
      <view class="submit-section">
        <button class="submit-btn" @click="handleSubmit" :disabled="submitting">
          {{ submitting ? '保存中...' : '保存修改' }}
        </button>
      </view>
    </scroll-view>

    <!-- 技能选择弹窗 -->
    <view v-if="showSkillPopup" class="popup-mask" @click="showSkillPopup = false">
      <view class="popup-content" @click.stop>
        <view class="popup-header">
          <text class="popup-title">选择服务分类</text>
          <view class="close-btn" @click="showSkillPopup = false">
            <u-icon name="close" size="20" color="#999" />
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-container">
          <view class="search-box">
            <u-icon name="search" size="16" color="#999" />
            <input
              class="search-input"
              v-model="searchKeyword"
              placeholder="搜索服务分类"
              @input="onSearchInput"
              confirm-type="search"
            />
            <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
              <u-icon name="close-circle-fill" size="16" color="#ccc" />
            </view>
          </view>
        </view>

        <scroll-view scroll-y class="skill-list" style="max-height: 50vh;">
          <view v-if="loading" class="loading-text">加载中...</view>
          <view v-else-if="filteredSkillOptions.length === 0 && searchKeyword" class="empty-text">
            未找到匹配的服务分类
          </view>
          <view v-else-if="filteredSkillOptions.length === 0" class="empty-text">暂无可选择的服务分类</view>

          <view
            v-for="skill in filteredSkillOptions"
            :key="skill.id"
            class="skill-item"
            :class="{ active: selectedSkill && selectedSkill.id === skill.id }"
            @click="selectSkill(skill)"
          >
            <text class="skill-name">{{ skill.name }}</text>
            <u-icon v-if="selectedSkill && selectedSkill.id === skill.id" name="checkmark" size="16" color="#fdd118" />
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 价格单位选择弹窗 -->
    <view v-if="showPriceUnitPicker" class="popup-mask" @click="showPriceUnitPicker = false">
      <view class="popup-content" @click.stop>
        <view class="popup-header">
          <text class="popup-title">选择价格单位</text>
          <view class="close-btn" @click="showPriceUnitPicker = false">
            <u-icon name="close" size="20" color="#999" />
          </view>
        </view>

        <view class="unit-list">
          <view
            v-for="unit in priceUnitOptions"
            :key="unit"
            class="unit-item"
            :class="{ active: formData.priceUnit === unit }"
            @click="selectPriceUnit(unit)"
          >
            <text class="unit-text">{{ unit }}</text>
            <view v-if="formData.priceUnit === unit" class="check-icon">
              <u-icon name="checkmark" size="20" color="#fdd118" />
            </view>
          </view>
        </view>

        <view class="popup-footer">
          <button class="confirm-btn" @click="confirmPriceUnitSelection">确定</button>
        </view>
      </view>
    </view>

    <!-- 提成类型选择弹窗 -->
    <view v-if="showCommissionTypePicker" class="popup-mask" @click="showCommissionTypePicker = false">
      <view class="popup-content" @click.stop>
        <view class="popup-header">
          <text class="popup-title">选择提成类型</text>
          <view class="close-btn" @click="showCommissionTypePicker = false">
            <u-icon name="close" size="20" color="#999" />
          </view>
        </view>

        <view class="unit-list">
          <view
            v-for="type in commissionTypeOptions"
            :key="type.value"
            class="unit-item"
            :class="{ active: formData.commissionType === type.value }"
            @click="selectCommissionType(type.value)"
          >
            <view class="unit-content">
              <text class="unit-text">{{ type.label }}</text>
              <text class="unit-desc">{{ type.desc }}</text>
            </view>
            <view v-if="formData.commissionType === type.value" class="check-icon">
              <u-icon name="checkmark" size="20" color="#fdd118" />
            </view>
          </view>
        </view>

        <view class="popup-footer">
          <button class="confirm-btn" @click="confirmCommissionTypeSelection">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getProductDetail, updateProduct } from '@/api/product.js'
import { getCompanySkills } from '@/api/skill.js'
import { generateProductImage } from '@/api/ai-image.js'

export default {
  data() {
    return {
      productId: null, // 产品ID
      loading: true,
      submitting: false,
      generating: false, // AI图片生成中状态
      
      // 表单数据
      formData: {
        productName: '', // 产品名称
        mainImage: '', // 产品主图URL
        mainImageId: null, // 产品主图ID
        detailImages: [], // 产品详情图URL列表
        detailImageIds: [], // 产品详情图ID列表
        minNumber: 1, // 最低购买量
        maxNumber: 10, // 最高购买量
        startTime: '08:00', // 开始时间
        endTime: '18:00', // 结束时间
        // 详细信息
        nowPrice: '', // 现价
        vipPrice: '', // 会员价
        duration: 60, // 服务时长(分钟)
        priceUnit: '', // 价格单位
        commissionType: null, // 提成类型：1-固定金额，0-百分比
        defineCommission: '' // 提成数值
      },
      
      // 服务技能相关
      selectedSkill: null,
      skillOptions: [],
      filteredSkillOptions: [],
      showSkillPopup: false,
      searchKeyword: '',
      
      // 选择器相关
      showPriceUnitPicker: false,
      showCommissionTypePicker: false,
      priceUnitOptions: ['次', '人', '台'],
      commissionTypeOptions: [
        { value: 1, label: '固定金额', desc: '按固定金额计算提成' },
        { value: 0, label: '百分比', desc: '按销售额百分比计算提成' }
      ]
    }
  },
  
  onLoad(options) {
    if (options.productId) {
      this.productId = options.productId
      this.loadProductData()
    } else {
      uni.showToast({
        title: '产品ID不能为空',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },

  methods: {
    // 加载产品数据
    async loadProductData() {
      try {
        this.loading = true

        // 先加载服务技能列表
        const skillsResult = await getCompanySkills()

        // 处理API返回的数据结构
        if (skillsResult && skillsResult.data) {
          this.skillOptions = skillsResult.data
        } else if (Array.isArray(skillsResult)) {
          this.skillOptions = skillsResult
        } else {
          this.skillOptions = []
        }

        this.filteredSkillOptions = this.skillOptions

        // 再加载产品详情
        const productDetail = await getProductDetail(this.productId)

        // 填充表单数据（此时技能列表已经加载完成）
        this.fillFormData(productDetail)
      } catch (error) {
        console.error('加载产品数据失败:', error)
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } finally {
        this.loading = false
      }
    },

    // 填充表单数据
    fillFormData(productDetail) {
      // 基本信息
      this.formData.productName = productDetail.product_name || ''
      this.formData.minNumber = productDetail.min_number || 1
      this.formData.maxNumber = productDetail.max_number || 10

      // 解析营业时间
      if (productDetail.business_hours) {
        const timeRange = productDetail.business_hours.split('-')
        if (timeRange.length === 2) {
          this.formData.startTime = timeRange[0].trim()
          this.formData.endTime = timeRange[1].trim()
        }
      }

      // 设置选中的服务技能 - 通过技能名称匹配
      if (productDetail.service_skill_name && this.skillOptions.length > 0) {
        // 通过技能名称匹配
        const matchedSkill = this.skillOptions.find(skill => skill.name === productDetail.service_skill_name)

        if (matchedSkill) {
          this.selectedSkill = matchedSkill
        } else {
          // 如果在技能列表中找不到，创建一个临时的技能对象
          this.selectedSkill = {
            id: 0, // 临时ID
            name: productDetail.service_skill_name,
            parent_id: 0 // 临时父级ID
          }
        }
      }

      // 处理图片
      if (productDetail.main_image_url) {
        this.formData.mainImage = productDetail.main_image_url
        this.formData.mainImageId = productDetail.main_image_id
      }

      if (productDetail.detail_images && productDetail.detail_images.length > 0) {
        this.formData.detailImages = productDetail.detail_images.map(img => img.url)
        this.formData.detailImageIds = productDetail.detail_images.map(img => img.id)
      }

      // SKU信息
      if (productDetail.skus && productDetail.skus.length > 0) {
        const sku = productDetail.skus[0] // 取第一个SKU
        this.formData.nowPrice = sku.now_price || ''
        this.formData.vipPrice = sku.vip_price || ''
        this.formData.duration = sku.duration || 60
        this.formData.priceUnit = sku.type_price_unit || ''
        this.formData.defineCommission = sku.define_commission || ''
        this.formData.commissionType = sku.commission_type
      }
    },

    // 处理服务技能点击
    handleSkillClick() {
      this.showSkillPopup = true
      this.searchKeyword = ''
      this.filteredSkillOptions = this.skillOptions
    },

    // 选择服务技能
    selectSkill(skill) {
      this.selectedSkill = skill
      this.showSkillPopup = false
    },

    // 搜索技能
    onSearchInput() {
      if (!this.searchKeyword.trim()) {
        this.filteredSkillOptions = this.skillOptions
      } else {
        this.filteredSkillOptions = this.skillOptions.filter(skill =>
          skill.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }
    },

    // 清空搜索
    clearSearch() {
      this.searchKeyword = ''
      this.filteredSkillOptions = this.skillOptions
    },

    // 时间选择
    onStartTimeChange(e) {
      this.formData.startTime = e.detail.value
    },

    onEndTimeChange(e) {
      this.formData.endTime = e.detail.value
    },

    // 提成类型相关方法
    getCommissionTypeText() {
      if (this.formData.commissionType === null) return '请选择提成类型'
      return this.formData.commissionType === 1 ? '固定金额' : '百分比'
    },

    getCommissionLabel() {
      return this.formData.commissionType === 1 ? '提成金额' : '提成比例'
    },

    getCommissionPlaceholder() {
      return this.formData.commissionType === 1 ? '请输入提成金额' : '请输入提成比例'
    },

    getCommissionUnit() {
      return this.formData.commissionType === 1 ? '元' : '%'
    },

    // 选择价格单位
    selectPriceUnit(unit) {
      this.formData.priceUnit = unit
    },

    // 确认价格单位选择
    confirmPriceUnitSelection() {
      if (!this.formData.priceUnit) {
        uni.showToast({
          title: '请选择价格单位',
          icon: 'none'
        })
        return
      }
      this.showPriceUnitPicker = false
    },

    // 选择提成类型
    selectCommissionType(type) {
      this.formData.commissionType = type
      // 清空提成数值，让用户重新输入
      this.formData.defineCommission = ''
    },

    // 确认提成类型选择
    confirmCommissionTypeSelection() {
      if (this.formData.commissionType === null) {
        uni.showToast({
          title: '请选择提成类型',
          icon: 'none'
        })
        return
      }
      this.showCommissionTypePicker = false
    },

    // 图片预览
    previewImage(imageUrl) {
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl
      })
    },

    // 上传主图
    uploadMainImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          try {
            uni.showLoading({ title: '上传中...' })

            // 调用产品图片上传接口
            const uploadResult = await this.uploadImageToServer(res.tempFilePaths[0], 'main')
            this.formData.mainImage = uploadResult.file_url
            this.formData.mainImageId = uploadResult.id

            uni.hideLoading()
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            })
          } catch (error) {
            uni.hideLoading()
            console.error('上传图片失败:', error)
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            })
          }
        },
        fail: (error) => {
          console.error('选择图片失败:', error)
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    // 上传详情图
    uploadDetailImage() {
      const remainCount = 5 - this.formData.detailImages.length
      uni.chooseImage({
        count: remainCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          try {
            uni.showLoading({ title: '上传中...' })

            // 批量上传详情图
            for (let i = 0; i < res.tempFilePaths.length; i++) {
              const uploadResult = await this.uploadImageToServer(
                res.tempFilePaths[i],
                'detail'
              )
              this.formData.detailImages.push(uploadResult.file_url)
              this.formData.detailImageIds.push(uploadResult.id)
            }

            uni.hideLoading()
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            })
          } catch (error) {
            uni.hideLoading()
            console.error('上传图片失败:', error)
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            })
          }
        },
        fail: (error) => {
          console.error('选择图片失败:', error)
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    // 上传图片到服务器
    async uploadImageToServer(filePath, imageType) {
      try {
        // 获取当前产品名称，如果没有则使用默认值
        const productName = this.formData.productName.trim() || '编辑产品'



        // 根据图片类型生成remark
        const remark = imageType === 'main'
          ? `产品主图-${productName}`
          : `产品详情图-${productName}`

        // 使用带token验证的文件上传接口，会创建数据库记录并获取当前用户信息
        const uploadResult = await this.uploadFileWithAuth(filePath, {
          showLoading: false, // 外层已经有loading了
          showToast: false,   // 外层会处理错误提示
          route: 'product',   // 指定文件路径为产品相关
          remark: remark      // 指定备注格式
        })

        if (uploadResult.success) {
          // 返回数据库ID和图片URL，格式与原接口保持一致
          return {
            file_url: uploadResult.url,
            id: uploadResult.data.id || uploadResult.fileInfo.id || 1 // 数据库记录ID
          }
        } else {
          throw new Error(uploadResult.message || '上传失败')
        }
      } catch (error) {
        console.error('上传图片失败:', error)
        throw error
      }
    },

    // 带token验证的文件上传方法（专门用于产品图片上传）
    uploadFileWithAuth(filePath, options = {}) {
      return new Promise((resolve, reject) => {
        // 默认配置
        const uploadConfig = {
          showLoading: true,
          loadingTitle: '上传中...',
          showToast: true,
          route: null,
          remark: null,
          ...options
        }

        // 显示加载提示
        if (uploadConfig.showLoading) {
          uni.showLoading({
            title: uploadConfig.loadingTitle,
            mask: true
          })
        }

        // 构建formData
        const formData = {}
        if (uploadConfig.route) {
          formData.route = uploadConfig.route
        }
        if (uploadConfig.remark) {
          formData.remark = uploadConfig.remark
        }

        // 获取配置的基础URL
        let baseUrl
        try {
          baseUrl = this.$store.state.baseUrl || uni.getStorageSync('baseUrl')
          if (!baseUrl) {
            const config = require('../../setConfig.js')
            baseUrl = config.host
          }
        } catch (error) {
          console.warn('获取baseUrl失败', error)
          baseUrl = 'https://api.jingangai.cn'
        }

        // 获取token
        const token = this.$store.state.token || uni.getStorageSync('token')

        uni.uploadFile({
          url: `${baseUrl}/api/v1/file/upload`, // 使用需要token验证的文件上传接口
          filePath: filePath,
          name: 'file',
          formData: formData,
          header: {
            'Authorization': `Bearer ${token}`
          },
          success: (res) => {
            if (uploadConfig.showLoading) {
              uni.hideLoading()
            }

            try {
              const result = JSON.parse(res.data)
              if (result.success || result.code === 200) {
                const fileInfo = result.data || result.fileInfo || {}
                const successResult = {
                  success: true,
                  message: result.message || '上传成功',
                  url: fileInfo.url || fileInfo.file_url,
                  data: result.data,
                  fileInfo: fileInfo
                }

                resolve(successResult)
              } else {
                const errorMsg = result.message || '上传失败'
                reject(new Error(errorMsg))
              }
            } catch (error) {
              reject(new Error('解析上传结果失败'))
            }
          },
          fail: () => {
            if (uploadConfig.showLoading) {
              uni.hideLoading()
            }
            reject(new Error('网络请求失败'))
          }
        })
      })
    },

    // 删除主图
    removeMainImage() {
      this.formData.mainImage = ''
      this.formData.mainImageId = null
    },

    // AI一键生成图片处理函数
    async handleGenerateImage() {
      // 检查产品名称是否已输入
      const productName = this.formData.productName?.trim()
      if (!productName) {
        uni.showToast({
          title: '请先输入产品名称',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 检查产品名称长度
      if (productName.length < 2) {
        uni.showToast({
          title: '产品名称至少需要2个字符',
          icon: 'none',
          duration: 2000
        })
        return
      }

      if (productName.length > 50) {
        uni.showToast({
          title: '产品名称不能超过50个字符',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 防止重复点击
      if (this.generating) {
        return
      }

      try {
        this.generating = true

        console.log('开始生成产品主图，产品名称:', productName)

        // 调用AI图片生成接口
        const result = await generateProductImage({
          service_name: productName
        })

        if (result && result.success && result.image_url) {
          // 生成成功，直接使用OBS图片URL（后端已经处理了下载和上传）
          this.formData.mainImage = result.image_url
          this.formData.mainImageId = null // AI生成的图片没有数据库ID

          uni.showToast({
            title: 'AI图片生成成功！',
            icon: 'success',
            duration: 2000
          })

          console.log('产品主图生成成功:', result.image_url)

          // 可选：震动反馈
          try {
            uni.vibrateShort()
          } catch (e) {
            // 忽略震动错误
          }
        } else {
          // 生成失败
          const errorMsg = (result && result.message) || '图片生成失败'
          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000
          })
          console.error('产品主图生成失败:', result)
        }

      } catch (error) {
        console.error('生成产品主图异常:', error)

        let errorMsg = '图片生成失败，请稍后重试'
        if (error.message) {
          if (error.message.includes('timeout') || error.message.includes('超时')) {
            errorMsg = 'AI生成超时，请稍后重试'
          } else if (error.message.includes('network') || error.message.includes('网络')) {
            errorMsg = '网络连接失败，请检查网络'
          } else if (error.message.includes('AI服务')) {
            errorMsg = 'AI服务暂时不可用，请稍后重试'
          } else if (error.message.includes('参数')) {
            errorMsg = '产品名称格式有误，请重新输入'
          } else {
            errorMsg = error.message
          }
        }

        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
      } finally {
        this.generating = false
      }
    },

    // 删除详情图
    removeDetailImage(index) {
      this.formData.detailImages.splice(index, 1)
      this.formData.detailImageIds.splice(index, 1)
    },

    // 表单验证
    validateForm() {
      if (!this.formData.productName.trim()) {
        uni.showToast({ title: '请输入产品名称', icon: 'none' })
        return false
      }

      if (!this.selectedSkill) {
        uni.showToast({ title: '请选择服务分类', icon: 'none' })
        return false
      }

      if (!this.formData.mainImage) {
        uni.showToast({ title: '请上传产品主图', icon: 'none' })
        return false
      }

      if (!this.formData.nowPrice || this.formData.nowPrice <= 0) {
        uni.showToast({ title: '请输入有效的现价', icon: 'none' })
        return false
      }

      if (!this.formData.vipPrice || this.formData.vipPrice <= 0) {
        uni.showToast({ title: '请输入有效的会员价', icon: 'none' })
        return false
      }

      if (!this.formData.duration || this.formData.duration <= 0) {
        uni.showToast({ title: '请输入有效的服务时长', icon: 'none' })
        return false
      }

      if (!this.formData.priceUnit) {
        uni.showToast({ title: '请选择价格单位', icon: 'none' })
        return false
      }

      if (this.formData.commissionType === null) {
        uni.showToast({ title: '请选择提成类型', icon: 'none' })
        return false
      }

      if (!this.formData.defineCommission || this.formData.defineCommission < 0) {
        uni.showToast({ title: '请输入有效的提成数值', icon: 'none' })
        return false
      }

      return true
    },

    // 提交表单
    async handleSubmit() {
      if (!this.validateForm()) {
        return
      }

      try {
        this.submitting = true

        // 构建提交数据
        const submitData = {
          product_name: this.formData.productName.trim(),
          service_skill_id: this.selectedSkill.id,
          service_skill_name: this.selectedSkill.name,
          service_skill_main_id: this.selectedSkill.parent_id,
          main_image_id: this.formData.mainImageId,
          main_image_url: this.formData.mainImage, // 添加主图URL，用于AI生成的图片
          detail_image_ids: this.formData.detailImageIds,
          min_number: this.formData.minNumber,
          max_number: this.formData.maxNumber,
          business_hours: `${this.formData.startTime}-${this.formData.endTime}`,
          // SKU信息
          sku_info: {
            name: this.formData.productName.trim(),
            now_price: parseFloat(this.formData.nowPrice),
            vip_price: parseFloat(this.formData.vipPrice),
            duration: parseInt(this.formData.duration),
            type_price_unit: this.formData.priceUnit,
            define_commission: parseFloat(this.formData.defineCommission),
            commission_type: this.formData.commissionType
          }
        }

        // 调用更新产品API
        await updateProduct(this.productId, submitData)

        uni.showToast({
          title: '产品更新成功',
          icon: 'success'
        })

        // 延迟返回上一页，并通知刷新数据
        setTimeout(() => {
          // 通过事件总线通知上一页刷新数据
          uni.$emit('productUpdated', {
            productId: this.productId,
            productData: submitData
          })

          uni.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('更新产品失败:', error)
        uni.showToast({
          title: error.message || '更新失败',
          icon: 'none'
        })
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.section {
  background-color: #fff;
  margin-bottom: 20rpx;

  .section-title {
    padding: 30rpx 30rpx 20rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.form-container {
  padding: 0 30rpx 30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 36rpx 0;
  border-bottom: 1px solid #f0f0f0;
  min-height: 120rpx;

  &:last-child {
    border-bottom: none;
  }

  &.clickable {
    cursor: pointer;
  }

  &.image-item {
    align-items: flex-start;
    flex-direction: column;
    min-height: auto;

    .form-label {
      margin-bottom: 20rpx;
    }

    .form-label.image-label {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .label-text {
      display: flex;
      align-items: center;
    }
  }
}

.form-label {
  display: flex;
  align-items: center;
  min-width: 220rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;

  .required {
    color: #ff4757;
    margin-right: 8rpx;
    font-size: 32rpx;
  }
}

.form-input {
  flex: 1;
  padding: 24rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 30rpx;
  background-color: #fff;
  min-height: 80rpx;
  line-height: 1.4;

  &.number-input {
    text-align: right;
  }
}

.form-value {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 1px solid #e0e0e0;
  border-radius: 8rpx;
  background-color: #fff;
  min-height: 80rpx;

  .value-text {
    font-size: 30rpx;
    color: #333;
    line-height: 1.4;

    &.placeholder {
      color: #999;
    }
  }
}

.form-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;

  .form-input {
    padding-right: 60rpx;
  }

  .input-unit {
    position: absolute;
    right: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

.time-range-simple {
  display: flex;
  align-items: center;
  flex: 1;

  .time-picker-simple {
    padding: 10rpx 20rpx;
    border-radius: 8rpx;
    background-color: #f8f8f8;
    min-width: 120rpx;
    text-align: center;

    .time-text {
      font-size: 30rpx;
      color: #333;
      line-height: 1.4;
    }
  }

  .time-separator {
    margin: 0 20rpx;
    font-size: 30rpx;
    color: #666;
    font-weight: 500;
  }
}

// 图片上传样式
.image-upload-container {
  width: 100%;
}

.image-preview {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;

  &.main-image {
    width: 200rpx;
    height: 200rpx;
  }

  &.detail-image {
    width: 160rpx;
    height: 160rpx;
  }

  image {
    width: 100%;
    height: 100%;
  }

  .image-delete {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    width: 32rpx;
    height: 32rpx;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  background-color: #fafafa;

  &.main-upload {
    width: 200rpx;
    height: 200rpx;
  }

  &.detail-upload {
    width: 160rpx;
    height: 160rpx;
  }

  .upload-text {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #999;

    &.small {
      font-size: 20rpx;
    }
  }
}

.detail-images-container {
  width: 100%;

  .image-upload-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
  }

  .form-tip {
    display: block;
    margin-top: 20rpx;
    font-size: 24rpx;
    color: #999;
  }
}

// 提交按钮
.submit-section {
  padding: 40rpx 30rpx;
  background-color: #fff;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #fdd118 0%, #f0c419 100%);
    border-radius: 44rpx;
    border: none;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
      opacity: 0.6;
    }
  }
}

// 弹窗样式
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-content {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;

  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.search-container {
  padding: 20rpx 30rpx;

  .search-box {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background-color: #f5f5f5;
    border-radius: 50rpx;

    .search-input {
      flex: 1;
      margin-left: 20rpx;
      font-size: 28rpx;
      background-color: transparent;
      border: none;
    }

    .clear-btn {
      margin-left: 20rpx;
    }
  }
}

.skill-list {
  max-height: 60vh;

  .loading-text,
  .empty-text {
    text-align: center;
    padding: 60rpx 30rpx;
    font-size: 28rpx;
    color: #999;
  }

  .skill-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &.active {
      background-color: #fff8e1;
    }

    .skill-name {
      font-size: 28rpx;
      color: #333;
    }
  }
}

// 选择器弹窗样式
.unit-list {
  padding: 20rpx 0;

  .unit-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &.active {
      background-color: #fff8e1;
    }

    .unit-content {
      flex: 1;

      .unit-text {
        font-size: 28rpx;
        color: #333;
        display: block;
      }

      .unit-desc {
        font-size: 24rpx;
        color: #999;
        margin-top: 8rpx;
        display: block;
      }
    }

    .unit-text {
      font-size: 28rpx;
      color: #333;
    }

    .check-icon {
      margin-left: 20rpx;
    }
  }
}

.popup-footer {
  padding: 30rpx;
  border-top: 1px solid #f0f0f0;

  .confirm-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #fdd118 0%, #f0c419 100%);
    border-radius: 44rpx;
    border: none;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* AI一键生成按钮样式 */
.generate-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #fdd118 0%, #f4c430 100%);
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.25);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(253, 209, 24, 0.3);
  margin-left: 20rpx;
}

.generate-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.4);
  background: linear-gradient(135deg, #e6c115 0%, #d4a017 100%);
}

.generate-btn.generating {
  background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
  pointer-events: none;
  opacity: 0.8;
}

.generate-btn-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
  letter-spacing: 1rpx;
}
</style>
