from pydantic import BaseModel, Field, ConfigDict, validator
from typing import List, Optional
from decimal import Decimal


class AccountInfo(BaseModel):
    """账号信息模型"""
    name: str = Field(..., description="姓名", min_length=2, max_length=20)
    mobile: str = Field(..., description="手机号", pattern=r'^1[3-9]\d{9}$')


class AddAccountRequest(BaseModel):
    """添加账号请求模型"""
    model_config = ConfigDict(from_attributes=True)

    account_list: List[AccountInfo] = Field(..., description="账号列表", min_items=1, max_items=10)
    unit_price: Optional[Decimal] = Field(200.00, description="单价（元/年）")
    remark: Optional[str] = Field(None, max_length=500, description="备注")

    @validator('account_list')
    def validate_account_list(cls, v):
        """验证账号列表"""
        if not v:
            raise ValueError('账号列表不能为空')

        # 检查手机号重复
        mobile_list = [acc.mobile for acc in v]
        if len(mobile_list) != len(set(mobile_list)):
            raise ValueError('账号列表中存在重复的手机号')

        # 检查姓名重复
        name_list = [acc.name for acc in v]
        if len(name_list) != len(set(name_list)):
            raise ValueError('账号列表中存在重复的姓名')

        return v


class AddAccountResponse(BaseModel):
    """添加账号响应模型"""
    model_config = ConfigDict(from_attributes=True)

    order_number: str = Field(..., description="订单号")
    total_amount: Decimal = Field(..., description="总金额")
    account_count: int = Field(..., description="账号数量")
    account_list: List[AccountInfo] = Field(..., description="账号列表")
    payment_url: Optional[str] = Field(None, description="支付链接")
    pay_token: Optional[str] = Field(None, description="支付令牌")


class AccountInfoModel(BaseModel):
    """账号信息模型"""
    model_config = ConfigDict(from_attributes=True)
    
    uuid: str = Field(..., description="账号UUID")
    mobile: str = Field(..., description="手机号")
    name: str = Field(..., description="姓名")
    role_name: Optional[str] = Field(None, description="角色名称")
    status_name: Optional[str] = Field(None, description="状态名称")
    paid_expire_time: Optional[str] = Field(None, description="付费有效期")
    create_time: Optional[str] = Field(None, description="创建时间")


class CompanyAccountListResponse(BaseModel):
    """公司账号列表响应模型"""
    model_config = ConfigDict(from_attributes=True)
    
    total_count: int = Field(..., description="总账号数")
    paid_count: int = Field(..., description="付费账号数")
    free_count: int = Field(..., description="免费账号数")
    account_list: List[AccountInfoModel] = Field(..., description="账号列表")
