from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

from config.get_db import get_db
from module_admin.service.add_account_service import AddAccountService
from module_admin.service.auth_adapter import AuthAdapter
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.entity.vo.add_account_vo import AddAccountRequest
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from exceptions.exception import QueryException, BusinessException, ValidationException

# 使用统一的API路由前缀格式
add_account_controller = APIRouter(prefix='/api/v1/account', dependencies=[Depends(AuthAdapter.get_current_user)])


@add_account_controller.get('/company-info', summary="获取公司账号信息")
async def get_company_account_info(
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取公司账号信息接口

    获取当前公司的账号数量统计信息

    Args:
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        公司账号信息
    """
    try:
        # 获取当前用户信息
        company_uuid = current_user.user.company_id

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"获取公司账号信息，公司: {company_uuid}")

        # 调用服务层获取账号信息
        result = await AddAccountService.get_company_account_info_service(
            query_db, company_uuid
        )

        return ResponseUtil.success(
            msg="获取公司账号信息成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"获取公司账号信息验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"获取公司账号信息业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取公司账号信息异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'])


@add_account_controller.post('/create-order', summary="创建加账号订单")
async def create_add_account_order(
    request_data: AddAccountRequest,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """创建加账号订单接口

    为指定的手机号列表创建账号并生成支付订单

    Args:
        request_data: 加账号请求数据
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        订单创建结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id
        company_name = getattr(current_user.user, 'company_name', '')
        store_id = getattr(current_user.user, 'store_id', '')
        store_uuid = getattr(current_user.user, 'store_uuid', None)
        store_name = getattr(current_user.user, 'store_name', '')
        operator_name = getattr(current_user.user, 'name', '系统管理员')

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 创建加账号订单，公司: {company_uuid}, 账号数量: {len(request_data.account_list)}")

        # 调用服务层创建订单
        result = await AddAccountService.create_add_account_order_service(
            query_db, company_uuid, company_name, store_id, store_uuid, store_name,
            current_user_id, operator_name, request_data
        )

        return ResponseUtil.success(
            msg="创建加账号订单成功",
            data=result.dict()
        )

    except ValidationException as e:
        logger.error(f"创建加账号订单验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"创建加账号订单业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"创建加账号订单异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'])


@add_account_controller.get('/list', summary="获取公司账号列表")
async def get_company_account_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取公司账号列表接口

    获取当前公司的所有账号列表，包括付费和免费账号

    Args:
        page: 页码
        size: 每页数量
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        账号列表数据
    """
    try:
        # 获取当前用户信息
        company_uuid = current_user.user.company_id

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"获取公司账号列表，公司: {company_uuid}, 页码: {page}, 每页: {size}")

        # 调用服务层获取账号列表
        result = await AddAccountService.get_company_account_list_service(
            query_db, company_uuid, page, size
        )

        return ResponseUtil.success(
            msg="获取公司账号列表成功",
            data=result
        )

    except ValidationException as e:
        logger.error(f"获取公司账号列表验证失败: {e.message}")
        return ResponseUtil.validation_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"获取公司账号列表业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取公司账号列表异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'])



