import uuid
import httpx
import json
import base64
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from fastapi import Request
from utils.log_util import logger
from exceptions.exception import BusinessException, ValidationException
from config.env import YeepayConfig
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from module_admin.entity.do.company_withdrawal import CompanyWithdrawal
from module_admin.dao.company_dao import CompanyDao
from utils.transaction_manager import TransactionManager, FinancialOperationValidator


class PaymentService:



    @classmethod
    async def create_business_payment_order_service(
        cls,
        query_db: AsyncSession,
        user_id: str,
        user_name: str,
        business_type: str,
        business_type_name: str,
        amount: float,
        description: str,
        related_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建业务支付订单服务

        Args:
            query_db: 数据库会话
            user_id: 用户ID
            user_name: 用户名
            business_type: 业务类型
            business_type_name: 业务类型名称
            amount: 支付金额
            description: 描述
            related_info: 相关信息

        Returns:
            支付结果
        """
        try:
            # 检查用户余额
            # 先获取用户的公司ID
            user_query = """
                SELECT company_id FROM internal_user
                WHERE uuid = :user_id AND status = '1'
            """
            user_result = await query_db.execute(text(user_query), {"user_id": user_id})
            user_row = user_result.fetchone()

            if not user_row:
                raise BusinessException(message="用户信息不存在")

            company_id = user_row._mapping.get("company_id")
            if not company_id:
                raise BusinessException(message="用户未关联公司")

            balance_query = """
                SELECT balance FROM company
                WHERE id = :company_id AND is_delete = '0'
            """
            balance_result = await query_db.execute(text(balance_query), {"company_id": company_id})
            balance_row = balance_result.fetchone()
            current_balance = balance_row._mapping.get("balance", 0) if balance_row else 0

            if current_balance < amount:
                raise BusinessException(message=f"余额不足，当前余额: {current_balance}元，需要支付: {amount}元")

            # 生成内部流水号
            transaction_no = cls._generate_transaction_no()
            logger.info(f"生成内部流水号: {transaction_no}")

            # 计算扣费后余额
            new_balance = current_balance - amount

            # 插入交易流水记录
            insert_sql = """
                INSERT INTO company_transaction (
                    company_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    related_order_no, pay_type, operator_id, operator_name,
                    description, remark, transaction_status, transaction_time,
                    created_by, created_at
                ) VALUES (
                    :company_uuid, :transaction_no, :business_type, :business_type_name,
                    :transaction_type, :amount, :balance_before, :balance_after,
                    :related_order_no, :pay_type, :operator_id, :operator_name,
                    :description, :remark, :transaction_status, NOW(),
                    :created_by, NOW()
                )
            """

            # 构建相关信息的JSON字符串
            related_info_json = json.dumps(related_info, ensure_ascii=False) if related_info else ""

            insert_params = {
                "company_uuid": company_id,
                "transaction_no": transaction_no,
                "business_type": business_type,
                "business_type_name": business_type_name,
                "transaction_type": 2,  # 2=支出
                "amount": amount,
                "balance_before": current_balance,
                "balance_after": new_balance,
                "related_order_no": transaction_no,  # 使用流水号作为订单号
                "pay_type": "BALANCE",
                "operator_id": user_id,
                "operator_name": user_name,
                "description": description,
                "remark": f"余额支付 - {description}, 相关信息: {related_info_json}",
                "transaction_status": "SUCCESS",  # 余额支付直接成功
                "created_by": user_id
            }

            await query_db.execute(text(insert_sql), insert_params)
            logger.info(f"插入交易流水记录完成")

            # 更新用户余额
            update_balance_sql = """
                UPDATE company
                SET balance = :new_balance
                WHERE id = :company_id AND is_delete = '0'
            """

            await query_db.execute(text(update_balance_sql), {
                "new_balance": new_balance,
                "company_id": company_id
            })
            logger.info(f"更新用户余额完成: {current_balance} -> {new_balance}")

            await query_db.commit()

            return {
                "transaction_no": transaction_no,
                "amount": amount,
                "balance_before": current_balance,
                "balance_after": new_balance,
                "status": "SUCCESS",
                "message": "支付成功"
            }

        except BusinessException:
            await query_db.rollback()
            raise
        except Exception as e:
            await query_db.rollback()
            logger.error(f"创建业务支付订单失败: {str(e)}")
            raise BusinessException(message=f"支付失败: {str(e)}")

    @classmethod
    def _generate_rsa_signature(cls, content: str, private_key_str: str) -> str:
        """生成RSA签名"""
        try:
            # 格式化私钥，每64个字符换行
            formatted_key = '\n'.join([private_key_str[i:i+64] for i in range(0, len(private_key_str), 64)])
            private_key_pem = f"-----BEGIN RSA PRIVATE KEY-----\n{formatted_key}\n-----END RSA PRIVATE KEY-----"

            # 加载私钥
            private_key = serialization.load_pem_private_key(
                private_key_pem.encode(),
                password=None
            )

            # 生成签名
            signature = private_key.sign(
                content.encode('utf-8'),
                padding.PKCS1v15(),
                hashes.SHA256()
            )

            # 返回base64编码的签名
            return base64.b64encode(signature).decode('utf-8')

        except Exception as e:
            logger.error(f"生成RSA签名失败: {str(e)}")
            raise BusinessException(message=f"生成签名失败: {str(e)}")

    @classmethod
    def _verify_rsa_signature(cls, content: str, signature: str, public_key_str: str) -> bool:
        """验证RSA签名"""
        try:
            # 格式化公钥，每64个字符换行
            formatted_key = '\n'.join([public_key_str[i:i+64] for i in range(0, len(public_key_str), 64)])
            public_key_pem = f"-----BEGIN PUBLIC KEY-----\n{formatted_key}\n-----END PUBLIC KEY-----"

            # 加载公钥
            public_key = serialization.load_pem_public_key(public_key_pem.encode())

            # 解码签名
            signature_bytes = base64.b64decode(signature)

            # 验证签名
            public_key.verify(
                signature_bytes,
                content.encode('utf-8'),
                padding.PKCS1v15(),
                hashes.SHA256()
            )

            return True

        except Exception as e:
            logger.error(f"验证RSA签名失败: {str(e)}")
            return False
    
    @classmethod
    async def create_yeepay_order_service(
        cls, 
        query_db: AsyncSession, 
        user_id: str, 
        amount: float, 
        request: Request
    ) -> Dict[str, Any]:
        """创建易宝支付订单服务
        
        Args:
            query_db: 数据库会话
            user_id: 用户ID
            amount: 充值金额
            request: 请求对象
            
        Returns:
            支付参数或支付链接
        """
        try:
            # 生成订单号
            order_number = cls._generate_order_number()

            # 获取用户openid
            user_openid = await cls._get_user_openid(query_db, user_id)
            if not user_openid:
                logger.error(f"用户 {user_id} 未绑定微信openid")
                raise BusinessException(message="用户未绑定微信，请先在小程序或公众号中绑定微信账号后再进行充值")

            # 构建易宝支付请求参数
            yeepay_request = cls._build_yeepay_request(
                order_number, amount, user_openid, request
            )

            # 调用易宝支付API
            payment_result = await cls._call_yeepay_api(yeepay_request)

            # 处理支付结果
            rsp_code = payment_result.get("rspCode")
            rsp_msg = payment_result.get("rspMsg")

            if rsp_code == "0000":
                # 支付订单创建成功，保存支付订单记录
                await cls._save_payment_order(query_db, user_id, order_number, amount, payment_result)

                # 提交事务
                await query_db.commit()

                result = {
                    "order_number": order_number,
                    "rspCode": rsp_code,
                    "rspMsg": rsp_msg
                }

                # 根据支付方式返回不同的参数
                if payment_result.get("payToken"):
                    # 小程序支付，返回支付参数
                    result["payToken"] = payment_result.get("payToken")
                elif payment_result.get("payUrl"):
                    # H5支付，返回支付链接
                    result["payUrl"] = payment_result.get("payUrl")

                return result
            else:
                # 支付订单创建失败，回滚事务
                await query_db.rollback()
                logger.error(f"易宝支付订单创建失败，响应码: {rsp_code}, 响应消息: {rsp_msg}")
                raise BusinessException(
                    message=payment_result.get("rspMsg", "创建支付订单失败")
                )
                
        except BusinessException as e:
            raise e
        except Exception as e:
            logger.error(f"创建易宝支付订单异常: {str(e)}")
            raise BusinessException(message=f"创建支付订单失败: {str(e)}")

    @classmethod
    async def create_yeepay_qrcode_order_service(
        cls,
        query_db: AsyncSession,
        user_id: str,
        amount: float,
        request: Request,
        is_order_payment: bool = False
    ) -> Dict[str, Any]:
        """创建易宝扫码支付订单服务

        Args:
            query_db: 数据库会话
            user_id: 用户ID
            amount: 充值金额
            request: 请求对象

        Returns:
            支付二维码URL
        """
        try:
            # 生成订单号
            order_number = cls._generate_order_number()

            # 构建易宝扫码支付请求参数
            yeepay_request = cls._build_yeepay_qrcode_request(
                order_number, amount, request
            )

            # 调用易宝扫码支付API
            payment_result = await cls._call_yeepay_qrcode_api(yeepay_request)

            # 处理支付结果
            rsp_code = payment_result.get("rspCode")
            rsp_msg = payment_result.get("rspMsg")

            if rsp_code == "0000":
                # 支付订单创建成功，保存支付订单记录
                await cls._save_payment_order(query_db, user_id, order_number, amount, payment_result, is_order_payment)

                # 提交事务
                await query_db.commit()

                result = {
                    "order_number": order_number,
                    "rspCode": rsp_code,
                    "rspMsg": rsp_msg
                }

                # 返回支付二维码URL
                if payment_result.get("payUrl"):
                    result["payUrl"] = payment_result.get("payUrl")
                    # 输出到控制台用于调试
                    logger.info(f"扫码支付URL: {payment_result.get('payUrl')}")

                return result
            else:
                # 支付订单创建失败，回滚事务
                await query_db.rollback()
                logger.error(f"易宝扫码支付订单创建失败，响应码: {rsp_code}, 响应消息: {rsp_msg}")
                raise BusinessException(
                    message=payment_result.get("rspMsg", "创建扫码支付订单失败")
                )

        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"创建易宝扫码支付订单失败: {str(e)}")
            raise BusinessException(message=f"扫码支付失败: {str(e)}")

    @classmethod
    async def handle_yeepay_notify_service(
        cls,
        query_db: AsyncSession,
        notify_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理易宝支付异步通知服务

        Args:
            query_db: 数据库会话
            notify_data: 易宝支付通知数据

        Returns:
            处理结果
        """
        try:
            # 验证通知数据格式
            if not isinstance(notify_data, dict):
                raise ValidationException(message="通知数据格式错误")

            # 验证签名（如果有配置公钥）
            notify_sign = notify_data.get("sign")
            notify_body = notify_data.get("body")

            if notify_sign and notify_body:
                platform_public_key = YeepayConfig.YEEPAY_PLATFORM_PUBLIC_KEY
                if platform_public_key:
                    # 验证通知签名
                    body_content = notify_body if isinstance(notify_body, str) else json.dumps(notify_body, ensure_ascii=False, separators=(',', ':'))
                    is_valid = cls._verify_rsa_signature(body_content, notify_sign, platform_public_key)
                    if not is_valid:
                        logger.error("易宝支付通知签名验证失败")
                        raise BusinessException(message="通知签名验证失败")

            # 解析通知内容
            if isinstance(notify_body, str):
                try:
                    body_data = json.loads(notify_body)
                except:
                    raise ValidationException(message="通知内容解析失败")
            else:
                body_data = notify_body or {}

            # 提取关键信息
            msg_public = body_data.get("msgPublic", {})
            msg_private = body_data.get("msgPrivate", {})

            order_number = msg_public.get("cusTraceNo")
            txn_state = msg_private.get("txnState")  # 交易状态：1=成功，4=失败
            sys_trace_no = msg_public.get("sysTraceNo")  # 易宝系统流水号

            if not order_number:
                raise ValidationException(message="订单号不能为空")

            # 查询支付订单
            payment_order = await cls._get_payment_order(query_db, order_number)
            if not payment_order:
                logger.error(f"支付订单不存在: {order_number}")
                raise BusinessException(message="支付订单不存在")

            # 检查订单状态，避免重复处理
            if payment_order.get("status") == "success":
                return {"status": "success", "message": "订单已处理"}

            # 根据支付状态处理
            if txn_state == "1":  # 支付成功
                # 更新用户余额
                await cls._update_user_balance(
                    query_db,
                    payment_order["user_id"],
                    payment_order["amount"],
                    order_number
                )

                # 更新支付订单状态
                await cls._update_payment_order_status(query_db, order_number, "success")

                return {"status": "success", "message": "支付成功处理完成"}

            elif txn_state == "4":  # 支付失败
                # 更新支付订单状态
                await cls._update_payment_order_status(query_db, order_number, "failed")

                return {"status": "success", "message": "支付失败处理完成"}

            else:
                # 其他状态（如处理中）
                return {"status": "success", "message": "支付状态处理中"}

        except BusinessException as e:
            logger.error(f"处理易宝支付通知业务异常: {str(e)}")
            raise e
        except ValidationException as e:
            logger.error(f"处理易宝支付通知验证异常: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"处理易宝支付通知系统异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"处理支付通知失败: {str(e)}")
    
    @classmethod
    async def get_user_balance_service(
        cls,
        query_db: AsyncSession,
        user_id: str
    ) -> Dict[str, Any]:
        """获取用户余额服务

        Args:
            query_db: 数据库会话
            user_id: 用户ID

        Returns:
            用户余额信息
        """
        try:
            # 查询用户所属公司的余额信息（使用新的余额结构）
            balance_query = """
                SELECT
                    c.balance as available,
                    COALESCE(c.frozen_amount, 0) as frozen,
                    (c.balance + COALESCE(c.frozen_amount, 0)) as total,
                    c.name as company_name,
                    iu.name as user_name,
                    iu.mobile
                FROM internal_user iu
                JOIN company c ON iu.company_id = c.id
                WHERE iu.uuid = :user_id AND iu.status = '1' AND c.is_delete = '0'
            """

            result = await query_db.execute(text(balance_query), {"user_id": user_id})
            row = result.fetchone()

            if row:
                available = float(row._mapping.get('available', 0))
                frozen = float(row._mapping.get('frozen', 0))
                total = float(row._mapping.get('total', 0))

                return {
                    "available": f"{available:.2f}",
                    "frozen": f"{frozen:.2f}",
                    "total": f"{total:.2f}",
                    "company_name": row._mapping.get('company_name', ''),
                    "user_name": row._mapping.get('user_name', ''),
                    "mobile": row._mapping.get('mobile', '')
                }
            else:
                # 如果没有用户记录，抛出异常
                raise BusinessException(message="用户不存在或用户未关联公司，无法获取余额信息")

        except Exception as e:
            logger.error(f"获取用户余额异常: {str(e)}")
            raise BusinessException(message=f"获取用户余额失败: {str(e)}")
    
    @classmethod
    async def get_balance_flow_service(
        cls,
        query_db: AsyncSession,
        user_id: str,
        start_date: str = None,
        end_date: str = None,
        flow_type: str = None
    ) -> List[Dict[str, Any]]:
        """获取余额流水服务
        
        Args:
            query_db: 数据库会话
            user_id: 用户ID
            start_date: 开始日期
            end_date: 结束日期
            flow_type: 流水类型
            
        Returns:
            余额流水列表
        """
        try:
            # 先获取用户的公司ID
            company_query = """
                SELECT company_id FROM internal_user
                WHERE uuid = :user_id AND status = '1'
            """

            company_result = await query_db.execute(text(company_query), {"user_id": user_id})
            company_row = company_result.fetchone()

            if not company_row:
                return []

            company_id = company_row._mapping.get("company_id")

            # 构建查询SQL - 使用company_transaction表
            flow_query = """
                SELECT
                    id,
                    transaction_no,
                    business_type_name,
                    transaction_type,
                    amount,
                    balance_before,
                    balance_after,
                    description,
                    related_order_no,
                    pay_type,
                    transaction_time,
                    transaction_status,
                    operator_name
                FROM company_transaction
                WHERE company_uuid = :company_id
            """

            params = {"company_id": company_id}

            # 添加日期过滤
            if start_date:
                flow_query += " AND DATE(transaction_time) >= :start_date"
                params["start_date"] = start_date

            if end_date:
                flow_query += " AND DATE(transaction_time) <= :end_date"
                params["end_date"] = end_date

            # 添加类型过滤 (transaction_type: 1=收入, 2=支出)
            if flow_type and flow_type != "all":
                if flow_type == "income":
                    flow_query += " AND transaction_type = 1"
                elif flow_type == "expense":
                    flow_query += " AND transaction_type = 2"

            flow_query += " ORDER BY transaction_time DESC LIMIT 100"

            result = await query_db.execute(text(flow_query), params)
            rows = result.fetchall()

            flow_list = []
            for row in rows:
                amount = row._mapping.get('amount', 0)
                transaction_type = row._mapping.get('transaction_type', 1)

                flow_list.append({
                    "id": row._mapping.get("id"),
                    "amount": f"{amount:.2f}",
                    "type": "income" if transaction_type == 1 else "expense",
                    "description": row._mapping.get("description", ""),
                    "business_type": row._mapping.get("business_type_name", ""),
                    "date": row._mapping.get("transaction_time").strftime("%Y-%m-%d %H:%M:%S") if row._mapping.get("transaction_time") else "",
                    "order_number": row._mapping.get("related_order_no", ""),
                    "transaction_no": row._mapping.get("transaction_no", ""),
                    "operator": row._mapping.get("operator_name", ""),
                    "pay_type": row._mapping.get("pay_type", "")
                })

            return flow_list
            
        except Exception as e:
            logger.error(f"获取余额流水异常: {str(e)}")
            raise BusinessException(message=f"获取余额流水失败: {str(e)}")
    
    @classmethod
    def _generate_order_number(cls) -> str:
        """生成订单号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_str = str(uuid.uuid4()).replace("-", "")[:6]
        return f"RC{timestamp}{random_str}"
    
    @classmethod
    async def _get_user_openid(cls, query_db: AsyncSession, user_id: str) -> str:
        """获取用户openid"""
        try:
            # 从internal_user表查询微信openid，优先小程序openid，其次公众号openid
            openid_query = """
                SELECT
                    wx_openid,
                    wx_official_openid,
                    name,
                    mobile
                FROM internal_user
                WHERE uuid = :user_id AND status = '1'
            """

            result = await query_db.execute(text(openid_query), {"user_id": user_id})
            row = result.fetchone()

            if row:
                user_name = row._mapping.get("name", "")
                mobile = row._mapping.get("mobile", "")
                wx_openid = row._mapping.get("wx_openid")
                wx_official_openid = row._mapping.get("wx_official_openid")

                # 优先使用小程序openid
                if wx_openid and wx_openid.strip():
                    return wx_openid.strip()

                # 如果没有小程序openid，使用公众号openid
                if wx_official_openid and wx_official_openid.strip():
                    return wx_official_openid.strip()

                # 如果都没有，记录用户信息用于调试
                logger.warning(f"用户 {user_id}({user_name}, {mobile}) 未绑定任何微信openid")
                return ""
            else:
                logger.error(f"用户 {user_id} 不存在或已禁用")
                return ""

        except Exception as e:
            logger.error(f"获取用户openid异常: {str(e)}")
            return ""

    @classmethod
    def _build_yeepay_request(
        cls,
        order_number: str,
        amount: float,
        user_openid: str,
        request: Request
    ) -> Dict[str, Any]:
        """构建易宝支付请求参数"""
        try:
            # 转换金额为分
            amount_cents = int(amount * 100)

            # 生成请求时间（使用本地时间，确保与易宝支付时区一致）
            now = datetime.now()
            req_time = now.strftime("%Y%m%d%H%M%S")

            # 记录时间信息用于调试
            logger.info(f"生成支付请求时间: {req_time}, 本地时间: {now}, 时区: {time.tzname}")

            # 生成过期时间（30分钟后）
            expire_time = (now + timedelta(minutes=30)).strftime("%Y%m%d%H%M%S")

            # 构建请求参数（按照官方文档格式）
            yeepay_request = {
                "msgPublic": {
                    "version": YeepayConfig.YEEPAY_VERSION,
                    "cusReqTime": req_time,
                    "cusTraceNo": order_number,
                    "cusCode": YeepayConfig.YEEPAY_CUSTOMER_CODE
                },
                "msgPrivate": {
                    "txnAmt": amount_cents,  # Integer 类型，单位：分
                    "currency": "CNY",  # 人民币
                    "payer": {
                        "sn": user_openid  # 小程序支付必填 openid
                    },
                    "bsnInfo": {
                        "subject": "余额充值",
                        "sn": order_number,  # 订单编号
                        "amt": amount_cents,  # 订单金额，单位：分
                        "expire": expire_time
                    },
                    "scene": {
                        "ip": cls._get_client_ip(request),
                        "type": "miniapp",  # 小程序支付场景
                        "code": "online",  # 线上场景
                        "name": "金刚到家"  # 应用名称
                    },
                    "txnRemark": "余额充值",
                    "notifyUrl": f"{request.base_url}prod-api/api/v1/payment/yeepay/notify",
                    "completeUrl": ""
                }
            }

            return yeepay_request

        except Exception as e:
            logger.error(f"构建易宝支付请求参数异常: {str(e)}")
            raise BusinessException(message=f"构建支付请求失败: {str(e)}")

    @classmethod
    def _build_yeepay_qrcode_request(
        cls,
        order_number: str,
        amount: float,
        request: Request
    ) -> Dict[str, Any]:
        """构建易宝扫码支付请求参数"""
        try:
            # 转换金额为分
            amount_cents = int(amount * 100)

            # 生成请求时间（使用本地时间，确保与易宝支付时区一致）
            now = datetime.now()
            req_time = now.strftime("%Y%m%d%H%M%S")

            # 记录时间信息用于调试
            logger.info(f"生成扫码支付请求时间: {req_time}, 本地时间: {now}, 时区: {time.tzname}")

            # 生成过期时间（30分钟后）
            expire_time = (now + timedelta(minutes=30)).strftime("%Y%m%d%H%M%S")

            # 构建请求参数（按照官方文档格式）
            yeepay_request = {
                "msgPublic": {
                    "version": YeepayConfig.YEEPAY_VERSION,
                    "cusReqTime": req_time,
                    "cusTraceNo": order_number,
                    "cusCode": YeepayConfig.YEEPAY_CUSTOMER_CODE
                },
                "msgPrivate": {
                    "txnAmt": amount_cents,  # Integer 类型，单位：分
                    "currency": "CNY",  # 人民币
                    "bsnInfo": {
                        "subject": "余额充值",
                        "sn": order_number,  # 订单编号
                        "amt": amount_cents,  # 订单金额，单位：分
                        "expire": expire_time
                    },
                    "scene": {
                        "ip": cls._get_client_ip(request),
                        "type": "native",  # 扫码支付场景
                        "code": "online",  # 线上场景
                        "name": "金刚到家"  # 应用名称
                    },
                    "txnRemark": "余额充值",
                    "notifyUrl": f"{request.base_url}prod-api/api/v1/payment/yeepay/notify",
                    "completeUrl": ""
                }
            }

            return yeepay_request

        except Exception as e:
            logger.error(f"构建易宝扫码支付请求参数异常: {str(e)}")
            raise BusinessException(message=f"构建扫码支付请求失败: {str(e)}")

    @classmethod
    async def _call_yeepay_api(cls, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """调用易宝支付API"""
        try:
            # 构建完整请求体
            request_body = {"body": request_data}

            # 生成签名
            merchant_private_key = YeepayConfig.YEEPAY_MERCHANT_PRIVATE_KEY
            logger.info(f"私钥内容: {merchant_private_key}")
            if not merchant_private_key:
                raise BusinessException(message="易宝支付商户私钥未配置")

            # 将body转换为JSON字符串进行签名（与PHP相同的方式）
            body_json = json.dumps(request_body["body"], ensure_ascii=False, separators=(',', ':'), sort_keys=False)
            logger.info(f"签名内容: {body_json}")

            # 生成签名
            signature = cls._generate_rsa_signature(body_json, merchant_private_key)
            logger.info(f"生成的签名: {signature}")

            # 添加签名到请求体
            request_body["sign"] = signature
            logger.info(f"完整请求体（含签名）: {request_body}")

            async with httpx.AsyncClient(timeout=YeepayConfig.YEEPAY_TIMEOUT) as client:
                response = await client.post(
                    YeepayConfig.YEEPAY_API_URL,
                    json=request_body,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"易宝支付API HTTP状态码: {response.status_code}")
                logger.info(f"易宝支付API响应头: {dict(response.headers)}")

                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"易宝支付API原始响应: {result}")

                    # 验证响应签名
                    response_sign = result.get("sign")
                    response_body = result.get("body")

                    if response_sign and response_body:
                        # 验证签名
                        platform_public_key = YeepayConfig.YEEPAY_PLATFORM_PUBLIC_KEY
                        if platform_public_key and platform_public_key.strip():
                            logger.info("开始验证易宝支付响应签名...")
                            is_valid = cls._verify_rsa_signature(response_body, response_sign, platform_public_key)
                            if not is_valid:
                                logger.error("易宝支付响应签名验证失败")
                                # 在生产环境中应该抛出异常，但在开发阶段可以跳过验证
                                logger.warning("⚠️ 开发环境跳过签名验证，生产环境请配置正确的平台公钥")
                                # raise BusinessException(message="响应签名验证失败")
                            else:
                                logger.info("✅ 易宝支付响应签名验证成功")
                        else:
                            logger.warning("⚠️ 未配置平台公钥，跳过响应签名验证")
                    elif response_sign is None:
                        logger.warning("响应中没有签名，可能是错误响应")
                        # 如果没有签名，尝试解析错误信息
                        if isinstance(response_body, str):
                            try:
                                body_data = json.loads(response_body)
                                if body_data.get("msgPublic", {}).get("rspMsg"):
                                    error_msg = body_data["msgPublic"]["rspMsg"]
                                    logger.error(f"易宝支付返回错误: {error_msg}")
                                    raise BusinessException(message=f"易宝支付错误: {error_msg}")
                            except:
                                pass

                    # 解析响应数据
                    if isinstance(response_body, str):
                        try:
                            body_data = json.loads(response_body)
                        except:
                            body_data = {}
                    else:
                        body_data = response_body or {}

                    if body_data.get("msgPublic"):
                        msg_public = body_data["msgPublic"]
                        msg_private = body_data.get("msgPrivate", {})

                        parsed_result = {
                            "rspCode": msg_public.get("rspCode"),
                            "rspMsg": msg_public.get("rspMsg"),
                            "payToken": msg_private.get("payToken"),
                            "payUrl": msg_private.get("payUrl"),
                            "sysTraceNo": msg_public.get("sysTraceNo")
                        }
                        logger.info(f"解析后的响应数据: {parsed_result}")
                        return parsed_result
                    else:
                        logger.error(f"易宝支付API响应格式错误，缺少必要字段: {result}")
                        raise BusinessException(message="易宝支付API响应格式错误")
                else:
                    response_text = response.text
                    logger.error(f"易宝支付API HTTP错误，状态码: {response.status_code}, 响应内容: {response_text}")
                    raise BusinessException(message=f"易宝支付API调用失败: {response.status_code}")

        except httpx.TimeoutException as e:
            logger.error(f"=== 易宝支付API调用超时 ===")
            logger.error(f"超时异常详情: {str(e)}")
            raise BusinessException(message="支付服务超时，请重试")
        except httpx.RequestError as e:
            logger.error(f"=== 易宝支付API请求错误 ===")
            logger.error(f"请求错误详情: {str(e)}")
            raise BusinessException(message=f"支付服务请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"=== 易宝支付API调用异常 ===")
            logger.error(f"异常类型: {type(e).__name__}")
            logger.error(f"异常详情: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"调用支付服务失败: {str(e)}")

    @classmethod
    async def _call_yeepay_qrcode_api(cls, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """调用易宝扫码支付API"""
        try:
            # 构建完整请求体
            request_body = {"body": request_data}

            # 生成签名
            merchant_private_key = YeepayConfig.YEEPAY_MERCHANT_PRIVATE_KEY
            if not merchant_private_key:
                raise BusinessException(message="易宝支付商户私钥未配置")

            # 将body转换为JSON字符串进行签名
            body_json = json.dumps(request_body["body"], ensure_ascii=False, separators=(',', ':'), sort_keys=False)
            logger.info(f"扫码支付签名内容: {body_json}")

            # 生成签名
            signature = cls._generate_rsa_signature(body_json, merchant_private_key)
            logger.info(f"扫码支付生成的签名: {signature}")

            # 添加签名到请求体
            request_body["sign"] = signature
            logger.info(f"扫码支付完整请求体（含签名）: {request_body}")

            # 调用扫码支付API
            qrcode_api_url = YeepayConfig.YEEPAY_API_URL.replace('/miniapp', '/native')

            async with httpx.AsyncClient(timeout=YeepayConfig.YEEPAY_TIMEOUT) as client:
                response = await client.post(
                    qrcode_api_url,
                    json=request_body,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"易宝扫码支付API HTTP状态码: {response.status_code}")
                logger.info(f"易宝扫码支付API响应头: {dict(response.headers)}")

                if response.status_code != 200:
                    logger.error(f"易宝扫码支付API HTTP错误: {response.status_code}")
                    raise BusinessException(message=f"扫码支付接口调用失败，HTTP状态码: {response.status_code}")

                # 解析响应
                response_text = response.text
                logger.info(f"易宝扫码支付API原始响应: {response_text}")

                try:
                    response_data = response.json()
                except json.JSONDecodeError as e:
                    logger.error(f"易宝扫码支付API响应JSON解析失败: {str(e)}")
                    raise BusinessException(message="扫码支付接口响应格式错误")

                logger.info(f"易宝扫码支付API解析后响应: {response_data}")

                # 解析响应数据
                response_body = response_data.get("body")
                if isinstance(response_body, str):
                    try:
                        body_data = json.loads(response_body)
                    except:
                        body_data = {}
                else:
                    body_data = response_body or {}

                if body_data.get("msgPublic"):
                    msg_public = body_data["msgPublic"]
                    msg_private = body_data.get("msgPrivate", {})

                    result = {
                        "rspCode": msg_public.get("rspCode"),
                        "rspMsg": msg_public.get("rspMsg"),
                        "sysTraceNo": msg_public.get("sysTraceNo"),
                        "cusTraceNo": msg_public.get("cusTraceNo")
                    }

                    # 添加扫码支付特有的payUrl
                    if msg_private.get("payUrl"):
                        result["payUrl"] = msg_private.get("payUrl")

                    return result
                else:
                    logger.error("易宝扫码支付API响应格式错误，缺少msgPublic")
                    raise BusinessException(message="扫码支付接口响应格式错误")

        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"调用易宝扫码支付API异常: {str(e)}")
            raise BusinessException(message=f"调用扫码支付接口失败: {str(e)}")

    @classmethod
    def _get_client_ip(cls, request: Request) -> str:
        """获取客户端IP"""
        try:
            # 尝试从X-Forwarded-For头获取真实IP
            forwarded_for = request.headers.get("X-Forwarded-For")
            if forwarded_for:
                return forwarded_for.split(",")[0].strip()

            # 尝试从X-Real-IP头获取
            real_ip = request.headers.get("X-Real-IP")
            if real_ip:
                return real_ip

            # 使用客户端IP
            return request.client.host if request.client else "127.0.0.1"
        except Exception:
            return "127.0.0.1"

    @classmethod
    async def _save_payment_order(
        cls,
        query_db: AsyncSession,
        user_id: str,
        order_number: str,
        amount: float,
        payment_result: Dict[str, Any],
        is_order_payment: bool = False
    ):
        """保存充值订单记录到公司流水表"""
        try:
            logger.info(f"=== 开始保存支付订单记录 ===")
            logger.info(f"用户ID: {user_id}, 订单号: {order_number}, 金额: {amount}, 是否订单支付: {is_order_payment}")
            logger.info(f"支付结果: {payment_result}")

            # 如果是订单扫码支付，不创建company_transaction记录
            if is_order_payment:
                logger.info(f"订单扫码支付，跳过创建company_transaction记录")
                return

            # 检查订单是否已存在，避免重复插入
            existing_order = await cls._get_payment_order(query_db, order_number)
            if existing_order:
                logger.info(f"订单 {order_number} 已存在，跳过重复保存")
                return

            # 获取用户信息和公司ID
            user_query = """
                SELECT company_id, name FROM internal_user
                WHERE uuid = :user_id AND status = '1'
            """
            logger.info(f"查询用户信息SQL: {user_query}")

            user_result = await query_db.execute(text(user_query), {"user_id": user_id})
            user_row = user_result.fetchone()
            logger.info(f"用户查询结果: {user_row._mapping if user_row else None}")

            if not user_row:
                logger.error(f"用户 {user_id} 不存在或已禁用")
                raise BusinessException(message="用户不存在或已禁用")

            company_id = user_row._mapping.get("company_id")
            user_name = user_row._mapping.get("name")
            logger.info(f"用户信息 - 公司ID: {company_id}, 用户名: {user_name}")

            # 从支付结果中获取易宝返回的交易流水号
            yeepay_transaction_id = payment_result.get('sysTraceNo', '')
            logger.info(f"易宝交易流水号: {yeepay_transaction_id}")

            # 生成内部流水号
            transaction_no = cls._generate_transaction_no()
            logger.info(f"生成内部流水号: {transaction_no}")

            # 获取当前公司余额
            balance_query = """
                SELECT balance FROM company
                WHERE id = :company_id AND is_delete = '0'
            """
            balance_result = await query_db.execute(text(balance_query), {"company_id": company_id})
            balance_row = balance_result.fetchone()
            current_balance = balance_row._mapping.get("balance", 0) if balance_row else 0

            # 插入公司流水记录（待支付状态）
            insert_sql = """
                INSERT INTO company_transaction (
                    company_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    related_order_no, pay_type, external_transaction_id,
                    operator_id, operator_name, description, remark,
                    transaction_status, transaction_time, created_by, created_at
                ) VALUES (
                    :company_uuid, :transaction_no, :business_type, :business_type_name,
                    :transaction_type, :amount, :balance_before, :balance_after,
                    :related_order_no, :pay_type, :external_transaction_id,
                    :operator_id, :operator_name, :description, :remark,
                    :transaction_status, NOW(), :created_by, NOW()
                )
            """

            insert_params = {
                "company_uuid": company_id,
                "transaction_no": transaction_no,
                "business_type": "RECHARGE",
                "business_type_name": "余额充值",
                "transaction_type": 1,  # 1=收入
                "amount": amount,
                "balance_before": current_balance,  # 当前余额
                "balance_after": current_balance,   # 待支付时余额不变
                "related_order_no": order_number,
                "pay_type": "yeepay",
                "external_transaction_id": yeepay_transaction_id,
                "operator_id": user_id,
                "operator_name": user_name,
                "description": f"易宝支付充值，订单号: {order_number}",
                "remark": f"状态: 待支付, 易宝流水号: {yeepay_transaction_id}",
                "transaction_status": "PENDING",  # 待支付状态
                "created_by": user_id
            }
            logger.info(f"插入数据库参数: {insert_params}")

            await query_db.execute(text(insert_sql), insert_params)
            logger.info(f"数据库插入执行完成")

            await query_db.commit()
            logger.info(f"✅ 保存充值订单成功 - 订单号: {order_number}, 易宝流水号: {yeepay_transaction_id}")

        except Exception as e:
            await query_db.rollback()
            logger.error(f"❌ 保存充值订单异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"保存充值订单失败: {str(e)}")

    @classmethod
    async def _get_payment_order(cls, query_db: AsyncSession, order_number: str) -> Optional[Dict[str, Any]]:
        """获取支付订单记录（支持普通充值和订单扫码支付）"""
        try:
            # 先查询company_transaction表（普通充值）
            company_query_sql = """
                SELECT
                    related_order_no,
                    operator_id,
                    amount,
                    remark,
                    pay_type,
                    external_transaction_id
                FROM company_transaction
                WHERE related_order_no = :order_number
                AND business_type = 'RECHARGE'
            """

            result = await query_db.execute(text(company_query_sql), {"order_number": order_number})
            row = result.fetchone()

            if row:
                # 这是普通充值订单，从remark中判断状态
                remark = row._mapping.get("remark", "")
                status = "pending" if "待支付" in remark else "success"

                return {
                    "order_number": row._mapping.get("related_order_no"),
                    "user_id": row._mapping.get("operator_id"),
                    "amount": row._mapping.get("amount"),
                    "status": status,
                    "payment_method": row._mapping.get("pay_type"),
                    "external_transaction_id": row._mapping.get("external_transaction_id"),
                    "payment_type": "company_recharge"  # 标识为公司充值
                }

            # 如果company_transaction表中没有找到，查询ccuser_transaction表（订单扫码支付）
            ccuser_query_sql = """
                SELECT
                    external_transaction_id,
                    ccuser_uuid,
                    amount,
                    remark,
                    pay_type,
                    related_order_no
                FROM ccuser_transaction
                WHERE external_transaction_id = :order_number
                AND business_type = 'ORDER_PAYMENT_RELATION'
            """

            ccuser_result = await query_db.execute(text(ccuser_query_sql), {"order_number": order_number})
            ccuser_row = ccuser_result.fetchone()

            if ccuser_row:
                # 这是订单扫码支付的关联记录
                remark = ccuser_row._mapping.get("remark", "")

                # 从备注中解析原订单号和用户信息
                original_order_number = ""
                user_id = ""
                amount = 0

                if "原订单号:" in remark:
                    try:
                        original_order_number = remark.split("原订单号:")[1].split(",")[0]
                    except:
                        pass

                if "用户ID:" in remark:
                    try:
                        user_id = remark.split("用户ID:")[1].split(",")[0]
                    except:
                        pass

                if "金额:" in remark:
                    try:
                        amount = float(remark.split("金额:")[1])
                    except:
                        amount = 0

                return {
                    "order_number": order_number,  # 支付订单号
                    "original_order_number": original_order_number,  # 原订单号
                    "user_id": user_id,
                    "amount": amount,
                    "status": "pending",  # 订单扫码支付默认为待支付状态
                    "pay_type": "WECHAT_QRCODE",
                    "external_transaction_id": order_number,
                    "payment_type": "order_qrcode"  # 标识为订单扫码支付
                }

            # 两个表都没有找到记录
            return None

        except Exception as e:
            logger.error(f"获取充值订单异常: {str(e)}")
            raise BusinessException(message=f"获取充值订单失败: {str(e)}")

    @classmethod
    async def _update_payment_order_status(
        cls,
        query_db: AsyncSession,
        order_number: str,
        status: str
    ):
        """更新充值订单状态"""
        try:
            # 如果是支付成功，跳过备注更新（因为_update_user_balance已经处理了）
            if status == "success":
                logger.info(f"支付成功状态，跳过备注更新（已在_update_user_balance中处理）: {order_number}")
                return

            # 更新公司流水记录的状态和备注
            status_text = "支付失败" if status == "failed" else "待支付"
            transaction_status = "FAILED" if status == "failed" else "PENDING"

            update_sql = """
                UPDATE company_transaction
                SET transaction_status = :transaction_status,
                    remark = CONCAT(
                        SUBSTRING_INDEX(remark, '状态:', 1),
                        '状态: ', :status_text,
                        CASE
                            WHEN LOCATE('易宝流水号:', remark) > 0
                            THEN CONCAT(', ', SUBSTRING(remark, LOCATE('易宝流水号:', remark)))
                            ELSE ''
                        END
                    )
                WHERE related_order_no = :order_number
                AND business_type = 'RECHARGE'
                AND transaction_status != 'SUCCESS'
            """

            await query_db.execute(text(update_sql), {
                "order_number": order_number,
                "status_text": status_text,
                "transaction_status": transaction_status
            })

            await query_db.commit()
            logger.info(f"更新充值订单状态成功: {order_number} -> {status}")

        except Exception as e:
            await query_db.rollback()
            logger.error(f"更新充值订单状态异常: {str(e)}")
            raise BusinessException(message=f"更新充值订单状态失败: {str(e)}")

    @classmethod
    async def _update_user_balance(
        cls,
        query_db: AsyncSession,
        user_id: str,
        amount: float,
        order_number: str
    ):
        """更新公司余额"""
        try:
            logger.info(f"=== 开始更新用户余额 ===")
            logger.info(f"用户ID: {user_id}, 充值金额: {amount}, 订单号: {order_number}")

            # 获取用户信息和公司ID
            user_query = """
                SELECT company_id, name FROM internal_user
                WHERE uuid = :user_id AND status = '1'
            """

            user_result = await query_db.execute(text(user_query), {"user_id": user_id})
            user_row = user_result.fetchone()

            if not user_row:
                logger.error(f"用户 {user_id} 不存在或已禁用")
                raise BusinessException(message="用户不存在或已禁用")

            company_id = user_row._mapping.get("company_id")
            user_name = user_row._mapping.get("name")
            logger.info(f"用户信息 - 公司ID: {company_id}, 用户名: {user_name}")

            # 查询当前公司余额
            balance_query = """
                SELECT balance FROM company
                WHERE id = :company_id AND is_delete = '0'
            """

            balance_result = await query_db.execute(text(balance_query), {"company_id": company_id})
            balance_row = balance_result.fetchone()

            if not balance_row:
                logger.error(f"公司 {company_id} 不存在")
                raise BusinessException(message="公司不存在")

            current_balance = balance_row._mapping.get("balance", 0)
            new_balance = current_balance + amount
            logger.info(f"余额变更 - 当前余额: {current_balance}, 充值金额: {amount}, 新余额: {new_balance}")

            # 更新公司余额
            update_sql = """
                UPDATE company
                SET balance = :new_balance, update_time = NOW()
                WHERE id = :company_id
            """

            await query_db.execute(text(update_sql), {
                "company_id": company_id,
                "new_balance": new_balance
            })
            logger.info(f"公司余额更新完成")

            # 更新原有的待支付记录，将其标记为成功并更新余额信息
            update_transaction_sql = """
                UPDATE company_transaction
                SET
                    transaction_status = 'SUCCESS',
                    balance_before = :current_balance,
                    balance_after = :new_balance,
                    remark = CONCAT(
                        SUBSTRING_INDEX(remark, '状态:', 1),
                        '状态: 支付成功',
                        CASE
                            WHEN LOCATE('易宝流水号:', remark) > 0
                            THEN CONCAT(', ', SUBSTRING(remark, LOCATE('易宝流水号:', remark)))
                            ELSE ''
                        END
                    )
                WHERE related_order_no = :order_number
                AND business_type = 'RECHARGE'
                AND transaction_status = 'PENDING'
            """

            await query_db.execute(text(update_transaction_sql), {
                "order_number": order_number,
                "current_balance": current_balance,
                "new_balance": new_balance
            })
            logger.info(f"更新原有流水记录完成")

            await query_db.commit()
            logger.info(f"✅ 更新公司余额成功: {company_id}, 充值金额: {amount}, 新余额: {new_balance}")

        except Exception as e:
            await query_db.rollback()
            logger.error(f"❌ 更新公司余额异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"更新公司余额失败: {str(e)}")

    @classmethod
    def _generate_transaction_no(cls) -> str:
        """生成流水号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_str = str(uuid.uuid4()).replace("-", "")[:6]
        return f"TXN{timestamp}{random_str}"

    @classmethod
    async def create_company_transaction_record(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        business_type: str,
        business_type_name: str,
        transaction_type: int,
        amount: float,
        balance_before: float,
        balance_after: float,
        related_order_no: str = "",
        pay_type: str = "",
        external_transaction_id: str = "",
        operator_id: str = "",
        operator_name: str = "",
        description: str = "",
        remark: str = "",
        transaction_status: str = "SUCCESS"
    ) -> str:
        """
        统一创建公司交易流水记录

        Args:
            query_db: 数据库会话
            company_uuid: 公司UUID
            business_type: 业务类型（如WITHDRAWAL、RECHARGE、PAYMENT等）
            business_type_name: 业务类型名称
            transaction_type: 交易类型（1=收入，2=支出）
            amount: 交易金额
            balance_before: 交易前余额
            balance_after: 交易后余额
            related_order_no: 关联订单号
            pay_type: 支付方式
            external_transaction_id: 外部交易ID
            operator_id: 操作人ID
            operator_name: 操作人姓名
            description: 描述
            remark: 备注
            transaction_status: 交易状态

        Returns:
            生成的交易流水号
        """
        try:
            # 内部生成交易流水号，确保唯一性
            transaction_no = cls._generate_transaction_no()

            # 插入交易流水记录
            insert_sql = text("""
                INSERT INTO company_transaction (
                    company_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    related_order_no, pay_type, external_transaction_id,
                    operator_id, operator_name, description, remark,
                    transaction_status, transaction_time, created_by, created_at
                ) VALUES (
                    :company_uuid, :transaction_no, :business_type, :business_type_name,
                    :transaction_type, :amount, :balance_before, :balance_after,
                    :related_order_no, :pay_type, :external_transaction_id,
                    :operator_id, :operator_name, :description, :remark,
                    :transaction_status, NOW(), :created_by, NOW()
                )
            """)

            await query_db.execute(insert_sql, {
                "company_uuid": company_uuid,
                "transaction_no": transaction_no,
                "business_type": business_type,
                "business_type_name": business_type_name,
                "transaction_type": transaction_type,
                "amount": amount,
                "balance_before": balance_before,
                "balance_after": balance_after,
                "related_order_no": related_order_no,
                "pay_type": pay_type,
                "external_transaction_id": external_transaction_id,
                "operator_id": operator_id,
                "operator_name": operator_name,
                "description": description,
                "remark": remark,
                "transaction_status": transaction_status,
                "created_by": operator_id
            })

            logger.info(f"✅ 创建交易流水记录成功: {transaction_no}, 业务类型: {business_type}, 金额: {amount}")
            return transaction_no

        except Exception as e:
            logger.error(f"❌ 创建交易流水记录失败: {str(e)}")
            raise BusinessException(message=f"创建交易流水记录失败: {str(e)}")

    @classmethod
    async def _create_withdrawal_transaction_records(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        withdrawal_no: str,
        withdrawal_type: int,
        apply_amount: float,
        fee_amount: float,
        available_balance: float,
        new_available_balance: float,
        user_id: str,
        user_name: str
    ) -> None:
        """创建提现相关的交易流水记录

        创建两条交易流水：
        1. 提现申请流水（扣除申请金额）
        2. 代扣个税流水（如果存在手续费）

        体现先后扣除余额，添加冻结金额的效果
        """
        try:
            withdrawal_type_name = "自行开票" if withdrawal_type == 1 else "零工提现"

            # 计算实际到账金额
            actual_amount = apply_amount - fee_amount

            if fee_amount > 0:
                # 有手续费的情况：先后扣除两笔金额

                # 第一条流水：扣除实际提现金额
                balance_after_withdrawal = available_balance - actual_amount

                withdrawal_description = f"提现申请：{withdrawal_type_name}，提现金额{actual_amount:.2f}元"
                withdrawal_remark = f"提现申请单号：{withdrawal_no}，扣除提现金额"

                withdrawal_transaction_no = await cls.create_company_transaction_record(
                    query_db=query_db,
                    company_uuid=company_uuid,
                    business_type="WITHDRAWAL",
                    business_type_name="提现申请",
                    transaction_type=2,  # 2=支出
                    amount=actual_amount,  # 记录实际提现金额
                    balance_before=available_balance,
                    balance_after=balance_after_withdrawal,
                    related_order_no=withdrawal_no,
                    pay_type="WITHDRAWAL",
                    operator_id=user_id,
                    operator_name=user_name,
                    description=withdrawal_description,
                    remark=withdrawal_remark,
                    transaction_status="PENDING"  # 未处理状态
                )

                logger.info(f"✅ 创建提现申请交易记录成功: {withdrawal_transaction_no}, 提现金额: {actual_amount:.2f}元")

                # 第二条流水：扣除代扣个税
                balance_after_tax = balance_after_withdrawal - fee_amount

                tax_description = f"代扣个税：{withdrawal_type_name}，代扣金额{fee_amount:.2f}元"
                tax_remark = f"提现申请单号：{withdrawal_no}，代扣个人所得税"

                tax_transaction_no = await cls.create_company_transaction_record(
                    query_db=query_db,
                    company_uuid=company_uuid,
                    business_type="TAX_DEDUCTION",
                    business_type_name="代扣个税",
                    transaction_type=2,  # 2=支出
                    amount=fee_amount,  # 记录代扣个税金额
                    balance_before=balance_after_withdrawal,  # 从扣除提现金额后的余额开始
                    balance_after=balance_after_tax,
                    related_order_no=withdrawal_no,
                    pay_type="TAX_DEDUCTION",
                    operator_id=user_id,
                    operator_name=user_name,
                    description=tax_description,
                    remark=tax_remark,
                    transaction_status="PENDING"  # 未处理状态
                )

                logger.info(f"✅ 创建代扣个税交易记录成功: {tax_transaction_no}, 代扣金额: {fee_amount:.2f}元")
                logger.info(f"📊 余额变化: {available_balance:.2f} → {balance_after_withdrawal:.2f} → {balance_after_tax:.2f}")
                logger.info(f"💰 实际到账金额: {actual_amount:.2f}元，代扣个税: {fee_amount:.2f}元")

            else:
                # 无手续费的情况：只扣除申请金额
                withdrawal_description = f"提现申请：{withdrawal_type_name}，申请金额{apply_amount:.2f}元"
                withdrawal_remark = f"提现申请单号：{withdrawal_no}，申请提现"

                withdrawal_transaction_no = await cls.create_company_transaction_record(
                    query_db=query_db,
                    company_uuid=company_uuid,
                    business_type="WITHDRAWAL",
                    business_type_name="提现申请",
                    transaction_type=2,  # 2=支出
                    amount=apply_amount,  # 记录申请金额
                    balance_before=available_balance,
                    balance_after=new_available_balance,
                    related_order_no=withdrawal_no,
                    pay_type="WITHDRAWAL",
                    operator_id=user_id,
                    operator_name=user_name,
                    description=withdrawal_description,
                    remark=withdrawal_remark,
                    transaction_status="PENDING"  # 未处理状态
                )

                logger.info(f"✅ 创建提现申请交易记录成功: {withdrawal_transaction_no}, 金额: {apply_amount:.2f}元")
                logger.info(f"📊 余额变化: {available_balance:.2f} → {new_available_balance:.2f}")
                logger.info(f"💰 实际到账金额: {actual_amount:.2f}元 (无代扣个税)")

        except Exception as e:
            logger.error(f"创建提现交易流水失败: {str(e)}")
            raise BusinessException(message=f"创建交易流水失败: {str(e)}")

    @classmethod
    def _generate_withdrawal_no(cls) -> str:
        """生成提现申请单号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_str = str(uuid.uuid4()).replace("-", "")[:6]
        return f"WD{timestamp}{random_str}"

    @classmethod
    async def query_yeepay_order_status_service(
        cls,
        query_db: AsyncSession,
        user_id: str,
        order_number: str
    ) -> Dict[str, Any]:
        """查询易宝支付订单状态服务

        Args:
            query_db: 数据库会话
            user_id: 用户ID
            order_number: 订单号

        Returns:
            支付状态信息
        """
        try:
            logger.info(f"=== 开始查询易宝支付订单状态 ===")
            logger.info(f"用户ID: {user_id}, 订单号: {order_number}")

            # 先查询本地订单信息
            local_order = await cls._get_payment_order(query_db, order_number)
            if not local_order:
                logger.error(f"本地订单 {order_number} 不存在")
                raise BusinessException(message="订单不存在")

            # 如果本地订单已经是成功状态，直接返回
            if local_order.get("status") == "success":
                logger.info(f"本地订单 {order_number} 已经是成功状态")
                return {
                    "order_number": order_number,
                    "status": "success",
                    "txn_state": "1",
                    "message": "支付成功"
                }

            # 调用易宝支付查询接口
            logger.info(f"调用易宝支付查询接口查询订单状态")
            yeepay_result = await cls._call_yeepay_query_api(order_number)

            # 解析查询结果
            txn_state = yeepay_result.get("txnState")
            logger.info(f"易宝支付查询结果 - 订单状态: {txn_state}")

            if txn_state == "1":  # 支付成功
                logger.info(f"易宝支付查询确认订单 {order_number} 支付成功")

                # 根据支付类型进行不同的处理
                payment_type = local_order.get("payment_type", "company_recharge")

                if payment_type == "order_qrcode":
                    # 这是订单扫码支付，处理客户余额逻辑
                    logger.info(f"检测到订单扫码支付，处理客户余额逻辑")
                    original_order_number = local_order.get("original_order_number")
                    user_id = local_order.get("user_id")
                    amount = local_order.get("amount")

                    if original_order_number and user_id and amount:
                        await cls._handle_order_payment_success_direct(
                            query_db, str(original_order_number), str(user_id), float(amount), order_number
                        )
                        # 提交事务，确保订单支付状态更新生效
                        await query_db.commit()
                        logger.info(f"订单扫码支付成功处理完成，事务已提交 - 原订单: {original_order_number}")
                    else:
                        logger.error(f"订单扫码支付信息不完整: {local_order}")
                else:
                    # 这是普通的余额充值，更新公司余额
                    logger.info(f"检测到普通余额充值，更新公司余额")
                    await cls._update_user_balance(
                        query_db,
                        local_order["user_id"],
                        local_order["amount"],
                        order_number
                    )

                await cls._update_payment_order_status(query_db, order_number, "success")

                return {
                    "order_number": order_number,
                    "status": "success",
                    "txn_state": txn_state,
                    "message": "支付成功"
                }

            elif txn_state == "4":  # 支付失败
                logger.info(f"易宝支付查询确认订单 {order_number} 支付失败")

                await cls._update_payment_order_status(query_db, order_number, "failed")

                return {
                    "order_number": order_number,
                    "status": "failed",
                    "txn_state": txn_state,
                    "message": "支付失败"
                }

            elif txn_state in ["0", "2"]:  # 待支付或支付中
                logger.info(f"易宝支付查询订单 {order_number} 状态为: {txn_state}")

                return {
                    "order_number": order_number,
                    "status": "pending",
                    "txn_state": txn_state,
                    "message": "支付处理中" if txn_state == "2" else "待支付"
                }

            else:  # 其他状态
                logger.warning(f"易宝支付查询订单 {order_number} 未知状态: {txn_state}")

                return {
                    "order_number": order_number,
                    "status": "unknown",
                    "txn_state": txn_state,
                    "message": "支付状态未知"
                }

        except BusinessException as e:
            logger.error(f"查询易宝支付订单状态业务异常: {str(e)}")
            await query_db.rollback()
            raise e
        except Exception as e:
            logger.error(f"查询易宝支付订单状态系统异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            await query_db.rollback()
            raise BusinessException(message=f"查询支付状态失败: {str(e)}")

    @classmethod
    async def _call_yeepay_query_api(cls, order_number: str) -> Dict[str, Any]:
        """调用易宝支付查询API"""
        try:
            logger.info(f"=== 开始调用易宝支付查询API ===")
            logger.info(f"查询订单号: {order_number}")

            # 生成查询请求参数
            now = datetime.now()
            req_time = now.strftime("%Y%m%d%H%M%S")
            query_trace_no = f"QRY{req_time}{str(uuid.uuid4()).replace('-', '')[:6]}"

            # 从订单号中提取日期（假设订单号格式为 RC20250703152833c95ebf）
            order_date = order_number[2:10]  # 提取 20250703

            query_request = {
                "msgPublic": {
                    "version": YeepayConfig.YEEPAY_VERSION,
                    "cusReqTime": req_time,
                    "cusTraceNo": query_trace_no,
                    "cusCode": YeepayConfig.YEEPAY_CUSTOMER_CODE
                },
                "msgPrivate": {
                    "origTxnDate": order_date,
                    "origCusTraceNo": order_number
                }
            }

            logger.info(f"查询请求参数: {query_request}")

            # 构建完整请求体
            request_body = {"body": query_request}

            # 生成签名
            merchant_private_key = YeepayConfig.YEEPAY_MERCHANT_PRIVATE_KEY
            if not merchant_private_key:
                raise BusinessException(message="易宝支付商户私钥未配置")

            body_json = json.dumps(request_body["body"], ensure_ascii=False, separators=(',', ':'))
            signature = cls._generate_rsa_signature(body_json, merchant_private_key)
            request_body["sign"] = signature

            logger.info(f"查询请求签名: {signature[:50]}...")

            # 调用查询接口
            query_url = "https://api.kuaijie-pay.com/forward/pay/txn/v2/pay/query"

            async with httpx.AsyncClient(timeout=YeepayConfig.YEEPAY_TIMEOUT) as client:
                response = await client.post(
                    query_url,
                    json=request_body,
                    headers={"Content-Type": "application/json"}
                )

                logger.info(f"查询API HTTP状态码: {response.status_code}")

                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"查询API原始响应: {result}")

                    # 解析响应数据
                    response_body = result.get("body")
                    if isinstance(response_body, str):
                        body_data = json.loads(response_body)
                    else:
                        body_data = response_body or {}

                    if body_data.get("msgPublic"):
                        msg_public = body_data["msgPublic"]
                        msg_private = body_data.get("msgPrivate", {})

                        if msg_public.get("rspCode") == "0000":
                            logger.info(f"查询成功，订单状态: {msg_private.get('txnState')}")
                            return msg_private
                        else:
                            error_msg = msg_public.get("rspMsg", "查询失败")
                            logger.error(f"查询失败: {error_msg}")
                            raise BusinessException(message=f"查询支付状态失败: {error_msg}")
                    else:
                        logger.error(f"查询响应格式错误: {result}")
                        raise BusinessException(message="查询响应格式错误")
                else:
                    logger.error(f"查询API HTTP错误: {response.status_code}")
                    raise BusinessException(message=f"查询API调用失败: {response.status_code}")

        except BusinessException as e:
            raise e
        except Exception as e:
            logger.error(f"调用易宝支付查询API异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"查询支付状态失败: {str(e)}")

    @classmethod
    async def get_payment_order_status_service(
        cls,
        query_db: AsyncSession,
        user_id: str,
        order_number: str
    ) -> Dict[str, Any]:
        """查询支付订单状态服务

        Args:
            query_db: 数据库会话
            user_id: 用户ID
            order_number: 订单号

        Returns:
            订单状态信息
        """
        try:
            logger.info(f"=== 开始查询支付订单状态 ===")
            logger.info(f"用户ID: {user_id}, 订单号: {order_number}")

            # 获取用户信息和公司ID
            user_query = """
                SELECT company_id, name FROM internal_user
                WHERE uuid = :user_id AND status = '1'
            """

            user_result = await query_db.execute(text(user_query), {"user_id": user_id})
            user_row = user_result.fetchone()

            if not user_row:
                logger.error(f"用户 {user_id} 不存在或已禁用")
                raise BusinessException(message="用户不存在或已禁用")

            company_id = user_row._mapping.get("company_id")
            user_name = user_row._mapping.get("name")
            logger.info(f"用户信息 - 公司ID: {company_id}, 用户名: {user_name}")

            # 查询支付订单详情
            order_query = """
                SELECT
                    related_order_no,
                    operator_id,
                    operator_name,
                    amount,
                    balance_before,
                    balance_after,
                    remark,
                    pay_type,
                    external_transaction_id,
                    description,
                    transaction_time,
                    created_at
                FROM company_transaction
                WHERE related_order_no = :order_number
                AND company_uuid = :company_id
                AND business_type = 'RECHARGE'
                AND operator_id = :user_id
                ORDER BY created_at DESC
                LIMIT 1
            """

            result = await query_db.execute(text(order_query), {
                "order_number": order_number,
                "company_id": company_id,
                "user_id": user_id
            })
            row = result.fetchone()

            if not row:
                logger.error(f"订单 {order_number} 不存在或不属于当前用户")
                raise BusinessException(message="订单不存在或无权限查看")

            # 解析订单状态
            remark = row._mapping.get("remark", "")
            if "支付成功" in remark:
                status = "success"
                status_text = "支付成功"
            elif "支付失败" in remark:
                status = "failed"
                status_text = "支付失败"
            elif "待支付" in remark:
                status = "pending"
                status_text = "待支付"
            else:
                status = "unknown"
                status_text = "状态未知"

            order_info = {
                "order_number": row._mapping.get("related_order_no"),
                "amount": f"{row._mapping.get('amount', 0):.2f}",
                "status": status,
                "status_text": status_text,
                "payment_method": row._mapping.get("pay_type", ""),
                "external_transaction_id": row._mapping.get("external_transaction_id", ""),
                "operator_name": row._mapping.get("operator_name", ""),
                "description": row._mapping.get("description", ""),
                "balance_before": f"{row._mapping.get('balance_before', 0):.2f}",
                "balance_after": f"{row._mapping.get('balance_after', 0):.2f}",
                "transaction_time": row._mapping.get("transaction_time").strftime("%Y-%m-%d %H:%M:%S") if row._mapping.get("transaction_time") else "",
                "created_time": row._mapping.get("created_at").strftime("%Y-%m-%d %H:%M:%S") if row._mapping.get("created_at") else ""
            }

            logger.info(f"✅ 查询订单状态成功: {order_info}")
            return order_info

        except BusinessException as e:
            logger.error(f"查询订单状态业务异常: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"查询订单状态系统异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"查询订单状态失败: {str(e)}")

    @classmethod
    async def create_withdrawal_application_service(
        cls,
        query_db: AsyncSession,
        user_id: str,
        user_name: str,
        withdrawal_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建提现申请服务

        Args:
            query_db: 数据库会话
            user_id: 用户ID
            user_name: 用户姓名
            withdrawal_data: 提现申请数据

        Returns:
            提现申请结果
        """
        try:
            # 获取用户公司信息
            company_query = """
                SELECT company_id, company_name
                FROM internal_user
                WHERE uuid = :user_id
            """
            company_result = await query_db.execute(text(company_query), {"user_id": user_id})
            company_row = company_result.fetchone()

            if not company_row:
                raise BusinessException(message="用户公司信息不存在")

            company_uuid = company_row._mapping.get("company_id")
            if not company_uuid:
                raise BusinessException(message="用户未关联公司")

            # 直接调用内部的提现申请创建方法
            return await cls.create_withdrawal_application_with_freeze(
                query_db, company_uuid, user_id, user_name, withdrawal_data
            )

        except BusinessException as e:
            logger.error(f"创建提现申请业务异常: {str(e)}")
            raise e
        except Exception as e:
            logger.error(f"创建提现申请系统异常: {str(e)}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            raise BusinessException(message=f"创建提现申请失败: {str(e)}")

    @classmethod
    async def create_withdrawal_application_with_freeze(
        cls,
        query_db: AsyncSession,
        company_uuid: str,
        user_id: str,
        user_name: str,
        withdrawal_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        创建提现申请并冻结金额

        业务流程：
        1. 验证余额是否充足
        2. 从账户余额扣除申请金额
        3. 增加冻结金额
        4. 创建提现申请记录
        5. 记录交易流水

        Args:
            query_db: 数据库会话
            company_uuid: 公司UUID
            user_id: 用户ID
            user_name: 用户姓名
            withdrawal_data: 提现申请数据

        Returns:
            提现申请结果
        """
        try:
            logger.info(f"🔄 开始创建提现申请并冻结金额: 公司{company_uuid}, 用户{user_name}")

            # 1. 获取当前余额和冻结金额信息
            balance_info = await CompanyDao.get_company_balance_info(query_db, company_uuid)
            available_balance = balance_info["available_balance"]  # balance字段，可用余额
            current_frozen = balance_info["frozen_amount"]         # 冻结金额
            total_balance_before = balance_info["total_balance"]   # 账户总余额

            apply_amount = float(withdrawal_data.get("apply_amount", 0))
            withdrawal_type = withdrawal_data.get("withdrawal_type", 2)

            # 2. 根据提现类型处理费率
            if withdrawal_type == 1:  # 企业对公提现
                # 企业对公提现无手续费，已线下自主处理
                withdrawal_data["fee_rate"] = 0.0000
                withdrawal_data["fee_amount"] = 0.0000
                withdrawal_data["actual_amount"] = apply_amount
                logger.info(f"企业对公提现，无手续费，申请金额: {apply_amount}")
            else:  # 个人提现
                # 获取公司提现费率配置（代扣个税）
                company_config = await CompanyDao.get_company_withdrawal_config(query_db, company_uuid)
                expected_fee_rate = company_config["withdrawal_fee_rate"]

                # 验证前端传递的费率是否与配置一致
                frontend_fee_rate = float(withdrawal_data.get("fee_rate", 0))
                if abs(frontend_fee_rate - expected_fee_rate) > 0.0001:  # 允许小数精度误差
                    logger.warning(f"个人提现代扣个税费率不一致，配置费率: {expected_fee_rate}, 前端费率: {frontend_fee_rate}")
                    # 使用配置的费率重新计算
                    withdrawal_data["fee_rate"] = expected_fee_rate
                    fee_amount = apply_amount * expected_fee_rate
                    actual_amount = apply_amount - fee_amount
                    withdrawal_data["fee_amount"] = fee_amount
                    withdrawal_data["actual_amount"] = actual_amount
                    logger.info(f"个人提现费率已更正，代扣个税费率: {expected_fee_rate}, 代扣金额: {fee_amount}, 实际到账: {actual_amount}")
                else:
                    logger.info(f"个人提现代扣个税费率验证通过，费率: {expected_fee_rate}")

            # 3. 验证提现金额
            FinancialOperationValidator.validate_withdrawal_amount(apply_amount, available_balance)

            # 4. 验证余额一致性
            balance_validation = await TransactionManager.validate_balance_consistency(
                query_db, company_uuid
            )
            if not balance_validation["is_valid"]:
                raise BusinessException(message=f"余额状态异常: {balance_validation['messages']}")

            # 5. 生成提现申请单号
            withdrawal_no = cls._generate_withdrawal_no()

            # 6. 计算新的可用余额和冻结金额（总余额不变，只是在可用和冻结之间转移）
            new_available_balance = available_balance - apply_amount  # 从可用余额扣除
            new_frozen = current_frozen + apply_amount                # 加到冻结金额

            # 7. 更新公司可用余额和冻结金额（总余额不变）
            success = await CompanyDao.update_company_frozen_amount(
                query_db, company_uuid, apply_amount, -apply_amount
            )

            if not success:
                raise BusinessException(message="更新公司账户失败")

            # 7. 创建交易流水记录
            withdrawal_type = withdrawal_data.get("withdrawal_type", 2)
            fee_amount = withdrawal_data.get("fee_amount", 0)

            # 计算交易流水中的余额变化（模拟实际扣除过程）
            if fee_amount > 0:
                # 有手续费：先扣除实际到账金额，再扣除代扣个税
                actual_amount = apply_amount - fee_amount
                balance_after_withdrawal = available_balance - actual_amount
                final_balance = balance_after_withdrawal - fee_amount
            else:
                # 无手续费：直接扣除申请金额
                final_balance = available_balance - apply_amount

            await cls._create_withdrawal_transaction_records(
                query_db,
                company_uuid=company_uuid,
                withdrawal_no=withdrawal_no,
                withdrawal_type=withdrawal_type,
                apply_amount=apply_amount,
                fee_amount=fee_amount,
                available_balance=available_balance,
                new_available_balance=final_balance,  # 传入最终余额
                user_id=user_id,
                user_name=user_name
            )

            # 8. 创建提现申请记录
            withdrawal_type_name = "自行开票" if withdrawal_type == 1 else "零工提现"

            withdrawal_record = CompanyWithdrawal(
                company_uuid=company_uuid,
                withdrawal_no=withdrawal_no,
                withdrawal_type=withdrawal_type,
                withdrawal_type_name=withdrawal_type_name,
                apply_amount=apply_amount,
                fee_rate=withdrawal_data.get("fee_rate", 0.2000),  # 使用验证后的费率，默认20%
                fee_amount=withdrawal_data.get("fee_amount", 0),
                actual_amount=withdrawal_data.get("actual_amount", apply_amount),
                bank_name=withdrawal_data.get("bank_name"),
                bank_account=withdrawal_data.get("bank_account"),
                account_holder=withdrawal_data.get("account_holder"),
                invoice_info=withdrawal_data.get("invoice_info", ""),
                apply_reason=withdrawal_data.get("apply_reason"),
                status="PENDING",
                applicant_id=user_id,
                applicant_name=user_name,
                apply_time=datetime.now(),
                # 不绑定流水编号，之前的绑定是错的
                created_by=user_id
            )

            query_db.add(withdrawal_record)

            # 9. 提交事务
            await query_db.commit()

            logger.info(f"✅ 创建提现申请成功: {withdrawal_no}, 申请金额: {apply_amount}, 冻结金额: {new_frozen}")

            return {
                "withdrawal_no": withdrawal_no,
                "status": "PENDING",
                "apply_amount": apply_amount,
                "actual_amount": withdrawal_data.get("actual_amount", apply_amount),
                "fee_amount": withdrawal_data.get("fee_amount", 0),
                "available_balance_before": available_balance,
                "available_balance_after": new_available_balance,
                "frozen_before": current_frozen,
                "frozen_after": new_frozen,
                "total_balance": total_balance_before,  # 总余额不变
                "message": "提现申请提交成功，金额已从可用余额转入冻结状态，请等待审核"
            }

        except BusinessException as e:
            await query_db.rollback()
            logger.error(f"❌ 创建提现申请业务异常: {str(e)}")
            raise e
        except Exception as e:
            await query_db.rollback()
            logger.error(f"❌ 创建提现申请系统异常: {str(e)}")
            raise BusinessException(message=f"创建提现申请失败: {str(e)}")

    @classmethod
    def _generate_withdrawal_no(cls) -> str:
        """生成提现申请单号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_str = str(uuid.uuid4()).replace("-", "")[:6]
        return f"WD{timestamp}{random_str}"

    @classmethod
    async def create_order_qrcode_payment_service(
        cls,
        query_db: AsyncSession,
        order_number: str,
        user_id: str,
        customer_uuid: str,
        amount: float,
        request: Request
    ) -> Dict[str, Any]:
        """创建订单扫码支付服务

        专门用于订单支付的微信扫码支付，不复用余额充值逻辑
        """
        try:
            # 生成支付订单号
            payment_order_number = cls._generate_order_number()

            # 获取订单的产品名称
            product_name = await cls._get_order_product_name(query_db, order_number)

            # 构建易宝扫码支付请求参数
            yeepay_request = cls._build_yeepay_order_qrcode_request(
                payment_order_number, amount, request, product_name
            )

            # 调用易宝扫码支付API
            payment_result = await cls._call_yeepay_qrcode_api(yeepay_request)

            # 处理支付结果
            rsp_code = payment_result.get("rspCode")
            rsp_msg = payment_result.get("rspMsg")

            if rsp_code == "0000":
                # 支付订单创建成功，保存关联信息
                await cls._save_order_payment_relation(
                    query_db, order_number, payment_order_number, user_id, customer_uuid, amount
                )

                result = {
                    "order_number": order_number,
                    "payment_order_number": payment_order_number,
                    "payUrl": payment_result.get("payUrl"),
                    "amount": amount,
                    "rspCode": rsp_code,
                    "rspMsg": rsp_msg
                }

                logger.info(f"订单扫码支付创建成功 - 原订单: {order_number}, 支付订单: {payment_order_number}")
                return result
            else:
                # 支付订单创建失败
                logger.error(f"订单扫码支付创建失败，响应码: {rsp_code}, 响应消息: {rsp_msg}")
                raise BusinessException(
                    message=payment_result.get("rspMsg", "创建订单扫码支付失败")
                )

        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"创建订单扫码支付异常: {str(e)}")
            raise BusinessException(message=f"创建订单扫码支付失败: {str(e)}")

    @classmethod
    async def _get_order_product_name(cls, query_db: AsyncSession, order_number: str) -> str:
        """获取订单的产品名称"""
        try:
            query = text("""
                SELECT product_name
                FROM `order`
                WHERE order_number = :order_number
            """)
            result = await query_db.execute(query, {"order_number": order_number})
            row = result.fetchone()

            if row and row[0]:
                return row[0]
            else:
                return "服务产品"

        except Exception as e:
            logger.error(f"获取订单产品名称失败: {str(e)}")
            return "服务产品"

    @classmethod
    def _build_yeepay_order_qrcode_request(
        cls,
        order_number: str,
        amount: float,
        request: Request,
        product_name: str = "服务产品"
    ) -> Dict[str, Any]:
        """构建订单扫码支付请求参数"""
        try:
            # 转换金额为分
            amount_cents = int(amount * 100)

            # 生成请求时间（使用本地时间，确保与易宝支付时区一致）
            now = datetime.now()
            req_time = now.strftime("%Y%m%d%H%M%S")

            # 记录时间信息用于调试
            logger.info(f"生成订单扫码支付请求时间: {req_time}, 本地时间: {now}, 时区: {time.tzname}")

            # 生成过期时间（30分钟后）
            expire_time = (now + timedelta(minutes=30)).strftime("%Y%m%d%H%M%S")

            # 构建请求参数（按照官方文档格式）
            yeepay_request = {
                "msgPublic": {
                    "version": YeepayConfig.YEEPAY_VERSION,
                    "cusReqTime": req_time,
                    "cusTraceNo": order_number,
                    "cusCode": YeepayConfig.YEEPAY_CUSTOMER_CODE
                },
                "msgPrivate": {
                    "txnAmt": amount_cents,  # Integer 类型，单位：分
                    "currency": "CNY",  # 人民币
                    "bsnInfo": {
                        "subject": product_name,  # 使用订单的产品名称
                        "sn": order_number,  # 订单编号
                        "amt": amount_cents,  # 订单金额，单位：分
                        "expire": expire_time
                    },
                    "scene": {
                        "ip": cls._get_client_ip(request),
                        "type": "native",  # 扫码支付场景
                        "code": "online",  # 线上场景
                        "name": "金刚到家"  # 应用名称
                    },
                    "txnRemark": f"{product_name}订单支付",
                    "notifyUrl": f"{request.base_url}prod-api/api/v1/payment/yeepay/notify",
                    "completeUrl": ""
                }
            }

            return yeepay_request

        except Exception as e:
            logger.error(f"构建订单扫码支付请求参数异常: {str(e)}")
            raise BusinessException(message=f"构建支付请求失败: {str(e)}")

    @classmethod
    async def _save_order_payment_relation(
        cls,
        query_db: AsyncSession,
        order_number: str,
        payment_order_number: str,
        user_id: str,
        customer_uuid: str,
        amount: float
    ) -> None:
        """保存订单支付关联信息"""
        try:
            # 创建一个简单的关联记录表，用于存储订单和支付订单的关联关系
            # 这里使用一个临时表或者现有表的备注字段

            # 方案：使用ccuser_transaction表创建一个临时记录来存储关联关系
            transaction_no = cls._generate_transaction_no()

            insert_sql = text("""
                INSERT INTO ccuser_transaction (
                    ccuser_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    related_order_no, external_transaction_id, description, remark,
                    operator_id, operator_name, transaction_time, created_by, created_at
                ) VALUES (
                    :ccuser_uuid, :transaction_no, :business_type, :business_type_name,
                    :transaction_type, :amount, :balance_before, :balance_after,
                    :related_order_no, :external_transaction_id, :description, :remark,
                    :operator_id, :operator_name, NOW(), :created_by, NOW()
                )
            """)

            await query_db.execute(insert_sql, {
                "ccuser_uuid": customer_uuid,
                "transaction_no": transaction_no,
                "business_type": "ORDER_PAYMENT_RELATION",
                "business_type_name": "订单支付关联",
                "transaction_type": 0,  # 0-关联记录，不影响余额
                "amount": 0,  # 关联记录，金额为0
                "balance_before": 0,
                "balance_after": 0,
                "related_order_no": order_number,
                "external_transaction_id": payment_order_number,
                "description": f"订单支付关联记录",
                "remark": f"原订单号:{order_number},支付订单号:{payment_order_number},用户ID:{user_id},金额:{amount}",
                "operator_id": user_id,
                "operator_name": "系统",
                "created_by": user_id
            })

            # 立即提交这个关联记录，确保不会被后续的事务回滚影响
            await query_db.commit()

            logger.info(f"保存订单支付关联信息成功并已提交 - 原订单: {order_number}, 支付订单: {payment_order_number}")

        except Exception as e:
            logger.error(f"保存订单支付关联信息失败: {str(e)}")
            # 回滚当前操作
            await query_db.rollback()
            raise

    @classmethod
    async def _handle_order_payment_success(
        cls,
        query_db: AsyncSession,
        payment_order_number: str,
        amount: float
    ) -> None:
        """处理订单支付成功

        订单扫码支付成功后的处理逻辑：
        1. 查询关联的订单信息
        2. 给用户余额增加支付金额
        3. 从用户余额扣除订单金额
        4. 记录相关流水
        5. 更新订单支付状态
        """
        try:
            logger.info(f"处理订单支付成功 - 支付订单号: {payment_order_number}, 金额: {amount}")

            # 1. 查询支付订单关联的原订单信息
            original_order = await cls._get_original_order_by_payment_order(query_db, payment_order_number)
            if not original_order:
                logger.warning(f"未找到支付订单 {payment_order_number} 关联的原订单")
                return

            order_number = original_order.get('order_number')
            user_id = original_order.get('user_id')
            customer_uuid = original_order.get('customer_uuid')

            if not order_number or not user_id:
                logger.warning(f"订单信息不完整: order_number={order_number}, user_id={user_id}")
                return

            logger.info(f"找到关联订单: {order_number}, 用户ID: {user_id}")

            # 2. 处理订单扫码支付成功的业务逻辑
            await cls._process_order_qrcode_payment_success(
                query_db, order_number, user_id, customer_uuid, amount, payment_order_number
            )

        except Exception as e:
            logger.error(f"处理订单支付成功失败: {str(e)}")
            # 不抛出异常，避免影响支付状态查询的主流程

    @classmethod
    async def _handle_order_payment_success_direct(
        cls,
        query_db: AsyncSession,
        original_order_number: str,
        user_id: str,
        amount: float,
        payment_order_number: str
    ) -> None:
        """直接处理订单扫码支付成功的业务逻辑"""
        try:
            logger.info(f"处理订单扫码支付成功 - 原订单: {original_order_number}, 用户ID: {user_id}, 金额: {amount}")

            # 1. 获取客户信息（姓名和电话）
            customer_info = await cls._get_customer_info_by_user_id(query_db, user_id)
            customer_name = customer_info.get('name', '')
            customer_mobile = customer_info.get('mobile', '')

            # 2. 获取客户UUID（通过user_id查询ccuser表）
            customer_uuid_query = text("""
                SELECT uuid FROM ccuser
                WHERE id = :user_id AND (is_delete != '1' OR is_delete IS NULL)
            """)

            uuid_result = await query_db.execute(customer_uuid_query, {"user_id": user_id})
            uuid_row = uuid_result.fetchone()
            customer_uuid = uuid_row[0] if uuid_row else ""

            # 3. 获取原始订单的创建者信息
            operator_info = await cls._get_order_creator_info(query_db, original_order_number)
            operator_id = operator_info.get('operator_id', 'SYSTEM')
            operator_name = operator_info.get('operator_name', '系统')

            # 3. 获取用户当前余额
            current_balance = await cls._get_user_current_balance(query_db, user_id)

            # 4. 给用户余额增加支付金额（充值）
            new_balance_after_recharge = current_balance + amount
            await cls._update_user_balance_direct(query_db, user_id, new_balance_after_recharge)

            # 5. 记录充值流水
            recharge_transaction_no = cls._generate_transaction_no()
            await cls._create_recharge_transaction_record(
                query_db, customer_uuid, recharge_transaction_no, amount,
                current_balance, new_balance_after_recharge, original_order_number,
                customer_name, customer_mobile, payment_order_number,
                operator_id, operator_name
            )

            # 6. 从用户余额扣除订单金额（消费）
            final_balance = new_balance_after_recharge - amount
            await cls._update_user_balance_direct(query_db, user_id, final_balance)

            # 7. 记录消费流水
            consumption_transaction_no = cls._generate_transaction_no()
            await cls._create_consumption_transaction_record(
                query_db, customer_uuid, consumption_transaction_no, amount,
                new_balance_after_recharge, final_balance, original_order_number,
                customer_name, customer_mobile, operator_id, operator_name
            )

            # 8. 更新订单支付状态（order_payments表的pay_status字段改为1）
            await cls._update_order_payment_status_direct(query_db, original_order_number, consumption_transaction_no)

            logger.info(f"订单扫码支付成功处理完成 - 原订单: {original_order_number}, 金额: {amount}")

        except Exception as e:
            logger.error(f"处理订单扫码支付成功失败: {str(e)}")
            # 不抛出异常，避免影响支付状态查询的主流程

    @classmethod
    async def _get_original_order_by_payment_order(
        cls,
        query_db: AsyncSession,
        payment_order_number: str
    ) -> Optional[Dict[str, Any]]:
        """通过支付订单号查询关联的原订单信息"""
        try:
            # 先检查是否存在company_transaction记录（普通充值）
            query_sql = text("""
                SELECT related_order_no, remark
                FROM company_transaction
                WHERE related_order_no = :payment_order_number
                AND business_type = 'RECHARGE'
            """)

            result = await query_db.execute(query_sql, {"payment_order_number": payment_order_number})
            payment_order = result.fetchone()

            if payment_order:
                # 这是普通充值，不是订单扫码支付
                logger.info(f"支付订单 {payment_order_number} 是普通充值")
                return None

            # 查询订单支付关联记录
            order_relation_sql = text("""
                SELECT related_order_no, remark
                FROM ccuser_transaction
                WHERE external_transaction_id = :payment_order_number
                AND business_type = 'ORDER_PAYMENT_RELATION'
            """)

            relation_result = await query_db.execute(order_relation_sql, {"payment_order_number": payment_order_number})
            relation_row = relation_result.fetchone()

            if relation_row:
                # 找到订单支付关联记录
                original_order_number = relation_row[0]
                remark = relation_row[1] or ""

                # 从备注中解析用户信息
                user_id = ""
                customer_uuid = ""

                if "用户ID:" in remark:
                    try:
                        user_id = remark.split("用户ID:")[1].split(",")[0]
                    except:
                        pass

                # 从备注中解析customer_uuid（如果有的话）
                # 或者通过user_id查询ccuser表获取customer_uuid

                logger.info(f"找到订单支付关联 - 原订单: {original_order_number}, 支付订单: {payment_order_number}")

                return {
                    "order_number": original_order_number,
                    "user_id": user_id,
                    "customer_uuid": customer_uuid,
                    "is_order_payment": True
                }

            # 如果没有找到关联的原订单，返回None
            logger.warning(f"支付订单 {payment_order_number} 未找到关联的原订单")
            return None

        except Exception as e:
            logger.error(f"查询原订单失败: {str(e)}")
            return None

    @classmethod
    async def _process_order_qrcode_payment_success(
        cls,
        query_db: AsyncSession,
        order_number: str,
        user_id: str,
        customer_uuid: str,
        amount: float,
        payment_order_number: str
    ) -> None:
        """处理订单扫码支付成功的业务逻辑"""
        try:
            # 1. 获取客户信息（姓名和电话）
            customer_info = await cls._get_customer_info_by_user_id(query_db, user_id)
            customer_name = customer_info.get('name', '')
            customer_mobile = customer_info.get('mobile', '')

            # 2. 获取原始订单的创建者信息
            operator_info = await cls._get_order_creator_info(query_db, order_number)
            operator_id = operator_info.get('operator_id', 'SYSTEM')
            operator_name = operator_info.get('operator_name', '系统')

            # 3. 获取用户当前余额
            current_balance = await cls._get_user_current_balance(query_db, user_id)

            # 4. 给用户余额增加支付金额（充值）
            new_balance_after_recharge = current_balance + amount
            await cls._update_user_balance_direct(query_db, user_id, new_balance_after_recharge)

            # 5. 记录充值流水
            recharge_transaction_no = cls._generate_transaction_no()
            await cls._create_recharge_transaction_record(
                query_db, customer_uuid, recharge_transaction_no, amount,
                current_balance, new_balance_after_recharge, order_number,
                customer_name, customer_mobile, payment_order_number,
                operator_id, operator_name
            )

            # 6. 从用户余额扣除订单金额（消费）
            final_balance = new_balance_after_recharge - amount
            await cls._update_user_balance_direct(query_db, user_id, final_balance)

            # 7. 记录消费流水
            consumption_transaction_no = cls._generate_transaction_no()
            await cls._create_consumption_transaction_record(
                query_db, customer_uuid, consumption_transaction_no, amount,
                new_balance_after_recharge, final_balance, order_number,
                customer_name, customer_mobile, operator_id, operator_name
            )

            # 8. 更新订单支付状态（order_payments表的pay_status字段改为1）
            await cls._update_order_payment_status_direct(query_db, order_number, consumption_transaction_no)

            logger.info(f"订单扫码支付成功处理完成 - 订单号: {order_number}, 金额: {amount}")

        except Exception as e:
            logger.error(f"处理订单扫码支付成功业务逻辑失败: {str(e)}")
            raise

    @classmethod
    async def _get_customer_info_by_user_id(cls, query_db: AsyncSession, user_id: str) -> Dict[str, str]:
        """通过user_id查询ccuser表获取客户姓名和电话"""
        try:
            query_sql = text("""
                SELECT name, mobile FROM ccuser
                WHERE id = :user_id AND (is_delete != '1' OR is_delete IS NULL)
            """)

            result = await query_db.execute(query_sql, {"user_id": user_id})
            row = result.fetchone()

            if row:
                return {
                    'name': row[0] or '',
                    'mobile': row[1] or ''
                }
            else:
                logger.warning(f"用户 {user_id} 在ccuser表中不存在")
                return {'name': '', 'mobile': ''}

        except Exception as e:
            logger.error(f"查询客户信息失败: {str(e)}")
            return {'name': '', 'mobile': ''}

    @classmethod
    async def _get_user_current_balance(cls, query_db: AsyncSession, user_id: str) -> float:
        """获取用户当前余额"""
        try:
            query_sql = text("""
                SELECT amount FROM ccuser
                WHERE id = :user_id AND (is_delete != '1' OR is_delete IS NULL)
            """)

            result = await query_db.execute(query_sql, {"user_id": user_id})
            row = result.fetchone()

            if row:
                return float(row[0] or 0)
            else:
                logger.warning(f"用户 {user_id} 不存在")
                return 0.0

        except Exception as e:
            logger.error(f"获取用户余额失败: {str(e)}")
            return 0.0

    @classmethod
    async def _update_user_balance_direct(cls, query_db: AsyncSession, user_id: str, new_balance: float) -> None:
        """直接更新用户余额"""
        try:
            update_sql = text("""
                UPDATE ccuser
                SET amount = :new_balance, updated_at = NOW()
                WHERE id = :user_id AND (is_delete != '1' OR is_delete IS NULL)
            """)

            await query_db.execute(update_sql, {
                "new_balance": new_balance,
                "user_id": user_id
            })

            logger.info(f"更新用户 {user_id} 余额为: {new_balance}")

        except Exception as e:
            logger.error(f"更新用户余额失败: {str(e)}")
            raise

    @classmethod
    async def _create_recharge_transaction_record(
        cls,
        query_db: AsyncSession,
        customer_uuid: str,
        transaction_no: str,
        amount: float,
        balance_before: float,
        balance_after: float,
        order_number: str,
        customer_name: str = "",
        customer_mobile: str = "",
        external_transaction_id: str = "",
        operator_id: str = "SYSTEM",
        operator_name: str = "系统"
    ) -> None:
        """创建充值流水记录"""
        try:
            insert_sql = text("""
                INSERT INTO ccuser_transaction (
                    ccuser_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    pay_type, external_transaction_id, description, remark,
                    operator_id, operator_name, transaction_time, created_by, created_at, transaction_status
                ) VALUES (
                    :ccuser_uuid, :transaction_no, :business_type, :business_type_name,
                    :transaction_type, :amount, :balance_before, :balance_after,
                    :pay_type, :external_transaction_id, :description, :remark,
                    :operator_id, :operator_name, NOW(), :created_by, NOW(), :transaction_status
                )
            """)

            await query_db.execute(insert_sql, {
                "ccuser_uuid": customer_uuid,
                "transaction_no": transaction_no,
                "business_type": "MEMBER_RECHARGE",
                "business_type_name": "会员充值",
                "transaction_type": 1,  # 1-收入
                "amount": amount,
                "balance_before": balance_before,
                "balance_after": balance_after,
                "pay_type": "WECHAT_QRCODE",
                "external_transaction_id": external_transaction_id,
                "description": f"微信扫码支付，订单号：{order_number}",
                "remark": f"微信扫码支付，会员姓名：{customer_name}，电话：{customer_mobile}",
                "operator_id": operator_id,
                "operator_name": operator_name,
                "created_by": operator_id,
                "transaction_status": "SUCCESS"  # 支付成功后直接设置为成功状态
            })

            logger.info(f"创建充值流水记录成功 - 交易号: {transaction_no}, 金额: {amount}, 操作员: {operator_name}")

        except Exception as e:
            logger.error(f"创建充值流水记录失败: {str(e)}")
            raise

    @classmethod
    async def _create_consumption_transaction_record(
        cls,
        query_db: AsyncSession,
        customer_uuid: str,
        transaction_no: str,
        amount: float,
        balance_before: float,
        balance_after: float,
        order_number: str,
        customer_name: str = "",
        customer_mobile: str = "",
        operator_id: str = "SYSTEM",
        operator_name: str = "系统"
    ) -> None:
        """创建消费流水记录"""
        try:
            insert_sql = text("""
                INSERT INTO ccuser_transaction (
                    ccuser_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    related_order_no, pay_type, description, remark,
                    operator_id, operator_name, transaction_time, created_by, created_at, transaction_status
                ) VALUES (
                    :ccuser_uuid, :transaction_no, :business_type, :business_type_name,
                    :transaction_type, :amount, :balance_before, :balance_after,
                    :related_order_no, :pay_type, :description, :remark,
                    :operator_id, :operator_name, NOW(), :created_by, NOW(), :transaction_status
                )
            """)

            await query_db.execute(insert_sql, {
                "ccuser_uuid": customer_uuid,
                "transaction_no": transaction_no,
                "business_type": "SERVICE_CONSUMPTION",
                "business_type_name": "服务购买",
                "transaction_type": 2,  # 2-支出
                "amount": amount,
                "balance_before": balance_before,
                "balance_after": balance_after,
                "related_order_no": order_number,
                "pay_type": "BALANCE",
                "description": f"微信扫码支付，订单号：{order_number}",
                "remark": f"微信扫码支付，会员姓名：{customer_name}，电话：{customer_mobile}",
                "operator_id": operator_id,
                "operator_name": operator_name,
                "created_by": operator_id,
                "transaction_status": "SUCCESS"  # 支付成功后直接设置为成功状态
            })

            logger.info(f"创建消费流水记录成功 - 交易号: {transaction_no}, 订单号: {order_number}, 金额: {amount}, 操作员: {operator_name}")

        except Exception as e:
            logger.error(f"创建消费流水记录失败: {str(e)}")
            raise

    @classmethod
    async def _get_order_creator_info(cls, query_db: AsyncSession, order_number: str) -> Dict[str, str]:
        """获取订单创建者信息"""
        try:
            # 查询订单的门店UUID
            order_query = text("""
                SELECT store_uuid, sale_user_uuid
                FROM `order`
                WHERE order_number = :order_number
            """)

            order_result = await query_db.execute(order_query, {"order_number": order_number})
            order_row = order_result.fetchone()

            if not order_row:
                logger.warning(f"未找到订单: {order_number}")
                return {"operator_id": "SYSTEM", "operator_name": "系统"}

            store_uuid = order_row[0]
            sale_user_uuid = order_row[1]

            # 优先使用销售人员UUID，如果没有则使用门店UUID查找门店管理员
            if sale_user_uuid:
                # 通过销售人员UUID查找用户信息
                user_query = text("""
                    SELECT uuid, name
                    FROM internal_user
                    WHERE uuid = :user_uuid AND status = '1'
                """)

                user_result = await query_db.execute(user_query, {"user_uuid": sale_user_uuid})
                user_row = user_result.fetchone()

                if user_row:
                    return {
                        "operator_id": user_row[0],
                        "operator_name": user_row[1]
                    }

            # 如果没有销售人员信息，通过门店UUID查找门店管理员
            if store_uuid:
                store_user_query = text("""
                    SELECT uuid, name
                    FROM internal_user
                    WHERE store_uuid = :store_uuid AND status = '1'
                    ORDER BY role_id ASC
                    LIMIT 1
                """)

                store_user_result = await query_db.execute(store_user_query, {"store_uuid": store_uuid})
                store_user_row = store_user_result.fetchone()

                if store_user_row:
                    return {
                        "operator_id": store_user_row[0],
                        "operator_name": store_user_row[1]
                    }

            # 如果都没有找到，返回系统
            return {"operator_id": "SYSTEM", "operator_name": "系统"}

        except Exception as e:
            logger.error(f"获取订单创建者信息失败: {str(e)}")
            return {"operator_id": "SYSTEM", "operator_name": "系统"}

    @classmethod
    async def _create_order_payment_record_for_qrcode(
        cls,
        query_db: AsyncSession,
        order_number: str,
        amount: float,
        transaction_id: str
    ) -> None:
        """为扫码支付创建订单支付记录"""
        try:
            insert_sql = text("""
                INSERT INTO order_payments (
                    order_number, pay_money, pay_actual, pay_type, pay_type_name,
                    pay_time, pay_status, transaction_id, create_time, update_time
                ) VALUES (
                    :order_number, :pay_money, :pay_actual, :pay_type, :pay_type_name,
                    NOW(), :pay_status, :transaction_id, NOW(), NOW()
                )
            """)

            await query_db.execute(insert_sql, {
                "order_number": order_number,
                "pay_money": amount,
                "pay_actual": amount,
                "pay_type": 112,  # 112-扫码支付
                "pay_type_name": "扫码支付",
                "pay_status": 1,  # 1-支付成功
                "transaction_id": transaction_id
            })

            logger.info(f"创建订单支付记录成功 - 订单号: {order_number}, 金额: {amount}")

        except Exception as e:
            logger.error(f"创建订单支付记录失败: {str(e)}")
            raise

    @classmethod
    async def _update_order_payment_status_direct(cls, query_db: AsyncSession, order_number: str, transaction_no: str = "") -> None:
        """更新order_payments表的pay_status字段为1，并更新transaction_id"""
        try:
            # 更新order_payments表的支付状态和交易流水号
            update_sql = text("""
                UPDATE order_payments
                SET pay_status = 1, transaction_id = :transaction_id, pay_time = NOW()
                WHERE order_number = :order_number
            """)

            await query_db.execute(update_sql, {
                "order_number": order_number,
                "transaction_id": transaction_no
            })

            logger.info(f"更新订单 {order_number} 在order_payments表的支付状态为已支付，交易流水号: {transaction_no}")

        except Exception as e:
            logger.error(f"更新order_payments表支付状态失败: {str(e)}")
            raise
