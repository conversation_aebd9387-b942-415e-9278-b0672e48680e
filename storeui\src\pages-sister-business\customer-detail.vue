<template>
  <view class="customer-detail-page">
    <!-- 页面加载动画 -->
    <view class="page-loading" v-if="loading">
      <view class="loading-container">
        <view class="loading-spinner">
          <u-loading-icon mode="circle" size="50" color="#fdd118"></u-loading-icon>
        </view>
        <text class="loading-text">正在加载客户资料...</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-container" v-else>
      <!-- 顶部导航 -->
      <view class="header-section">
        <view class="header-background"></view>
        <view class="header-content">
          <view class="navbar">
            <view class="nav-left" @click="goBack">
              <u-icon name="arrow-left" size="20" color="#fff"></u-icon>
            </view>
            <view class="nav-title">
              <text class="title-text">客户资料详情</text>
            </view>
            <view class="nav-right">
              <!-- 移除编辑按钮 -->
            </view>
          </view>
        </view>
      </view>

      <!-- 客户信息表单 -->
      <view class="form-container">
        <!-- 姓名 -->
        <view class="form-item required">
          <text class="label">姓名</text>
          <input
            class="input"
            v-model="customerData.name"
            placeholder="请输入姓名"
          />
        </view>

        <!-- 电话 -->
        <view class="form-item">
          <text class="label">电话</text>
          <input
            class="input"
            v-model="customerData.mobile"
            placeholder="请输入电话"
          />
        </view>

        <!-- 微信号或昵称 -->
        <view class="form-item">
          <text class="label">微信号或昵称</text>
          <input
            class="input"
            v-model="customerData.wechat_number"
            placeholder="请输入微信号或昵称"
          />
        </view>

        <!-- 来源 -->
        <view class="form-item clickable" @click="showSourcePicker">
          <text class="label">来源</text>
          <view class="value-display">
            <text class="value">{{ getSourceName(customerData.source) }}</text>
            <u-icon name="arrow-right" size="12" color="#999"></u-icon>
          </view>
        </view>

        <!-- 销售归属 -->
        <view class="form-item" v-if="customerData.sales_owner_name">
          <view class="label-container">
            <text class="label">销售归属</text>
          </view>
          <view class="value-display">
            <text class="value">{{ customerData.sales_owner_name }}</text>
          </view>
        </view>

        <!-- 售后归属 -->
        <view class="form-item" v-if="customerData.after_sales_owner_name">
          <view class="label-container">
            <text class="label">售后归属</text>
          </view>
          <view class="value-display">
            <text class="value">{{ customerData.after_sales_owner_name }}</text>
          </view>
        </view>

        <!-- 类型 -->
        <view class="form-item required clickable" @click="showTypePicker">
          <text class="label">类型</text>
          <view class="value-display">
            <text class="value">{{ customerData.aunt_name || '家政员' }}</text>
            <u-icon name="arrow-right" size="12" color="#999"></u-icon>
          </view>
        </view>

        <!-- 薪资待遇 -->
        <view class="form-item required clickable" @click="showSalaryPicker">
          <text class="label">薪资待遇</text>
          <view class="value-display">
            <text class="value">{{ getSalaryDisplay() }}</text>
            <u-icon name="arrow-right" size="12" color="#999"></u-icon>
          </view>
        </view>

        <!-- 假期 -->
        <view class="form-item clickable" @click="showVacationPicker">
          <text class="label">假期</text>
          <view class="value-display">
            <text class="value">{{ getVacationDisplay() }}</text>
            <u-icon name="arrow-right" size="12" color="#999"></u-icon>
          </view>
        </view>

        <!-- 具体地址 -->
        <view class="form-item clickable" @click="showAddressEditor">
          <text class="label">具体地址</text>
          <view class="value-display">
            <text class="value" :class="{ 'placeholder': !customerData.address }">{{ customerData.address || '点击选择' }}</text>
            <u-icon name="arrow-right" size="12" color="#999"></u-icon>
          </view>
        </view>

        <!-- 备注 -->
        <view class="form-item textarea-item">
          <text class="label">备注</text>
          <textarea
            class="textarea"
            v-model="customerData.remark"
            placeholder="您可以备注客户详细需求及其他情况（如：生产医院、宝宝、老人情况）"
          />
        </view>









        <!-- 底部安全区域 -->
        <view class="safe-area-bottom"></view>
      </view>

      <!-- 底部保存按钮 -->
      <view class="bottom-save-area">
        <view class="save-button" @click="saveCustomerData">
          <text class="save-text">保存</text>
        </view>
      </view>
    </view>

    <!-- 类型选择弹窗 -->
    <u-popup
      :show="showTypeModal"
      mode="bottom"
      :round="20"
      :safe-area-inset-bottom="true"
      @close="showTypeModal = false"
      :close-on-click-overlay="true"
    >
      <view class="type-picker-container">

        <!-- 类型选择网格 -->
        <view class="type-grid">
          <view
            class="type-grid-item"
            v-for="type in typeList"
            :key="type.dict_code"
            @click="toggleTypeSelection(type)"
            :class="{ 'selected': isTypeSelected(type.dict_value) }"
          >
            <text class="type-grid-text">{{ type.dict_label }}</text>
            <!-- 选中状态的勾选图标 -->
            <view
              class="type-check-icon"
              v-if="isTypeSelected(type.dict_value)"
            >
              <u-icon name="checkmark" size="14" color="#fff"></u-icon>
            </view>
          </view>
        </view>

        <!-- 如果没有数据显示提示 -->
        <view v-if="typeList.length === 0" style="text-align: center; padding: 40rpx; color: #999;">
          <text>暂无类型数据</text>
        </view>

        <!-- 确认和取消按钮 -->
        <view class="type-button-section">
          <view class="type-btn type-cancel" @click="showTypeModal = false">
            <text class="type-btn-text">取消</text>
          </view>
          <view class="type-btn type-reset" @click="resetTypeSelection">
            <text class="type-btn-text">重置</text>
          </view>
          <view class="type-btn type-confirm" @click="confirmTypeSelection">
            <text class="type-btn-text">确认</text>
          </view>
        </view>

        <!-- 底部指示条 -->
        <view class="bottom-indicator"></view>
      </view>
    </u-popup>

    <!-- 来源选择弹窗 -->
    <u-popup
      :show="showSourceModal"
      mode="bottom"
      :round="20"
      :safe-area-inset-bottom="true"
      @close="showSourceModal = false"
      :close-on-click-overlay="true"
    >
      <view class="source-picker-container">
        <!-- 来源选择网格 -->
        <view class="source-grid">
          <view
            class="source-grid-item"
            v-for="source in sourceList"
            :key="source.id"
            @click="selectSource(source)"
            :class="{ 'selected': String(customerData.source) === String(source.id) }"
          >
            <text class="source-grid-text">{{ source.name }}</text>
            <!-- 选中状态的勾选图标 -->
            <view
              class="source-check-icon"
              v-if="String(customerData.source) === String(source.id)"
            >
              <u-icon name="checkmark" size="14" color="#fff"></u-icon>
            </view>
          </view>
        </view>

        <!-- 取消按钮 -->
        <view class="source-cancel-section">
          <view class="source-cancel-btn" @click="showSourceModal = false">
            <text class="source-cancel-text">取消</text>
          </view>
        </view>

        <!-- 底部指示条 -->
        <view class="bottom-indicator"></view>
      </view>
    </u-popup>

    <!-- 假期选择弹窗 -->
    <u-popup
      :show="showVacationModal"
      mode="bottom"
      :round="20"
      :safe-area-inset-bottom="true"
      @close="showVacationModal = false"
      :close-on-click-overlay="true"
    >
      <view class="vacation-picker-container">
        <!-- 假期选择网格 -->
        <view class="vacation-grid">
          <view
            class="vacation-grid-item"
            v-for="vacation in vacationOptions"
            :key="vacation.value"
            @click="selectVacation(vacation)"
            :class="{ 'selected': isVacationSelected(vacation.value) }"
          >
            <text class="vacation-grid-text">{{ vacation.label }}</text>
            <!-- 选中状态的勾选图标 -->
            <view
              class="vacation-check-icon"
              v-if="isVacationSelected(vacation.value)"
            >
              <u-icon name="checkmark" size="14" color="#fff"></u-icon>
            </view>
          </view>
        </view>

        <!-- 取消按钮 -->
        <view class="vacation-cancel-section">
          <view class="vacation-cancel-btn" @click="showVacationModal = false">
            <text class="vacation-cancel-text">取消</text>
          </view>
        </view>

        <!-- 底部指示条 -->
        <view class="bottom-indicator"></view>
      </view>
    </u-popup>

    <!-- 薪资编辑弹窗 -->
    <u-popup
      :show="showSalaryModal"
      mode="center"
      :round="20"
      @close="showSalaryModal = false"
      :close-on-click-overlay="true"
    >
      <view class="salary-picker-container">
        <view class="salary-header">
          <text class="salary-title">编辑薪资待遇</text>
        </view>

        <view class="salary-content">
          <!-- 最低薪资 -->
          <view class="salary-input-row">
            <text class="salary-label">最低薪资</text>
            <input
              class="salary-input"
              v-model="tempSalaryData.minSalary"
              type="number"
              placeholder="请输入最低薪资"
            />
            <text class="salary-unit-text">元</text>
          </view>

          <!-- 最高薪资 -->
          <view class="salary-input-row">
            <text class="salary-label">最高薪资</text>
            <input
              class="salary-input"
              v-model="tempSalaryData.maxSalary"
              type="number"
              placeholder="请输入最高薪资"
            />
            <text class="salary-unit-text">元</text>
          </view>

          <!-- 薪资单位选择 -->
          <view class="salary-unit-section">
            <text class="salary-unit-label">薪资单位</text>
            <view class="salary-unit-grid">
              <view
                class="salary-unit-item"
                v-for="unit in salaryUnits"
                :key="unit.value"
                @click="selectSalaryUnit(unit)"
                :class="{ 'selected': isUnitSelected(unit.value) }"
              >
                <text class="salary-unit-item-text">{{ unit.label }}</text>
                <!-- 自定义天数输入框显示在按钮右边 -->
                <view class="custom-days-inline" v-if="unit.value === 'custom' && tempSalaryData.isCustom">
                  <input
                    class="custom-days-input-inline"
                    v-model="tempSalaryData.customDays"
                    type="number"
                    placeholder="天数"
                  />
                  <text class="custom-days-unit">天</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="salary-buttons">
          <view class="salary-btn salary-cancel" @click="cancelSalaryEdit">
            <text class="salary-btn-text">取消</text>
          </view>
          <view class="salary-btn salary-confirm" @click="confirmSalaryEdit">
            <text class="salary-btn-text">确定</text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 地址编辑弹窗 -->
    <u-popup
      :show="showAddressModal"
      mode="center"
      :round="20"
      @close="showAddressModal = false"
      :close-on-click-overlay="true"
    >
      <view class="address-editor-container">
        <view class="address-header">
          <text class="address-title">编辑具体地址</text>
        </view>

        <view class="address-content">
          <textarea
            class="address-textarea"
            v-model="tempAddress"
            placeholder="请输入详细地址信息"
            :maxlength="200"
            :show-count="true"
            auto-height
          />
        </view>

        <view class="address-buttons">
          <view class="address-btn address-cancel" @click="cancelAddressEdit">
            <text class="address-btn-text">取消</text>
          </view>
          <view class="address-btn address-confirm" @click="confirmAddressEdit">
            <text class="address-btn-text">确定</text>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getCustomerDetail, updateCustomerDetail, getSourceList } from '@/api/sister-business.js';
import { getDictDataByType } from '@/api/dict.js';

export default {
  data() {
    return {
      customerUuid: '',
      customerData: {},
      loading: false,
      showSourceModal: false,
      sourceList: [],
      showVacationModal: false,
      vacationOptions: [
        { label: '无休', value: '' },
        { label: '法定假', value: '法定假' },
        { label: '单休', value: '单休' },
        { label: '双休', value: '双休' },
        { label: '月休1天', value: '月休1天' },
        { label: '月休2天', value: '月休2天' },
        { label: '月休3天', value: '月休3天' },
        { label: '月休4天', value: '月休4天' },
        { label: '月休6天', value: '月休6天' },
        { label: '月休8天', value: '月休8天' }
      ],
      showSalaryModal: false,
      tempSalaryData: {
        minSalary: '',
        maxSalary: '',
        unit: '元/月',
        isCustom: false,
        customDays: ''
      },
      salaryUnits: [
        { label: '每月', value: '元/月' },
        { label: '每日', value: '元/日' },
        { label: '每次', value: '元/次' },
        { label: '每小时', value: '元/小时' },
        { label: '自定义天数', value: 'custom' }
      ],
      showAddressModal: false,
      tempAddress: '',
      showTypeModal: false,
      typeList: [],
      selectedTypes: []
    };
  },

  onLoad(options) {
    if (options.uuid) {
      this.customerUuid = options.uuid;
      this.loadCustomerDetail();
      this.loadSourceList();
      this.loadTypeList();
    }
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 加载客户详情
    async loadCustomerDetail() {
      this.loading = true;
      try {
        const response = await getCustomerDetail(this.customerUuid);
        if (response) {
          this.customerData = response;
        }
      } catch (error) {
        console.error('加载客户详情失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取来源名称
    getSourceName(source) {
      if (this.sourceList.length > 0) {
        // 确保类型一致性，将两边都转换为字符串进行比较
        const sourceItem = this.sourceList.find(item => String(item.id) === String(source));
        return sourceItem ? sourceItem.name : '未知来源';
      }

      // 兜底映射（在来源列表未加载时使用）（修复：统一来源映射逻辑）
      const sourceMap = {
        '1': '线上推广',
        '2': '线下推广',
        '3': '老客户介绍',
        '4': '广告投放',
        '5': '门店咨询',
        '6': '电话咨询',
        '7': '其他渠道',
        '8': '微网站',
        '9': '广场抢单',
        '-1': '客户转介绍',
        '-2': '公司400电话',
        '-3': '系统分配',
        '-4': '公司指派'
      };
      return sourceMap[String(source)] || '其他渠道';
    },

    // 加载来源列表
    async loadSourceList() {
      console.log('开始加载来源列表');
      try {
        const response = await getSourceList();
        console.log('API响应:', response);
        if (response && response.length > 0) {
          this.sourceList = response;
          console.log('使用API数据，来源列表:', this.sourceList);
        } else {
          // 如果API返回空数据，使用兜底数据
          this.sourceList = [
            { id: '1', name: '微信留单' },
            { id: '2', name: '电话留单' },
            { id: '3', name: '上门留单' },
            { id: '4', name: '转介绍' },
            { id: '5', name: '广告留单' },
            { id: '6', name: '活动留单' },
            { id: '7', name: '其他留单' },
            { id: '8', name: '公司指派' }
          ];
          console.log('使用兜底数据，来源列表:', this.sourceList);
        }
      } catch (error) {
        console.error('加载来源列表失败:', error);
        // API调用失败时使用兜底数据（修复：统一来源映射逻辑）
        this.sourceList = [
          { id: '1', name: '线上推广' },
          { id: '2', name: '线下推广' },
          { id: '3', name: '老客户介绍' },
          { id: '4', name: '广告投放' },
          { id: '5', name: '门店咨询' },
          { id: '6', name: '电话咨询' },
          { id: '7', name: '其他渠道' },
          { id: '8', name: '微网站' },
          { id: '9', name: '广场抢单' },
          { id: '-1', name: '客户转介绍' },
          { id: '-2', name: '公司400电话' },
          { id: '-3', name: '系统分配' },
          { id: '-4', name: '公司指派' }
        ];
        console.log('API失败，使用兜底数据，来源列表:', this.sourceList);
      }
    },

    // 显示来源选择器
    showSourcePicker() {
      console.log('点击来源选择器');
      console.log('当前来源列表:', this.sourceList);
      console.log('当前showSourceModal状态:', this.showSourceModal);
      this.showSourceModal = true;
      console.log('设置后showSourceModal状态:', this.showSourceModal);
    },

    // 选择来源
    selectSource(source) {
      this.customerData.source = source.id;
      this.showSourceModal = false;

      uni.showToast({
        title: `已选择：${source.name}`,
        icon: 'none',
        duration: 1500
      });
    },

    // 加载类型列表
    async loadTypeList() {
      try {
        console.log('开始加载类型列表...');
        const response = await getDictDataByType('aunt_type');
        console.log('API原始响应:', response);
        console.log('响应类型:', typeof response);
        console.log('响应是否为数组:', Array.isArray(response));

        // 检查响应数据结构
        if (response) {
          // 如果响应是包装对象，尝试提取数据
          let dataArray = response;
          if (response.data) {
            dataArray = response.data;
            console.log('从response.data提取数据:', dataArray);
          } else if (response.result) {
            dataArray = response.result;
            console.log('从response.result提取数据:', dataArray);
          } else if (response.list) {
            dataArray = response.list;
            console.log('从response.list提取数据:', dataArray);
          }

          if (Array.isArray(dataArray) && dataArray.length > 0) {
            console.log('数据数组长度:', dataArray.length);
            console.log('第一个元素:', dataArray[0]);
            console.log('第一个元素的字段:', Object.keys(dataArray[0]));

            // 检查字段名称并进行标准化（后端返回的是驼峰命名，优先使用驼峰格式）
            this.typeList = dataArray.map(item => {
              const standardItem = {
                dict_code: item.dictCode || item.dict_code || item.id,
                dict_label: item.dictLabel || item.dict_label || item.label || item.name,
                dict_value: item.dictValue || item.dict_value || item.value || item.code,
                dict_type: item.dictType || item.dict_type || 'aunt_type'
              };
              console.log('原始API数据项:', item);
              console.log('标准化后的项:', standardItem);
              console.log('字段检查 - dict_code:', standardItem.dict_code, 'dict_label:', standardItem.dict_label, 'dict_value:', standardItem.dict_value);
              return standardItem;
            });

            console.log('类型列表加载成功，最终数据:', this.typeList);
          } else {
            console.log('数据为空或不是数组，使用兜底数据');
            this.useDefaultTypeList();
          }
        } else {
          console.log('响应为空，使用兜底数据');
          this.useDefaultTypeList();
        }
      } catch (error) {
        console.error('加载类型列表失败:', error);
        this.useDefaultTypeList();
      }
    },

    // 使用默认类型列表
    useDefaultTypeList() {
      this.typeList = [
        { dict_code: 1, dict_label: '月嫂', dict_value: 'yuesao', dict_type: 'aunt_type' },
        { dict_code: 2, dict_label: '育儿嫂', dict_value: 'yuersao', dict_type: 'aunt_type' },
        { dict_code: 3, dict_label: '保姆', dict_value: 'baomu', dict_type: 'aunt_type' },
        { dict_code: 4, dict_label: '钟点工', dict_value: 'zhongdiangong', dict_type: 'aunt_type' }
      ];
      console.log('使用兜底数据:', this.typeList);
    },

    // 显示类型选择器
    showTypePicker() {
      console.log('showTypePicker 被调用');
      console.log('当前 typeList:', this.typeList);
      console.log('typeList 长度:', this.typeList.length);

      // 初始化已选择的类型
      this.initSelectedTypes();
      this.showTypeModal = true;

      console.log('弹窗已打开，showTypeModal:', this.showTypeModal);
    },

    // 初始化已选择的类型
    initSelectedTypes() {
      console.log('=== 初始化已选择的类型 ===');
      console.log('当前客户数据 aunt_type:', this.customerData.aunt_type);
      console.log('当前客户数据 aunt_name:', this.customerData.aunt_name);
      console.log('当前 typeList:', this.typeList);

      this.selectedTypes = [];
      if (this.customerData.aunt_type) {
        // 将逗号分隔的字符串转换为数组
        const rawValues = this.customerData.aunt_type.split(',').filter(type => type.trim());
        console.log('从 aunt_type 解析出的原始值:', rawValues);

        // 数据清理：只保留有效的 dict_value（因为aunt_type应该存储dict_value）
        const validValues = [];

        rawValues.forEach(value => {
          console.log('检查值:', value);

          // 检查是否是有效的 dict_value
          const foundByValue = this.typeList.find(item => item.dict_value === value);
          if (foundByValue) {
            console.log('✅ 有效的 dict_value:', value, '->', foundByValue.dict_label);
            validValues.push(foundByValue.dict_value);
            return;
          }

          // 检查是否是 dict_label，如果是则转换为 dict_value
          const foundByLabel = this.typeList.find(item => item.dict_label === value);
          if (foundByLabel) {
            console.log('🔄 发现 dict_label，转换为 dict_value:', value, '->', foundByLabel.dict_value);
            validValues.push(foundByLabel.dict_value);
            return;
          }

          // 检查是否是 dict_code，如果是则转换为 dict_value
          const foundByCode = this.typeList.find(item => String(item.dict_code) === String(value));
          if (foundByCode) {
            console.log('🔄 发现 dict_code，转换为 dict_value:', value, '->', foundByCode.dict_value);
            validValues.push(foundByCode.dict_value);
            return;
          }

          console.warn('⚠️ 无效的值，跳过:', value);
        });

        this.selectedTypes = validValues;
        console.log('清理后的有效 selectedTypes (dict_value):', this.selectedTypes);

        // 如果数据被清理了，更新客户数据
        if (rawValues.length !== validValues.length) {
          console.log('🧹 数据已清理，更新客户数据');
          this.updateCustomerTypeData();
        }
      }
      console.log('最终初始化的 selectedTypes:', this.selectedTypes);
      console.log('=== 初始化完成 ===');
    },

    // 更新客户类型数据
    updateCustomerTypeData() {
      console.log('=== 更新客户类型数据 ===');

      // 根据有效的 selectedTypes（dict_value）更新 aunt_type 和 aunt_name
      this.customerData.aunt_type = this.selectedTypes.join(',');

      const selectedLabels = this.selectedTypes.map(value => {
        const typeItem = this.typeList.find(item => item.dict_value === value);
        return typeItem ? typeItem.dict_label : value;
      });
      this.customerData.aunt_name = selectedLabels.join(',');

      console.log('更新后的 aunt_type (dict_value):', this.customerData.aunt_type);
      console.log('更新后的 aunt_name (dict_label):', this.customerData.aunt_name);
      console.log('=== 更新完成 ===');
    },

    // 判断类型是否被选中（通过dict_value判断）
    isTypeSelected(dictValue) {
      if (!dictValue) {
        console.warn('isTypeSelected: dictValue 为空');
        return false;
      }
      return this.selectedTypes.includes(dictValue);
    },

    // 切换类型选择状态（使用dict_value）
    toggleTypeSelection(type) {
      console.log('=== toggleTypeSelection 被调用 ===');
      console.log('传入的 type 参数:', type);
      console.log('type 的所有字段:', Object.keys(type || {}));

      // 参数验证
      if (!type) {
        console.error('toggleTypeSelection: type 参数为空');
        return;
      }

      if (!type.dict_value) {
        console.error('toggleTypeSelection: type.dict_value 不存在');
        console.error('type对象详情:', JSON.stringify(type, null, 2));
        return;
      }

      console.log('🔍 【用户选择类型调试信息】');
      console.log('📋 dict_code (字典编码):', type.dict_code);
      console.log('🏷️  dict_label (字典标签):', type.dict_label);
      console.log('🔑 dict_value (字典键值):', type.dict_value);
      console.log('📝 dict_type (字典类型):', type.dict_type);
      console.log('📊 操作前的 selectedTypes (已选中的dict_value):', this.selectedTypes);

      const index = this.selectedTypes.indexOf(type.dict_value);
      if (index > -1) {
        // 如果已选中，则取消选择
        this.selectedTypes.splice(index, 1);
        console.log('❌ 【取消选择】');
        console.log('   - 取消的 dict_value:', type.dict_value);
        console.log('   - 取消的 dict_label:', type.dict_label);

        // 显示Toast提示
        uni.showToast({
          title: `取消选择：${type.dict_label}`,
          icon: 'none',
          duration: 1000
        });
      } else {
        // 如果未选中，则添加选择
        this.selectedTypes.push(type.dict_value);
        console.log('✅ 【添加选择】');
        console.log('   - 添加的 dict_value:', type.dict_value);
        console.log('   - 添加的 dict_label:', type.dict_label);

        // 显示Toast提示
        uni.showToast({
          title: `选择：${type.dict_label}`,
          icon: 'none',
          duration: 1000
        });
      }

      console.log('📊 操作后的 selectedTypes (已选中的dict_value):', this.selectedTypes);

      // 显示当前所有选中项的详细信息
      console.log('📋 【当前所有选中项详情】');
      this.selectedTypes.forEach((selectedValue, index) => {
        const selectedItem = this.typeList.find(item => item.dict_value === selectedValue);
        if (selectedItem) {
          console.log(`   ${index + 1}. dict_value: ${selectedItem.dict_value}, dict_label: ${selectedItem.dict_label}`);
        }
      });

      console.log('=== toggleTypeSelection 结束 ===');
    },

    // 确认类型选择
    confirmTypeSelection() {
      console.log('=== 🎯 确认类型选择开始 ===');
      console.log('📊 当前选择的类型数组 selectedTypes:', this.selectedTypes);
      console.log('📋 当前类型列表 typeList 长度:', this.typeList.length);

      // 验证至少选择一个类型
      if (this.selectedTypes.length === 0) {
        console.log('⚠️ 用户未选择任何类型');
        uni.showToast({
          title: '请至少选择一个类型',
          icon: 'none',
          duration: 1500
        });
        return;
      }

      console.log('🔄 开始处理选择的类型...');
      console.log('📝 【选中类型详细信息】');

      // 获取对应的 dict_value 和 dict_label
      const validValues = [];
      const validLabels = [];

      this.selectedTypes.forEach((selectedValue, index) => {
        console.log(`\n--- 处理第 ${index + 1} 个选中项 ---`);
        console.log('🔍 正在处理的 dict_value:', selectedValue);

        // 在 typeList 中查找对应的项
        const typeItem = this.typeList.find(item => {
          const isMatch = item.dict_value === selectedValue;
          console.log(`   比较: "${item.dict_value}" === "${selectedValue}" -> ${isMatch}`);
          return isMatch;
        });

        if (typeItem) {
          console.log('✅ 找到匹配项:');
          console.log('   📋 dict_code:', typeItem.dict_code);
          console.log('   🏷️  dict_label:', typeItem.dict_label);
          console.log('   🔑 dict_value:', typeItem.dict_value);
          console.log('   📝 dict_type:', typeItem.dict_type);

          validValues.push(typeItem.dict_value);
          validLabels.push(typeItem.dict_label);
        } else {
          console.warn('❌ 未找到匹配项，selectedValue:', selectedValue);
          console.warn('   将直接使用原值作为备用');
          // 如果没找到，可能是数据问题，尝试直接使用
          validValues.push(selectedValue);
          validLabels.push(selectedValue);
        }
      });

      console.log('\n🎯 【最终处理结果】');
      console.log('📊 有效的 dict_value 数组:', validValues);
      console.log('📊 有效的 dict_label 数组:', validLabels);
      console.log('🔗 将要存储的 aunt_type (逗号分隔):', validValues.join(','));
      console.log('🔗 将要存储的 aunt_name (逗号分隔):', validLabels.join(','));

      // 更新客户数据 - 按照数据库格式：aunt_type存储dict_value，aunt_name存储dict_label
      this.customerData.aunt_type = validValues.join(',');
      this.customerData.aunt_name = validLabels.join(',');

      console.log('\n💾 【数据更新完成】');
      console.log('✅ customerData.aunt_type:', this.customerData.aunt_type);
      console.log('✅ customerData.aunt_name:', this.customerData.aunt_name);
      console.log('=== 🎯 确认类型选择结束 ===');

      this.showTypeModal = false;

      uni.showToast({
        title: `类型已设置为：${validLabels.join('、')}`,
        icon: 'none',
        duration: 2000
      });
    },

    // 重置类型选择
    resetTypeSelection() {
      console.log('=== 重置类型选择 ===');

      uni.showModal({
        title: '确认重置',
        content: '确定要清空所有已选择的类型吗？',
        success: (res) => {
          if (res.confirm) {
            this.selectedTypes = [];
            this.customerData.aunt_type = '';
            this.customerData.aunt_name = '';

            console.log('✅ 类型选择已重置');
            uni.showToast({
              title: '已重置类型选择',
              icon: 'none',
              duration: 1500
            });
          }
        }
      });
    },

    // 获取假期显示文本
    getVacationDisplay() {
      if (!this.customerData.vacation) {
        return '无休';
      }
      return this.customerData.vacation;
    },

    // 显示假期选择弹窗
    showVacationPicker() {
      this.showVacationModal = true;
    },

    // 判断假期是否被选中
    isVacationSelected(value) {
      const currentVacation = this.customerData.vacation || '';
      // 如果当前假期为空且选择的是"无休"
      if (!currentVacation && !value) {
        return true;
      }
      return currentVacation === value;
    },

    // 选择假期
    selectVacation(vacation) {
      // 更新客户数据中的假期信息
      this.customerData.vacation = vacation.value;
      this.showVacationModal = false;

      const displayText = vacation.label;
      uni.showToast({
        title: `假期已设置为：${displayText}`,
        icon: 'none',
        duration: 1500
      });
    },

    // 显示地址编辑弹窗
    showAddressEditor() {
      this.tempAddress = this.customerData.address || '';
      this.showAddressModal = true;
    },

    // 取消地址编辑
    cancelAddressEdit() {
      this.showAddressModal = false;
      this.tempAddress = '';
    },

    // 确认地址编辑
    confirmAddressEdit() {
      // 验证地址不能为空
      if (!this.tempAddress.trim()) {
        uni.showToast({
          title: '请输入具体地址',
          icon: 'none',
          duration: 1500
        });
        return;
      }

      // 更新客户数据
      this.customerData.address = this.tempAddress.trim();
      this.showAddressModal = false;

      uni.showToast({
        title: '地址已更新',
        icon: 'none',
        duration: 1500
      });
    },

    // 显示薪资编辑弹窗
    showSalaryPicker() {
      const currentUnit = this.customerData.salary_unit || '元/月';

      // 检查是否是自定义天数格式（如：元/15天）
      const customMatch = currentUnit.match(/^元\/(\d+)天$/);

      // 初始化临时薪资数据
      this.tempSalaryData = {
        minSalary: this.customerData.min_salary || '',
        maxSalary: this.customerData.max_salary || '',
        unit: customMatch ? 'custom' : currentUnit,
        isCustom: !!customMatch,
        customDays: customMatch ? customMatch[1] : ''
      };
      this.showSalaryModal = true;
    },

    // 判断薪资单位是否被选中
    isUnitSelected(unitValue) {
      if (unitValue === 'custom') {
        return this.tempSalaryData.isCustom;
      }
      return this.tempSalaryData.unit === unitValue && !this.tempSalaryData.isCustom;
    },

    // 选择薪资单位
    selectSalaryUnit(unit) {
      if (unit.value === 'custom') {
        this.tempSalaryData.isCustom = true;
        this.tempSalaryData.unit = 'custom';
        this.tempSalaryData.customDays = this.tempSalaryData.customDays || '15'; // 默认15天
      } else {
        this.tempSalaryData.isCustom = false;
        this.tempSalaryData.unit = unit.value;
        this.tempSalaryData.customDays = '';
      }
    },

    // 取消薪资编辑
    cancelSalaryEdit() {
      this.showSalaryModal = false;
      this.tempSalaryData = {
        minSalary: '',
        maxSalary: '',
        unit: '元/月',
        isCustom: false,
        customDays: ''
      };
    },

    // 确认薪资编辑
    confirmSalaryEdit() {
      // 验证输入
      if (!this.tempSalaryData.minSalary && !this.tempSalaryData.maxSalary) {
        uni.showToast({
          title: '请至少输入一个薪资值',
          icon: 'none',
          duration: 1500
        });
        return;
      }

      // 如果是自定义天数，验证天数输入
      if (this.tempSalaryData.isCustom) {
        if (!this.tempSalaryData.customDays || this.tempSalaryData.customDays <= 0) {
          uni.showToast({
            title: '请输入有效的天数',
            icon: 'none',
            duration: 1500
          });
          return;
        }
      }

      // 更新客户数据
      this.customerData.min_salary = this.tempSalaryData.minSalary;
      this.customerData.max_salary = this.tempSalaryData.maxSalary;

      // 处理薪资单位
      if (this.tempSalaryData.isCustom) {
        this.customerData.salary_unit = `元/${this.tempSalaryData.customDays}天`;
      } else {
        this.customerData.salary_unit = this.tempSalaryData.unit;
      }

      this.showSalaryModal = false;

      uni.showToast({
        title: '薪资待遇已更新',
        icon: 'none',
        duration: 1500
      });
    },

    // 获取薪资显示
    getSalaryDisplay() {
      const minSalary = this.customerData.min_salary;
      const maxSalary = this.customerData.max_salary;
      const unit = this.customerData.salary_unit || '元/每月';

      if (minSalary && maxSalary) {
        return `${minSalary}-${maxSalary}${unit}`;
      } else if (minSalary) {
        return `${minSalary}${unit}`;
      } else if (maxSalary) {
        return `${maxSalary}${unit}`;
      } else {
        return '6000元/每月';
      }
    },

    // 保存客户数据
    async saveCustomerData() {
      try {
        console.log('=== 💾 开始保存客户数据 ===');
        console.log('📊 【保存前的完整客户数据】');
        console.log('🔑 aunt_type (字典键值):', this.customerData.aunt_type);
        console.log('🏷️  aunt_name (字典标签):', this.customerData.aunt_name);
        console.log('👤 客户姓名:', this.customerData.name);
        console.log('📱 客户手机:', this.customerData.mobile);
        console.log('🏠 客户地址:', this.customerData.address);

        // 解析并显示类型详情
        if (this.customerData.aunt_type) {
          console.log('\n📋 【类型数据解析】');
          const typeValues = this.customerData.aunt_type.split(',');
          const typeLabels = this.customerData.aunt_name ? this.customerData.aunt_name.split(',') : [];

          console.log('🔢 类型数量:', typeValues.length);
          typeValues.forEach((value, index) => {
            const label = typeLabels[index] || '未知标签';
            console.log(`   ${index + 1}. dict_value: "${value}" -> dict_label: "${label}"`);
          });
        }

        // 只发送页面上实际展示和可编辑的字段
        const updateData = {
          uuid: this.customerData.uuid,
          name: this.customerData.name,
          mobile: this.customerData.mobile,
          wechat_number: this.customerData.wechat_number,
          wechat_nickname: this.customerData.wechat_nickname,
          aunt_type: this.customerData.aunt_type,
          aunt_name: this.customerData.aunt_name,
          min_salary: this.customerData.min_salary,
          max_salary: this.customerData.max_salary,
          salary_unit: this.customerData.salary_unit,
          vacation: this.customerData.vacation,
          address: this.customerData.address,
          remark: this.customerData.remark,
          source: this.customerData.source
        };

        console.log('\n🚀 【发送到后端的数据】');
        console.log('🔑 updateData.aunt_type (将存储到数据库):', updateData.aunt_type);
        console.log('🏷️  updateData.aunt_name (将存储到数据库):', updateData.aunt_name);
        console.log('🆔 updateData.uuid (客户标识):', updateData.uuid);
        console.log('📦 完整的 updateData:', JSON.stringify(updateData, null, 2));

        // 验证数据格式
        if (updateData.aunt_type) {
          const typeValues = updateData.aunt_type.split(',');
          const typeLabels = updateData.aunt_name ? updateData.aunt_name.split(',') : [];
          console.log('\n🔍 【数据格式验证】');
          console.log('✅ aunt_type 格式正确，包含', typeValues.length, '个类型值');
          console.log('✅ aunt_name 格式正确，包含', typeLabels.length, '个类型标签');

          if (typeValues.length !== typeLabels.length) {
            console.warn('⚠️ 警告：类型值和标签数量不匹配！');
          }
        }

        console.log('\n📡 正在发送请求到后端...');
        await updateCustomerDetail(updateData);
        console.log('✅ 🎉 保存成功！数据已更新到数据库');
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('❌ 保存失败:', error);
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.customer-detail-page {
  height: 100vh;
  background: #f8f8f8;
}

// 页面加载动画
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading-spinner {
      margin-bottom: 30rpx;
    }

    .loading-text {
      font-size: 28rpx;
      color: #666;
      text-align: center;
    }
  }
}

// 主要内容容器
.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 顶部区域
.header-section {
  flex-shrink: 0;
  position: relative;
  padding-top: calc(var(--status-bar-height) + 20rpx);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 30rpx;
  }
}

// 导航栏
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;

  .nav-left {
    width: 80rpx;
    display: flex;
    align-items: center;
  }

  .nav-title {
    flex: 1;
    text-align: center;

    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #fff;
    }
  }

  .nav-right {
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

// 表单容器
.form-container {
  flex: 1;
  overflow-y: auto;
  background: #fff;
  padding-bottom: 120rpx; // 为底部保存按钮留出空间
}

// 表单项
.form-item {
  display: flex;
  align-items: center;
  padding: 28rpx 30rpx; // 减少padding降低高度
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 80rpx; // 减少最小高度
  box-sizing: border-box;

  &.required .label::before {
    content: '*';
    color: #ff4757;
    margin-right: 8rpx;
  }

  &.clickable {
    cursor: pointer;

    &:active {
      background-color: #f8f8f8;
    }
  }



  &.textarea-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 32rpx 30rpx;
    min-height: auto;

    .label {
      margin-bottom: 20rpx;
    }

    .textarea {
      width: 100%;
      min-height: 80rpx;
      padding: 20rpx;
      border: 1rpx solid #e8e8e8;
      border-radius: 8rpx;
      font-size: 28rpx;
      color: #333;
      background: #fff;
      box-sizing: border-box;
      resize: none;

      &::placeholder {
        color: #999;
      }

      &:focus {
        border-color: #fdd118;
        outline: none;
      }
    }
  }

  .label {
    font-size: 28rpx;
    color: #333;
    font-weight: 400;
    flex-shrink: 0;
    width: 200rpx;
  }

  .input {
    flex: 1;
    text-align: right;
    font-size: 28rpx;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
    padding: 0;

    &::placeholder {
      color: #999;
    }
  }

  .value-display {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 16rpx;

    .value {
      font-size: 28rpx;
      color: #333;
      text-align: right;

      &.placeholder {
        color: #999;
      }
    }
  }
}

// 底部保存区域
.bottom-save-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;

  .save-button {
    width: 100%;
    height: 88rpx;
    background: #00c851;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      background: #00a844;
    }

    .save-text {
      font-size: 32rpx;
      color: #fff;
      font-weight: 500;
    }
  }
}

// 底部安全区域
.safe-area-bottom {
  height: calc(env(safe-area-inset-bottom) + 40rpx);
}

// 类型选择弹窗
.type-picker-container {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 30rpx 0;

  .type-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    margin-bottom: 40rpx;

    .type-grid-item {
      height: 80rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2rpx solid transparent;
      transition: all 0.2s ease;
      position: relative;

      &:active {
        background: #e9ecef;
        transform: scale(0.98);
      }

      &.selected {
        background: #e8f5e8;
        border-color: #52c41a;

        .type-grid-text {
          color: #52c41a;
          font-weight: 500;
        }

        .type-check-icon {
          position: absolute;
          top: -8rpx;
          right: -8rpx;
          width: 32rpx;
          height: 32rpx;
          background: #52c41a;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 3rpx solid #fff;
        }
      }

      .type-grid-text {
        font-size: 26rpx;
        color: #333;
        text-align: center;
      }
    }
  }

  .type-button-section {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
    margin: 0 -30rpx;

    .type-btn {
      flex: 1;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:not(:last-child) {
        border-right: 1rpx solid #f0f0f0;
      }

      &:active {
        background: #f5f5f5;
      }

      .type-btn-text {
        font-size: 32rpx;
        color: #666;
      }

      &.type-confirm {
        .type-btn-text {
          color: #fdd118;
          font-weight: 600;
        }
      }

      &.type-reset {
        .type-btn-text {
          color: #ff4757;
          font-weight: 500;
        }
      }
    }
  }

  .bottom-indicator {
    height: 8rpx;
    width: 60rpx;
    background: #e8e8e8;
    border-radius: 4rpx;
    margin: 20rpx auto 0;
  }
}

// 来源选择弹窗
.source-picker-container {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 30rpx 0;

  // 来源选择网格
  .source-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx;
    padding-bottom: 40rpx;

    .source-grid-item {
      position: relative;
      height: 88rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2rpx solid transparent;
      transition: all 0.2s ease;

      &:active {
        background: #e9ecef;
        transform: scale(0.98);
      }

      // 选中状态
      &.selected {
        background: #e8f5e8;
        border-color: #52c41a;

        .source-grid-text {
          color: #52c41a;
          font-weight: 500;
        }
      }

      .source-grid-text {
        font-size: 28rpx;
        color: #333;
        text-align: center;
        line-height: 1.2;
      }

      // 选中状态的勾选图标
      .source-check-icon {
        position: absolute;
        top: -6rpx;
        right: -6rpx;
        width: 32rpx;
        height: 32rpx;
        background: #52c41a;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3rpx solid #fff;
        box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
      }
    }
  }

  // 取消按钮区域
  .source-cancel-section {
    padding: 20rpx 0 40rpx;
    border-top: 1rpx solid #f0f0f0;

    .source-cancel-btn {
      width: 100%;
      height: 88rpx;
      background: #fff;
      border: 1rpx solid #d9d9d9;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        background: #f5f5f5;
      }

      .source-cancel-text {
        font-size: 32rpx;
        color: #666;
      }
    }
  }

  // 底部指示条
  .bottom-indicator {
    width: 134rpx;
    height: 10rpx;
    background: #000;
    border-radius: 5rpx;
    margin: 0 auto 20rpx;
    opacity: 0.3;
  }
}

// 假期选择弹窗
.vacation-picker-container {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx 30rpx 0;

  // 假期选择网格
  .vacation-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx;
    padding-bottom: 40rpx;

    .vacation-grid-item {
      position: relative;
      height: 88rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2rpx solid transparent;
      transition: all 0.2s ease;

      &:active {
        background: #e9ecef;
        transform: scale(0.98);
      }

      // 选中状态
      &.selected {
        background: #e8f5e8;
        border-color: #52c41a;

        .vacation-grid-text {
          color: #52c41a;
          font-weight: 500;
        }
      }

      .vacation-grid-text {
        font-size: 28rpx;
        color: #333;
        text-align: center;
        line-height: 1.2;
      }

      // 选中状态的勾选图标
      .vacation-check-icon {
        position: absolute;
        top: -6rpx;
        right: -6rpx;
        width: 32rpx;
        height: 32rpx;
        background: #52c41a;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3rpx solid #fff;
        box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
      }
    }
  }

  // 取消按钮区域
  .vacation-cancel-section {
    padding: 20rpx 0 40rpx;
    border-top: 1rpx solid #f0f0f0;

    .vacation-cancel-btn {
      width: 100%;
      height: 88rpx;
      background: #fff;
      border: 1rpx solid #d9d9d9;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        background: #f5f5f5;
      }

      .vacation-cancel-text {
        font-size: 32rpx;
        color: #666;
      }
    }
  }

  // 底部指示条
  .bottom-indicator {
    width: 134rpx;
    height: 10rpx;
    background: #000;
    border-radius: 5rpx;
    margin: 0 auto 20rpx;
    opacity: 0.3;
  }
}

// 薪资编辑弹窗
.salary-picker-container {
  background: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  padding: 0;

  .salary-header {
    padding: 40rpx 40rpx 20rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;

    .salary-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .salary-content {
    padding: 40rpx;

    .salary-input-row {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;

      .salary-label {
        width: 160rpx;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }

      .salary-input {
        flex: 1;
        height: 80rpx;
        border: 2rpx solid #e8e8e8;
        border-radius: 12rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #333;
        background: #f8f9fa;
        margin-right: 20rpx;

        &:focus {
          border-color: #fdd118;
          background: #fff;
        }
      }

      .salary-unit-text {
        font-size: 28rpx;
        color: #666;
        width: 40rpx;
      }
    }

    .salary-unit-section {
      margin-top: 40rpx;

      .salary-unit-label {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 20rpx;
        display: block;
      }

      .salary-unit-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16rpx;

        .salary-unit-item {
          height: 72rpx;
          background: #f8f9fa;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2rpx solid transparent;
          transition: all 0.2s ease;
          position: relative;

          &:active {
            background: #e9ecef;
            transform: scale(0.98);
          }

          &.selected {
            background: #e8f5e8;
            border-color: #52c41a;

            .salary-unit-item-text {
              color: #52c41a;
              font-weight: 500;
            }
          }

          .salary-unit-item-text {
            font-size: 26rpx;
            color: #333;
          }

          .custom-days-inline {
            display: flex;
            align-items: center;
            margin-left: 16rpx;
            gap: 8rpx;

            .custom-days-input-inline {
              width: 60rpx;
              height: 48rpx;
              border: 1rpx solid #52c41a;
              border-radius: 6rpx;
              text-align: center;
              font-size: 24rpx;
              color: #52c41a;
              background: #fff;
              padding: 0;
            }

            .custom-days-unit {
              font-size: 24rpx;
              color: #52c41a;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .salary-buttons {
    display: flex;
    border-top: 1rpx solid #f0f0f0;

    .salary-btn {
      flex: 1;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:first-child {
        border-right: 1rpx solid #f0f0f0;
      }

      &:active {
        background: #f5f5f5;
      }

      .salary-btn-text {
        font-size: 32rpx;
        color: #666;
      }

      &.salary-confirm {
        .salary-btn-text {
          color: #fdd118;
          font-weight: 600;
        }
      }
    }
  }
}

// 地址编辑弹窗
.address-editor-container {
  background: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  padding: 0;

  .address-header {
    padding: 40rpx 40rpx 20rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;

    .address-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .address-content {
    padding: 40rpx;

    .address-textarea {
      width: 100%;
      min-height: 200rpx;
      border: 2rpx solid #e8e8e8;
      border-radius: 12rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #333;
      background: #f8f9fa;
      line-height: 1.5;

      &:focus {
        border-color: #fdd118;
        background: #fff;
      }
    }
  }

  .address-buttons {
    display: flex;
    border-top: 1rpx solid #f0f0f0;

    .address-btn {
      flex: 1;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:first-child {
        border-right: 1rpx solid #f0f0f0;
      }

      &:active {
        background: #f5f5f5;
      }

      .address-btn-text {
        font-size: 32rpx;
        color: #666;
      }

      &.address-confirm {
        .address-btn-text {
          color: #fdd118;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
