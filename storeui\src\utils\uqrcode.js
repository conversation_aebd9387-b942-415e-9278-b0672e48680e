/**
 * 二维码生成工具
 * 使用多个备用API和重试机制生成真实可识别的二维码
 */

// 二维码生成器
class UQRCode {
  constructor() {
    this.errorCorrectLevel = {
      L: 'L',
      M: 'M',
      Q: 'Q',
      H: 'H'
    };
    // 备用的二维码API列表
    this.qrApiList = [
      {
        name: 'osch<PERSON>',
        url: 'https://tool.oschina.net/action/qrcode/generate',
        params: (text, size) => `?data=${encodeURIComponent(text)}&output=image%2Fgif&error=L&type=0&margin=0&size=4&${Date.now()}`
      }
    ];
    this.currentApiIndex = 0;
    this.maxRetries = 3;
  }

  // 生成二维码
  make(options) {
    const {
      canvasId,
      componentInstance,
      text,
      size = 200,
      margin = 10,
      backgroundColor = '#ffffff',
      foregroundColor = '#000000',
      success,
      fail,
      useImageMode = false // 新增参数，是否使用image模式
    } = options;

    try {
      if (useImageMode) {
        // 直接返回二维码URL，不使用canvas绘制
        return this.getQRCodeUrl(text, size);
      } else {
        // 使用在线API生成二维码并绘制到canvas
        this.generateWithAPI(text, canvasId, componentInstance, size, success, fail);
      }
    } catch (error) {
      console.error('二维码生成失败:', error);
      if (fail) fail(error);
    }
  }

  // 获取二维码URL（带重试机制）
  getQRCodeUrl(text, size = 200) {
    try {
      // 使用当前API生成URL
      const api = this.qrApiList[this.currentApiIndex];
      const qrUrl = api.url + api.params(text, size);
      console.log(`使用 ${api.name} 生成二维码URL:`, qrUrl);
      return qrUrl;
    } catch (error) {
      console.error('生成二维码URL失败:', error);
      return '';
    }
  }

  // 使用在线API生成二维码（带重试和多API支持）
  generateWithAPI(text, canvasId, componentInstance, size, success, fail, retryCount = 0) {
    try {
      const ctx = uni.createCanvasContext(canvasId, componentInstance);
      
      // 获取当前API的二维码URL
      const qrUrl = this.getQRCodeUrl(text, size);
      
      if (!qrUrl) {
        if (fail) fail(new Error('无法生成二维码URL'));
        return;
      }

      // 设置超时时间
      const timeoutId = setTimeout(() => {
        console.log('二维码下载超时，尝试重试');
        this.handleDownloadFailure(text, canvasId, componentInstance, size, success, fail, retryCount);
      }, 10000); // 10秒超时

      // 下载二维码图片并绘制到canvas
      uni.downloadFile({
        url: qrUrl,
        timeout: 10000, // 设置10秒超时
        success: (res) => {
          clearTimeout(timeoutId);
          
          if (res.statusCode === 200 && res.tempFilePath) {
            console.log('二维码下载成功，开始绘制');
            try {
              // 绘制图片到canvas
              ctx.drawImage(res.tempFilePath, 0, 0, size, size);
              ctx.draw(false, () => {
                console.log('二维码绘制完成');
                if (success) success({ canvasId });
              });
            } catch (drawError) {
              console.error('绘制二维码失败:', drawError);
              this.handleDownloadFailure(text, canvasId, componentInstance, size, success, fail, retryCount);
            }
          } else {
            console.error('下载二维码失败，状态码:', res.statusCode);
            this.handleDownloadFailure(text, canvasId, componentInstance, size, success, fail, retryCount);
          }
        },
        fail: (err) => {
          clearTimeout(timeoutId);
          console.error('下载二维码失败:', err);
          this.handleDownloadFailure(text, canvasId, componentInstance, size, success, fail, retryCount);
        }
      });

    } catch (error) {
      console.error('生成二维码失败:', error);
      this.handleDownloadFailure(text, canvasId, componentInstance, size, success, fail, retryCount);
    }
  }

  // 处理下载失败的情况
  handleDownloadFailure(text, canvasId, componentInstance, size, success, fail, retryCount) {
    if (retryCount < this.maxRetries) {
      console.log(`二维码下载失败，准备重试 (${retryCount + 1}/${this.maxRetries})`);
      
      // 如果当前API失败，尝试下一个API
      if (retryCount > 0) {
        this.currentApiIndex = (this.currentApiIndex + 1) % this.qrApiList.length;
        console.log(`切换到备用API: ${this.qrApiList[this.currentApiIndex].name}`);
      }
      
      // 延迟重试，避免频繁请求
      setTimeout(() => {
        this.generateWithAPI(text, canvasId, componentInstance, size, success, fail, retryCount + 1);
      }, 1000 * (retryCount + 1)); // 递增延迟：1秒、2秒、3秒
    } else {
      console.error('所有重试尝试均失败，使用备用方案');
      // 重置API索引供下次使用
      this.currentApiIndex = 0;
      
      // 尝试生成简单的文本提示
      this.drawFallbackQRCode(text, canvasId, componentInstance, size, success, fail);
    }
  }

  // 绘制备用二维码（显示错误信息和链接）
  drawFallbackQRCode(text, canvasId, componentInstance, size, success, fail) {
    try {
      const ctx = uni.createCanvasContext(canvasId, componentInstance);
      
      // 设置背景
      ctx.setFillStyle('#f8f8f8');
      ctx.fillRect(0, 0, size, size);
      
      // 绘制边框
      ctx.setStrokeStyle('#ddd');
      ctx.setLineWidth(2);
      ctx.strokeRect(1, 1, size - 2, size - 2);
      
      // 绘制错误提示文本
      ctx.setFillStyle('#666');
      ctx.setFontSize(14);
      ctx.setTextAlign('center');
      
      const centerX = size / 2;
      const centerY = size / 2;
      
      ctx.fillText('二维码生成失败', centerX, centerY - 20);
      ctx.fillText('请复制链接使用', centerX, centerY);
      ctx.fillText('或刷新重试', centerX, centerY + 20);
      
      ctx.draw(false, () => {
        console.log('备用二维码绘制完成');
        if (success) success({ canvasId, fallback: true });
      });
    } catch (error) {
      console.error('备用二维码绘制失败:', error);
      if (fail) fail(error);
    }
  }

  // 重置API索引（供外部调用，用于重新开始尝试）
  resetApiIndex() {
    this.currentApiIndex = 0;
  }
}

// 创建实例
const qrCodeGenerator = new UQRCode();

export default qrCodeGenerator;
