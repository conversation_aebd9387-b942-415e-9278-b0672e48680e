<template>
  <view class="sister-business-page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 顶部背景区域 -->
    <view class="header-background-section">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航栏 -->
        <view class="header-section">
          <view class="navbar">
            <text class="nav-title">到店业务</text>
          </view>
        </view>

        <!-- 标签栏 -->
        <view class="tab-section">
          <scroll-view class="tab-container" scroll-x>
            <view class="tab-wrapper">
              <view v-for="(tab, i) in tabs" :key="i" class="tab-item" :class="{ active: currentTab === i }" @click="handleTabClick(i)">
                <text class="tab-text">{{ tab }}</text>
                <view v-if="currentTab === i" class="tab-line"></view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 订单 -->
      <scroll-view scroll-y class="main-content" v-if="currentTab === 0" @scrolltolower="onLoadMore">



      <!-- 统计卡片区域 -->
      <view class="filter-tabs w10 bg-w">
        <view class="filter-row">
          <view class="filter-item" :class="{ active: activeFilter === 'all' }" @tap="setActiveFilter('all')">
            <view class="filter-count">{{ statistics.total }}</view>
            <view class="filter-name">全部线索</view>
          </view>
          <view class="filter-item" :class="{ active: activeFilter === 'pending' }" @tap="setActiveFilter('pending')">
            <view class="filter-count">{{ statistics.pending }}</view>
            <view class="filter-name">待跟进线索</view>
          </view>
          <view class="filter-item" :class="{ active: activeFilter === 'processing' }" @tap="setActiveFilter('processing')">
            <view class="filter-count">{{ statistics.processing }}</view>
            <view class="filter-name">跟进中潜客</view>
          </view>
          <view class="filter-item" :class="{ active: activeFilter === 'completed' }" @tap="setActiveFilter('completed')">
            <view class="filter-count">{{ statistics.completed }}</view>
            <view class="filter-name">已成交客户</view>
          </view>
        </view>

        <view class="filter-row">
          <view class="filter-item" :class="{ active: activeTimeFilter === 'all' }" @tap="setActiveTimeFilter('all')">
            <view class="filter-name">全部线索</view>
          </view>
          <view class="filter-item" :class="{ active: activeTimeFilter === 'nofollow3' }" @tap="setActiveTimeFilter('nofollow3')">
            <view class="filter-name">3天未跟进</view>
          </view>
          <view class="filter-item" :class="{ active: activeTimeFilter === 'distribute3' }" @tap="setActiveTimeFilter('distribute3')">
            <view class="filter-name">近3天分配</view>
          </view>
          <view class="filter-item" :class="{ active: activeTimeFilter === 'todayContact' }" @tap="setActiveTimeFilter('todayContact')">
            <view class="filter-name">今日联系</view>
          </view>
        </view>

        <view class="filter-row">
          <view class="filter-item" :class="{ active: activeTimeFilter === 'todayInput' }" @tap="setActiveTimeFilter('todayInput')">
            <view class="filter-name">今日录入</view>
          </view>
          <view class="filter-item" :class="{ active: activeTimeFilter === 'weekInput' }" @tap="setActiveTimeFilter('weekInput')">
            <view class="filter-name">本周录入</view>
          </view>
          <view class="filter-item" :class="{ active: activeTimeFilter === 'monthInput' }" @tap="setActiveTimeFilter('monthInput')">
            <view class="filter-name">本月录入</view>
          </view>
          <view class="filter-item" :class="{ active: activeTimeFilter === 'lastMonthInput' }" @tap="setActiveTimeFilter('lastMonthInput')">
            <view class="filter-name">上月录入</view>
          </view>
        </view>
      </view>

      <!-- 搜索和筛选 -->
      <view class="search-section">
        <view class="search-box">
          <u-icon name="search" size="16" color="#999"></u-icon>
          <input
            class="search-input"
            v-model="searchKeyword"
            placeholder="搜索客户姓名、手机号"
            @input="onSearchInput"
            @confirm="performSearch"
          />
          <view class="search-clear" v-if="searchKeyword" @click="clearSearch">
            <u-icon name="close-circle" size="14" color="#999"></u-icon>
          </view>
        </view>
      </view>

      <!-- 三嫂订单列表 -->
      <view class="order-list-container">
        <view class="order-list">
          <view
            class="order-item"
            :class="{
              'sold-out': order.shared_status === 3,
              'shared': order.shared_status && order.shared_status !== 3
            }"
            v-for="(order, index) in orderList"
            :key="index"
            @click="handleOrderClick(order)"
          >
            <!-- 卡片头部：标题 + 状态标签 -->
            <view class="card-header">
              <view class="header-left">
                <text class="main-title">{{ order.aunt_name || '家政服务' }}</text>
                <text class="sub-title">{{ order.source_name || getSourceName(order.source) }} | {{ order.name || '未命名客户' }}</text>
              </view>
              <view class="header-right">
                <!-- 共享状态标记 -->
                <view class="shared-badge" v-if="order.shared_status && order.shared_status !== 3">
                  <u-icon name="share" size="12" color="#fff"></u-icon>
                  <text class="shared-text">已共享</text>
                </view>
                <!-- 状态标签 -->
                <view class="status-tag" :class="{
                  'tag-pending': order.status === '1',
                  'tag-processing': order.status === '2',
                  'tag-completed': order.status === '3',
                  'tag-cancelled': order.status === '4',
                  'tag-sold-out': isOrderSoldToOthers(order),
                  'tag-purchased': order.shared_status === 3 && !isOrderSoldToOthers(order)
                }">
                  {{ getOrderStatusText(order) }}
                </view>
              </view>
            </view>

            <!-- 卡片内容：备注和跟进信息 -->
            <view class="card-content">
              <!-- 备注信息 -->
              <view class="info-row" v-if="order.requirements || order.work_demands">
                <text class="info-label">备注：</text>
                <text class="info-text">{{ order.requirements || order.work_demands }}</text>
              </view>

              <!-- 跟进信息 -->
              <view class="info-row" v-if="order.sales_owner_name">
                <text class="info-label">跟进：</text>
                <text class="info-text">负责人{{ order.sales_owner_name }}，请及时跟进</text>
              </view>

              <!-- 已售出信息 -->
              <view class="info-row sold-info" v-if="order.shared_status === 3">
                <text class="info-label">售出价格：</text>
                <text class="info-text price-text">¥{{ order.shared_commission || '0' }}</text>
              </view>

            </view>

            <!-- 底部时间信息 -->
            <view class="card-time">
              <text class="time-text">{{ formatUpdateTime(order.update_time) }} {{ order.latest_follow_user_name || order.sales_owner_name || '系统' }}更新</text>
            </view>

            <!-- 标签区域 -->
            <view class="card-tags" v-if="order.tags && order.tags.length > 0">
              <view
                class="tag-item"
                v-for="tag in order.tags"
                :key="tag.id"
                :style="{ backgroundColor: tag.color || '#1890FF' }"
              >
                <text class="tag-text">{{ tag.name }}</text>
              </view>
            </view>

            <!-- 底部操作按钮 -->
            <view class="card-actions" v-if="!isOrderSoldToOthers(order)">
              <view class="action-btn outline" @click.stop="assignOrder(order)">
                <text>分配给</text>
              </view>
              <view class="action-btn outline" @click.stop="addFollow(order)">
                <text>写跟进</text>
              </view>
              <view class="action-btn outline" @click.stop="shareToSquare(order)" v-if="!order.shared_status">
                <text>共享</text>
              </view>
              <view class="action-btn primary" @click.stop="handleCall(order.mobile)">
                <text>打电话</text>
              </view>
            </view>

            <!-- 已售出给其他门店状态的操作按钮 -->
            <view class="card-actions sold-actions" v-if="isOrderSoldToOthers(order)">
              <view class="sold-notice">
                <text>该线索已被其他门店购买，无法进行操作</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 初始加载状态 -->
        <view class="loading-state" v-if="(loading || refreshing) && orderList.length === 0">
          <u-loading-icon mode="circle" size="40" color="#fdd118"></u-loading-icon>
          <text class="loading-text">正在加载线索...</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && !loadingMore && !refreshing && orderList.length === 0">
          <u-icon name="list" size="60" color="#ccc"></u-icon>
          <text class="empty-text">暂无三嫂线索</text>
        </view>

        <!-- 加载更多状态 -->
        <view class="loading-more" v-if="loadingMore">
          <u-loading-icon mode="flower" color="#007aff"></u-loading-icon>
          <text class="loading-text">加载中...</text>
        </view>
      </view>

        <!-- 底部安全区域 -->
        <view class="safe-area-bottom"></view>
      </scroll-view>

      <!-- 合同 -->
      <scroll-view scroll-y class="main-content" v-if="currentTab === 1" @scrolltolower="onContractLoadMore">
        <ContractList ref="contractComponent" :userStoreUuid="userStoreUuid" />
      </scroll-view>
    </view>

    <!-- 销售归属员工选择弹窗 -->
    <sales-attribution-selector
      :show="showSalesAttributionSelector"
      :leadId="currentSelectedOrder ? (currentSelectedOrder.id || currentSelectedOrder.uuid) : ''"
      type="sales"
      @success="handleSalesAttributionSuccess"
      @close="handleAttributionSelectorClose"
    />

    <!-- 添加线索浮动按钮 - 只在线索页面显示 -->
    <view class="floating-btn-container" v-if="currentTab === 0">
      <view class="add-lead-floating-btn" @tap="handleAddLeadClick">
        <u-icon name="plus" size="24" color="#fff"></u-icon>
      </view>
    </view>
  </view>
</template>

<script>
import { getSisterBusinessList } from '@/api/sister-business.js';
import { userInfo } from '@/api/user.js';
import { shareStoreLeadToDemandSquare } from '@/api/demand-square.js';
import { mapState } from 'vuex';
import ContractList from './contract.vue';
import SalesAttributionSelector from '@/components/sales-attribution-selector/sales-attribution-selector.vue';

export default {
  components: {
    ContractList,
    SalesAttributionSelector
  },
  data() {
    return {
      // 标签页相关
      currentTab: 0,
      tabs: ['线索', '合同'],

      // 页面状态
      loading: false,
      refreshing: false,
      loadingMore: false,

      // 筛选相关
      activeFilter: 'all', // 默认选中"全部"，这样可以看到所有数据
      activeTimeFilter: 'all', // 默认选中"全部线索"

      // 统计数据
      statistics: {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        cancelled: 0,
        signed: 0
      },

      // 分页数据
      currentPage: 1,
      pageSize: 100, // 使用API允许的最大值，一次加载更多数据
      totalCount: 0,
      totalPages: 0,

      // 搜索相关
      searchKeyword: '',
      searchTimer: null, // 搜索防抖定时器

      // 线索列表数据
      orderList: [],
      originalOrderList: [], // 保存原始数据，用于统计计算

      // 用户信息
      userStoreUuid: '',

      // 员工选择弹窗相关
      showSalesAttributionSelector: false,
      currentSelectedOrder: null, // 当前选择的订单
    };
  },
  computed: {
    ...mapState(['StatusBar'])
  },
  mounted() {
    this.initUserInfo();
  },

  onShow() {
    console.log('三嫂业务页面显示，当前tab:', this.currentTab);

    // 检查是否需要刷新销售归属信息
    this.checkAndRefreshSalesAttribution();

    // 每次页面显示时刷新当前标签页的数据
    if (this.userStoreUuid) {
      if (this.currentTab === 0) {
        this.loadOrderList(true);
      } else if (this.currentTab === 1) {
        this.$nextTick(() => {
          if (this.$refs.contractComponent) {
            this.$refs.contractComponent.refreshData();
          }
        });
      }
    }
  },

  beforeDestroy() {
    // 清理搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },

  methods: {
    // 标签页切换处理
    handleTabClick(i) {
      // 如果点击的是当前已选中的标签，不做任何操作
      if (this.currentTab === i) return;

      // 更新当前标签
      this.currentTab = i;

      // 如果点击的是"线索"标签，刷新线索列表
      if (i === 0) {
        this.$nextTick(() => {
          if (this.userStoreUuid) {
            this.loadOrderList(true);
          }
        });
      }

      // 如果点击的是"合同"标签，刷新合同列表
      if (i === 1) {
        this.$nextTick(() => {
          if (this.$refs.contractComponent) {
            this.$refs.contractComponent.refreshData();
          }
        });
      }
    },

    // 初始化用户信息
    async initUserInfo() {
      try {
        console.log('获取用户信息...');
        const userInfoResult = await userInfo();
        console.log('用户信息:', userInfoResult);
        
        if (userInfoResult && userInfoResult.user && userInfoResult.user.store_uuid) {
          this.userStoreUuid = userInfoResult.user.store_uuid;
          console.log('获取到store_uuid:', this.userStoreUuid);
          // 获取到store_uuid后加载订单列表
          this.loadOrderList(true);
        } else {
          console.error('未获取到store_uuid');
          uni.showToast({
            title: '获取门店信息失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        uni.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    },

    // 加载订单列表
    async loadOrderList(refresh = false) {
      if (!this.userStoreUuid) {
        console.log('store_uuid为空，无法加载订单列表');
        return;
      }

      if (refresh) {
        this.currentPage = 1;
        this.refreshing = true;
      } else {
        this.loading = true;
      }

      try {
        console.log('加载三嫂订单列表，页码:', this.currentPage, '筛选条件:', this.activeFilter, this.activeTimeFilter);

        const params = {
          store_uuid: this.userStoreUuid,
          page: this.currentPage,
          size: 100, // 使用API允许的最大值
          keyword: this.searchKeyword
          // 移除 exclude_status，恢复显示已成交客户
        };

        const response = await getSisterBusinessList(params);
        console.log('订单列表响应:', response);

        if (response && response.list) {
          // 保存原始数据
          if (refresh) {
            this.originalOrderList = response.list;
          } else {
            this.originalOrderList = [...this.originalOrderList, ...response.list];
          }

          // 前端筛选逻辑
          let filteredList = this.applyFilters(response.list);

          if (refresh) {
            this.orderList = filteredList;
          } else {
            this.orderList = [...this.orderList, ...filteredList];
          }

          this.totalCount = response.total || 0;
          this.totalPages = Math.ceil(this.totalCount / this.pageSize);

          // 更新统计信息
          this.updateStatistics(response.statistics || this.calculateStatistics(this.originalOrderList));
        }
      } catch (error) {
        console.error('加载订单列表失败:', error);
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.refreshing = false;
        this.loadingMore = false;
      }
    },

    // 应用筛选条件
    applyFilters(list) {
      console.log('应用筛选条件，原始数据:', list.length, '条');
      console.log('当前筛选条件 - 状态:', this.activeFilter, '时间:', this.activeTimeFilter);

      let filteredList = [...list];

      // 先应用时间筛选
      filteredList = this.applyTimeFilter(filteredList);

      // 再应用状态筛选
      filteredList = this.applyStatusFilter(filteredList);

      console.log('最终筛选结果:', filteredList.length, '条');
      return filteredList;
    },

    // 应用时间筛选
    applyTimeFilter(list) {
      if (this.activeTimeFilter === 'all') {
        return [...list];
      }

      const beforeTimeFilter = list.length;
      const now = new Date();
      const filteredList = list.filter(item => {
        const createTime = new Date(item.create_time);
        const updateTime = new Date(item.update_time);

        switch (this.activeTimeFilter) {
          case 'nofollow3':
            // 3天未跟进
            const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
            return updateTime < threeDaysAgo;
          case 'distribute3':
            // 近3天分配
            const threeDaysFromNow = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
            return createTime >= threeDaysFromNow;
          case 'todayContact':
            // 今日联系
            return this.isSameDay(updateTime, now);
          case 'todayInput':
            // 今日录入
            return this.isSameDay(createTime, now);
          case 'weekInput':
            // 本周录入
            return this.isThisWeek(createTime);
          case 'monthInput':
            // 本月录入
            return this.isThisMonth(createTime);
          case 'lastMonthInput':
            // 上月录入
            return this.isLastMonth(createTime);
          default:
            return true;
        }
      });

      console.log('时间筛选后:', beforeTimeFilter, '->', filteredList.length);
      return filteredList;
    },

    // 应用状态筛选
    applyStatusFilter(list) {
      if (this.activeFilter === 'all') {
        return [...list];
      }

      const beforeFilter = list.length;
      const filteredList = list.filter(item => {
        switch (this.activeFilter) {
          case 'pending':
            return item.status === '1'; // 待跟进
          case 'processing':
            return item.status === '2'; // 跟进中
          case 'completed':
            return item.status === '3'; // 已成交
          case 'cancelled':
            return item.status === '4'; // 已取消
          default:
            return true;
        }
      });

      console.log('状态筛选后:', beforeFilter, '->', filteredList.length);
      return filteredList;
    },

    // 计算统计信息
    calculateStatistics(list) {
      const stats = {
        total: list.length,
        pending: 0,
        processing: 0,
        completed: 0,
        cancelled: 0,
        signed: 0
      };

      list.forEach(item => {
        switch (item.status) {
          case '1':
            stats.pending++;
            break;
          case '2':
            stats.processing++;
            break;
          case '3':
            stats.completed++;
            break;
          case '4':
            stats.cancelled++;
            break;
          case '5':
            stats.signed++;
            break;
        }
      });

      return stats;
    },

    // 更新统计信息
    updateStatistics(stats) {
      if (stats) {
        this.statistics = {
          total: stats.total || 0,
          pending: stats.pending || 0,
          processing: stats.processing || 0,
          completed: stats.completed || 0,
          cancelled: stats.cancelled || 0,
          signed: stats.signed || 0
        };
      }

      // 重新计算当前筛选条件下的统计信息
      this.updateFilteredStatistics();
    },

    // 更新筛选后的统计信息
    updateFilteredStatistics() {
      if (!this.originalOrderList || this.originalOrderList.length === 0) {
        return;
      }

      // 应用时间筛选，但不应用状态筛选
      const timeFilteredList = this.applyTimeFilter(this.originalOrderList);

      // 计算各状态的数量
      const filteredStats = this.calculateStatistics(timeFilteredList);

      // 更新显示的统计信息
      this.statistics = filteredStats;

      console.log('更新筛选后统计信息:', this.statistics);
    },

    // 下拉刷新
    onRefresh() {
      this.loadOrderList(true);
    },

    // 上拉加载更多（线索）
    onLoadMore() {
      // 防止重复加载
      if (this.loading || this.loadingMore || this.refreshing) {
        return;
      }

      if (this.currentPage < this.totalPages) {
        this.loadingMore = true;
        this.currentPage++;
        this.loadOrderList();
      }
    },

    // 合同页面上拉加载更多
    onContractLoadMore() {
      if (this.$refs.contractComponent) {
        this.$refs.contractComponent.onLoadMore();
      }
    },

    // 搜索相关方法
    onSearchInput() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置防抖，500ms后执行搜索
      this.searchTimer = setTimeout(() => {
        this.performSearch();
      }, 500);
    },

    performSearch() {
      // 重置页码并加载数据
      this.currentPage = 1;
      this.loadOrderList(true);
    },

    clearSearch() {
      this.searchKeyword = '';
      // 清空搜索后重新加载数据
      this.currentPage = 1;
      this.loadOrderList(true);
    },

    // 筛选相关方法
    setActiveFilter(filter) {
      console.log('切换状态筛选:', filter);
      this.activeFilter = filter;

      // 重新筛选数据
      if (this.originalOrderList && this.originalOrderList.length > 0) {
        // 应用新的筛选条件
        this.orderList = this.applyFilters(this.originalOrderList);
      } else {
        // 如果没有原始数据，重新加载
        this.currentPage = 1;
        this.loadOrderList(true);
      }

      // 显示筛选提示
      const filterNames = {
        'all': '全部线索',
        'pending': '待跟进线索',
        'processing': '跟进中潜客',
        'completed': '已成交客户',
        'cancelled': '失效公共池'
      };

      if (filterNames[filter]) {
        uni.showToast({
          title: `已切换到${filterNames[filter]}`,
          icon: 'none',
          duration: 1500
        });
      }
    },

    setActiveTimeFilter(filter) {
      console.log('切换时间筛选:', filter);
      this.activeTimeFilter = filter;

      // 重新筛选数据
      if (this.originalOrderList && this.originalOrderList.length > 0) {
        // 应用新的筛选条件
        this.orderList = this.applyFilters(this.originalOrderList);

        // 更新统计信息（基于时间筛选后的数据）
        this.updateFilteredStatistics();
      } else {
        // 如果没有原始数据，重新加载
        this.currentPage = 1;
        this.loadOrderList(true);
      }

      // 显示筛选提示
      const filterNames = {
        'all': '全部线索',
        'nofollow3': '3天未跟进',
        'distribute3': '近3天分配',
        'todayContact': '今日联系',
        'todayInput': '今日录入',
        'weekInput': '本周录入',
        'monthInput': '本月录入',
        'lastMonthInput': '上月录入'
      };

      if (filterNames[filter]) {
        uni.showToast({
          title: `已切换到${filterNames[filter]}`,
          icon: 'none',
          duration: 1500
        });
      }
    },



    // 线索操作方法
    handleOrderClick(order) {
      // 如果是已售出的线索，需要区分是否为自己购买
      if (order.shared_status === 3) {
        // 检查是否为自己门店购买的线索
        const userInfo = uni.getStorageSync('userInfo');
        if (userInfo && userInfo.store_uuid && order.grab_store_uuid === userInfo.store_uuid) {
          // 自己门店购买的线索，允许查看详情
          this.navigateToDetail(order);
        } else {
          // 其他门店购买的线索，不允许查看
          uni.showToast({
            title: '该线索已被其他门店购买，无法查看详情',
            icon: 'none'
          });
        }
        return;
      }

      // 正常跳转到线索详情页面
      this.navigateToDetail(order);
    },

    navigateToDetail(order) {
      // 跳转到线索详情页面
      uni.navigateTo({
        url: `/pages-sister-business/detail?id=${order.id || order.uuid || 1}`
      });
    },

    // 共享线索到家政人广场（修复：防止重复操作）
    shareToSquare(order) {
      // 🔥 修复：检查是否已经共享或正在共享中
      if (order.shared_status === 1) {
        uni.showToast({
          title: '该线索已经共享过了',
          icon: 'none'
        });
        return;
      }

      if (order.is_sharing) {
        uni.showToast({
          title: '正在共享中，请稍候',
          icon: 'none'
        });
        return;
      }

      uni.showModal({
        title: '共享线索',
        content: '',  // 🔥 修复：清空默认内容，避免需要手动删除
        editable: true,
        placeholderText: '请输入佣金金额（元）',
        success: (res) => {
          if (res.confirm && res.content) {
            const commission = parseFloat(res.content);
            if (isNaN(commission) || commission <= 0) {
              uni.showToast({
                title: '请输入有效的佣金金额',
                icon: 'none'
              });
              return;
            }
            this.confirmShareToSquare(order, commission);
          }
        }
      });
    },

    // 确认共享线索（修复：完善状态管理和错误处理）
    async confirmShareToSquare(order, commission) {
      try {
        // 🔥 修复：设置共享中状态，防止重复操作
        this.setOrderSharingStatus(order, true);

        uni.showLoading({ title: '共享中...' });

        const response = await shareStoreLeadToDemandSquare({
          customer_uuid: order.uuid,
          commission_amount: commission,
          expire_hours: 48
        });

        uni.hideLoading();

        if (response && response.success !== false) {
          uni.showToast({
            title: '共享成功',
            icon: 'success'
          });
          // 更新当前线索的共享状态，避免重复刷新整个列表
          this.updateOrderShareStatus(order, response);
        } else {
          // 🔥 修复：共享失败时清除共享中状态
          this.setOrderSharingStatus(order, false);
          uni.showToast({
            title: response?.message || '共享失败，请重试',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        // 🔥 修复：异常时清除共享中状态
        this.setOrderSharingStatus(order, false);
        console.error('共享线索失败:', error);
        uni.showToast({
          title: '共享失败，请重试',
          icon: 'none'
        });
      }
    },

    // 更新订单共享状态（修复：防止重复操作和状态不一致）
    updateOrderShareStatus(order, shareResponse) {
      // 在原始数据中更新
      const originalIndex = this.originalOrderList.findIndex(item => item.uuid === order.uuid);
      if (originalIndex !== -1) {
        this.originalOrderList[originalIndex].shared_status = 1; // 已共享状态
        this.originalOrderList[originalIndex].shared_commission = shareResponse.commission_amount;
        this.originalOrderList[originalIndex].demand_uuid = shareResponse.demand_uuid; // 🔥 修复：记录需求UUID
        this.originalOrderList[originalIndex].is_sharing = false; // 🔥 修复：清除共享中状态
      }

      // 在显示数据中更新
      const displayIndex = this.orderList.findIndex(item => item.uuid === order.uuid);
      if (displayIndex !== -1) {
        this.orderList[displayIndex].shared_status = 1;
        this.orderList[displayIndex].shared_commission = shareResponse.commission_amount;
        this.orderList[displayIndex].demand_uuid = shareResponse.demand_uuid; // 🔥 修复：记录需求UUID
        this.orderList[displayIndex].is_sharing = false; // 🔥 修复：清除共享中状态
      }

      console.log('已更新线索共享状态:', order.uuid, '需求UUID:', shareResponse.demand_uuid);
    },

    // 🔥 新增：设置订单共享中状态的辅助方法
    setOrderSharingStatus(order, isSharing) {
      // 在原始数据中更新
      const originalIndex = this.originalOrderList.findIndex(item => item.uuid === order.uuid);
      if (originalIndex !== -1) {
        this.originalOrderList[originalIndex].is_sharing = isSharing;
      }

      // 在显示数据中更新
      const displayIndex = this.orderList.findIndex(item => item.uuid === order.uuid);
      if (displayIndex !== -1) {
        this.orderList[displayIndex].is_sharing = isSharing;
      }
    },

    handleCall(mobile) {
      if (!mobile) {
        uni.showToast({
          title: '客户未填写手机号',
          icon: 'none'
        });
        return;
      }
      
      uni.makePhoneCall({
        phoneNumber: mobile,
        fail: (err) => {
          console.error('拨打电话失败:', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },

    handleOrderDetail(order) {
      // 查看线索详情
      this.navigateToDetail(order);
    },

    // 复制微信号码
    copyWechatNumber(wechatNumber) {
      if (!wechatNumber) {
        uni.showToast({
          title: '微信号为空',
          icon: 'none'
        });
        return;
      }

      uni.setClipboardData({
        data: wechatNumber,
        success: () => {
          uni.showToast({
            title: '微信号已复制',
            icon: 'success'
          });
        },
        fail: (err) => {
          console.error('复制微信号失败:', err);
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },

    // 获取状态显示文字
    getStatusText(status) {
      const statusMap = {
        '1': '待跟进',
        '2': '跟进中',
        '3': '已成交',
        '4': '已取消',
        '5': '已签约'
      };
      return statusMap[status] || '未知';
    },

    // 获取客户来源名称（修复：统一来源映射逻辑）
    getSourceName(sourceId) {
      const sourceMap = {
        '1': '线上推广',
        '2': '线下推广',
        '3': '老客户介绍',
        '4': '广告投放',
        '5': '门店咨询',
        '6': '电话咨询',
        '7': '其他渠道',
        '8': '微网站',
        '9': '广场抢单',
        '-1': '客户转介绍',
        '-2': '公司400电话',
        '-3': '系统分配',
        '-4': '公司指派'
      };
      return sourceMap[sourceId] || '其他渠道';
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 格式化更新时间
    formatUpdateTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `更新: ${year}-${month}-${day} ${hours}:${minutes}`;
    },

    // 判断是否为最近7天内更新
    isRecentUpdate(dateString) {
      if (!dateString) return false;

      const updateDate = new Date(dateString);
      const now = new Date();
      const diffTime = now.getTime() - updateDate.getTime();
      const diffDays = diffTime / (1000 * 60 * 60 * 24);

      // 7天内的更新视为最近更新
      return diffDays <= 7;
    },

    // 获取跟进天数文本
    getFollowDaysText(updateTime) {
      if (!updateTime) return '未跟进';

      const updateDate = new Date(updateTime);
      const now = new Date();
      const diffTime = now.getTime() - updateDate.getTime();
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        return '今日跟进';
      } else if (diffDays === 1) {
        return '1天未跟进';
      } else if (diffDays <= 7) {
        return `${diffDays}天未跟进`;
      } else {
        return '长期未跟进';
      }
    },



    // 分配订单 - 显示销售归属选择弹窗
    assignOrder(order) {
      console.log('分配订单给销售人员:', order);

      if (!order.id && !order.uuid) {
        uni.showToast({
          title: '订单信息不完整',
          icon: 'none'
        });
        return;
      }

      // 保存当前选择的订单
      this.currentSelectedOrder = order;

      // 显示销售归属人选择弹窗
      this.showSalesAttributionSelector = true;
    },

    // 销售归属选择成功处理
    handleSalesAttributionSuccess(data) {
      console.log('销售归属设置成功:', data);

      if (!this.currentSelectedOrder) {
        return;
      }

      // 更新当前选择的订单数据
      this.currentSelectedOrder.sales_owner_name = data.staff.name;
      this.currentSelectedOrder.sales_owner_uuid = data.staff.uuid;

      // 更新列表中对应的订单数据
      const orderIndex = this.orderList.findIndex(order =>
        (order.id || order.uuid) === (this.currentSelectedOrder.id || this.currentSelectedOrder.uuid)
      );
      if (orderIndex !== -1) {
        this.orderList[orderIndex].sales_owner_name = data.staff.name;
        this.orderList[orderIndex].sales_owner_uuid = data.staff.uuid;
      }

      // 清空当前选择的订单
      this.currentSelectedOrder = null;
    },

    // 归属选择弹窗关闭处理
    handleAttributionSelectorClose() {
      this.showSalesAttributionSelector = false;
      this.currentSelectedOrder = null;
    },

    // 添加跟进 - 跳转到线索详情的跟进记录页面并自动弹出添加跟进表单
    addFollow(order) {
      uni.navigateTo({
        url: `/pages-sister-business/detail?id=${order.id || order.uuid}&tab=1&autoAdd=true`
      });
    },

    // 日期比较辅助方法
    isSameDay(date1, date2) {
      return date1.getFullYear() === date2.getFullYear() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getDate() === date2.getDate();
    },

    isThisWeek(date) {
      const now = new Date();
      const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
      startOfWeek.setHours(0, 0, 0, 0);
      return date >= startOfWeek;
    },

    isThisMonth(date) {
      const now = new Date();
      return date.getFullYear() === now.getFullYear() &&
             date.getMonth() === now.getMonth();
    },

    isLastMonth(date) {
      const now = new Date();
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1);
      return date.getFullYear() === lastMonth.getFullYear() &&
             date.getMonth() === lastMonth.getMonth();
    },

    // 检查并刷新销售归属信息
    checkAndRefreshSalesAttribution() {
      try {
        const app = getApp();
        if (app && app.globalData && app.globalData.needRefreshCustomer) {
          const refreshData = app.globalData.needRefreshCustomer;
          console.log('检测到销售归属更新，需要刷新列表:', refreshData);

          // 清除全局刷新标记
          app.globalData.needRefreshCustomer = null;

          // 刷新订单列表
          if (this.userStoreUuid && this.currentTab === 0) {
            console.log('刷新三嫂业务列表');
            this.loadOrderList(true);
          }
        }
      } catch (error) {
        console.log('检查销售归属刷新标记失败:', error);
      }
    },

    // 添加线索按钮点击处理
    handleAddLeadClick() {
      console.log('点击添加线索按钮');
      // 跳转到新建线索页面
      uni.navigateTo({
        url: '/pages-sister-business/create-lead'
      });
    },

    // 判断线索是否被其他门店购买
    isOrderSoldToOthers(order) {
      if (order.shared_status !== 3) {
        return false; // 未售出
      }

      // 使用新的lead_source_type字段判断
      if (order.lead_source_type === 'purchased') {
        return false; // 自己购买的线索，不算"售出给其他门店"
      } else {
        return true; // 自己发布但被他人购买的线索
      }
    },

    // 获取订单状态文本
    getOrderStatusText(order) {
      if (order.shared_status === 3) {
        // 已售出状态，区分自己购买和他人购买
        if (this.isOrderSoldToOthers(order)) {
          return '已售出';
        } else {
          return '已购买';
        }
      }

      // 其他状态显示跟进天数
      return this.getFollowDaysText(order.update_time);
    }
  }
};
</script>

<style lang="scss" scoped>
.sister-business-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 顶部背景区域
.header-background-section {
  flex-shrink: 0;
  position: relative;
  padding-bottom: 20rpx;
  margin-bottom: 0;
  // 添加状态栏安全区域
  padding-top: calc(var(--status-bar-height) + 20rpx);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 20rpx;
  }
}

// 顶部导航区域
.header-section {
  padding-top: 0;
}

.navbar {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  position: relative;
}

.nav-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  width: 100%;
}

.tab-section {
  position: relative;
  width: 100%;
}

.tab-container {
  display: flex;
  background-color: transparent;
  padding: 20rpx 0;
  width: 100%;

  .tab-wrapper {
    display: flex;
    width: 100%;
    justify-content: space-evenly;
    padding: 0 20rpx;
  }

  .tab-item {
    position: relative;
    padding: 20rpx 0;
    flex: 1;
    text-align: center;

    .tab-text {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 500;
    }

    &.active .tab-text {
      color: #ffffff;
      font-weight: 600;
    }

    .tab-line {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 8rpx;
      background-color: #ffffff;
      border-radius: 6rpx;
    }
  }

  &::-webkit-scrollbar {
    display: none;
  }
}

.content {
  flex: 1;
  height: 0; // 重要：确保 flex 子元素能够正确计算高度
  overflow: hidden; // 防止内容溢出

  .main-content {
    height: 100%;
  }
}



// 统计卡片区域
.filter-tabs {
  padding: 0 0;
  background: #fff;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 20rpx;
  border-top: 2rpx solid #f5f6fa;

  &:first-child {
    border-top: none;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  margin: 0 10rpx;
  padding: 10rpx 0;
  border-radius: 8rpx;
  transition: all 0.3s;

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }

  &.active {
    background-color: #fdd118;

    .filter-count {
      color: #fff;
    }

    .filter-name {
      color: #fff;
      font-weight: bold;
    }
  }

  .filter-count {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }

  .filter-name {
    font-size: 24rpx;
    color: #666;
    margin-top: 6rpx;
  }
}

// 搜索区域
.search-section {
  padding: 24rpx 32rpx; // 统一上下padding为24rpx
  background: transparent;

  .search-box {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 50rpx;
    padding: 20rpx 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(10rpx);

    .search-input {
      flex: 1;
      margin-left: 16rpx;
      font-size: 28rpx;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }

    .search-clear {
      margin-left: 16rpx;
      padding: 8rpx;
    }
  }
}



// 订单列表容器
.order-list-container {
  background: #f8f8f8;
  min-height: calc(100vh - 400rpx);
  border-radius: 32rpx 32rpx 0 0;
  padding: 32rpx 32rpx 0;
  margin-top: 0; // 移除顶部margin，让间距完全由搜索区域的padding控制
}

// 订单列表
.order-list {
  .order-item {
    background: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    }

    // 卡片头部
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16rpx;

      .header-left {
        flex: 1;

        .main-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          line-height: 1.3;
          margin-bottom: 8rpx;
          display: block;
        }

        .sub-title {
          font-size: 26rpx;
          color: #666;
          line-height: 1.3;
          display: block;
        }
      }

      .header-right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 8rpx;
      }

      .shared-badge {
        display: flex;
        align-items: center;
        gap: 6rpx;
        padding: 6rpx 16rpx;
        background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        border-radius: 16rpx;
        box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.3);

        .shared-text {
          font-size: 22rpx;
          color: #fff;
          font-weight: 700;
          line-height: 1;
          letter-spacing: 1rpx;
        }
      }

      .status-tag {
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        font-size: 24rpx;
        font-weight: 500;
        white-space: nowrap;

        &.tag-pending {
          background: #fff2e8;
          color: #ff6b35;
        }

        &.tag-processing {
          background: #e8f4ff;
          color: #007aff;
        }

        &.tag-completed {
          background: #e8f5e8;
          color: #34c759;
        }

        &.tag-cancelled {
          background: #f5f5f5;
          color: #8e8e93;
        }

        &.tag-sold-out {
          background: #ffe8e8;
          color: #ff4757;
          font-weight: 600;
        }
      }
    }

    // 已共享线索样式
    &.shared {
      position: relative;
      border-left: 8rpx solid #fdd118;
      background: linear-gradient(135deg, rgba(253, 209, 24, 0.05) 0%, rgba(255, 128, 27, 0.05) 100%);

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 16rpx rgba(253, 209, 24, 0.15);
      }
    }

    // 已售出线索样式
    &.sold-out {
      opacity: 0.6;
      background: #f8f8f8;
      border: 2rpx dashed #ddd;

      .main-title,
      .sub-title,
      .info-text {
        color: #999 !important;
      }

      &:active {
        transform: none;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
      }
    }

    // 卡片内容
    .card-content {
      margin-bottom: 16rpx;

      .info-row {
        margin-bottom: 12rpx;
        line-height: 1.5;

        .info-label {
          font-size: 26rpx;
          color: #666;
          display: inline;
        }

        .info-text {
          font-size: 26rpx;
          color: #333;
          display: inline;
        }

        &.sold-info {
          .price-text {
            color: #ff4757;
            font-weight: 600;
            font-size: 28rpx;
          }
        }
      }

      .phone-number {
        font-size: 32rpx;
        color: #ff6b35;
        font-weight: 600;
        margin-top: 8rpx;
        cursor: pointer;
      }
    }

    // 底部时间信息
    .card-time {
      margin-bottom: 20rpx;

      .time-text {
        font-size: 24rpx;
        color: #999;
        line-height: 1.3;
      }
    }

    // 标签区域
    .card-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;
      margin-bottom: 20rpx;

      .tag-item {
        padding: 6rpx 16rpx;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: 200rpx;

        .tag-text {
          font-size: 22rpx;
          color: #fff;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    // 底部操作按钮
    .card-actions {
      display: flex;
      gap: 16rpx;

      .action-btn {
        flex: 1;
        height: 72rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8rpx;
        font-size: 26rpx;
        font-weight: 500;
        transition: all 0.3s ease;

        &.outline {
          background: #fff;
          border: 2rpx solid #e5e5e5;
          color: #666;

          &:active {
            background: #f8f8f8;
            border-color: #d1d1d1;
          }
        }

        &.primary {
          background: #fdd118;
          color: #333;
          border: 2rpx solid #fdd118;

          &:active {
            background: #e6bc15;
            border-color: #e6bc15;
          }
        }

        text {
          font-size: 26rpx;
          font-weight: 500;
        }
      }
    }

    // 已售出状态的操作区域
    .sold-actions {
      .sold-notice {
        text-align: center;
        padding: 16rpx;
        background: #fff2f0;
        border-radius: 8rpx;
        border: 1rpx solid #ffccc7;

        text {
          font-size: 26rpx;
          color: #ff4d4f;
        }
      }
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: calc(100vh - 500rpx);
  padding: 120rpx 0;

  .empty-text {
    display: block;
    margin-top: 24rpx;
    font-size: 28rpx;
    color: #999;
  }
}

// 初始加载状态
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
  }
}

// 加载更多状态
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;

  .loading-text {
    margin-left: 16rpx;
    font-size: 26rpx;
    color: #999;
  }
}

// 底部安全区域
.safe-area-bottom {
  height: 120rpx;
}

// 通用样式类
.w10 {
  width: 100%;
}

.bg-w {
  background-color: #fff;
}

// 添加线索浮动按钮容器
.floating-btn-container {
  position: fixed;
  bottom: 30px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
}

// 添加线索浮动按钮
.add-lead-floating-btn {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(253, 209, 24, 0.4);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #e6c015 0%, #e6721a 100%);
    box-shadow: 0 4px 16px rgba(253, 209, 24, 0.5);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(253, 209, 24, 0.5);
  }
}
</style>
