<template>
  <view class="page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 头部 -->
    <view class="header-section">
      <view class="header-background"></view>
      <view class="header-content">
        <view class="navbar">
          <view class="nav-left" @click="goBack">
            <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
          </view>
          <text class="nav-title">销售项目</text>
          <view class="nav-right"></view>
        </view>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ totalProducts }}</text>
          <text class="stats-label">总项目数</text>
        </view>
        <view class="stats-item">
          <text class="stats-number">{{ totalCategories }}</text>
          <text class="stats-label">服务分类</text>
        </view>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-box">
        <u-icon name="search" size="18" color="#999"></u-icon>
        <input
          class="search-input"
          placeholder="搜索项目名称或分类"
          v-model="searchKeyword"
          @input="handleSearch"
        />
        <view class="search-clear" v-if="searchKeyword" @click="clearSearch">
          <u-icon name="close-circle-fill" size="16" color="#ccc"></u-icon>
        </view>
      </view>
    </view>

    <!-- 分类筛选 -->
    <view class="category-section" v-if="categoryList.length > 0">
      <scroll-view class="category-scroll" scroll-x>
        <view class="category-list">
          <view
            class="category-item"
            :class="{ active: selectedCategory === '全部' }"
            @click="handleCategoryChange('全部')"
          >
            <u-icon name="grid" size="16" :color="selectedCategory === '全部' ? '#fdd118' : '#999'"></u-icon>
            <text>全部</text>
          </view>
          <view
            class="category-item"
            :class="{ active: selectedCategory === category }"
            @click="handleCategoryChange(category)"
            v-for="category in categoryList"
            :key="category"
          >
            <u-icon name="grid" size="16" :color="selectedCategory === category ? '#fdd118' : '#999'"></u-icon>
            <text>{{ category }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 产品列表 -->
    <scroll-view
      class="product-list"
      scroll-y
      :style="{ height: listHeight }"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <!-- 加载状态 -->
      <view class="loading-container" v-if="loading">
        <u-loading-icon mode="circle" color="#fdd118" size="24"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-container" v-else-if="!loading && filteredProductList.length === 0">
        <u-empty text="暂无销售项目" mode="list"></u-empty>
      </view>

      <!-- 网格产品列表 -->
      <view class="grid-product-list" v-else>
        <view
          class="grid-product-item"
          v-for="(product, index) in filteredProductList"
          :key="index"
          @click="viewProductDetail(product)"
        >
          <view class="product-card">
            <view class="product-header">
              <view class="product-icon">
                <u-icon name="bag" size="20" color="#fdd118"></u-icon>
              </view>
              <view class="product-category-tag">
                {{ product.service_skill_name || product.service_skill_main_name || '其他服务' }}
              </view>
            </view>
            <view class="product-content">
              <view class="product-name">{{ product.product_name || product.name }}</view>
              <view class="product-price" v-if="product.sku_info && product.sku_info.now_price">
                <text class="price-symbol">¥</text>
                <text class="price-value">{{ product.sku_info.now_price }}</text>
                <text class="price-unit">{{ product.sku_info.type_price_unit || '/次' }}</text>
              </view>
              <view class="product-price-placeholder" v-else>
                <text class="price-text">价格面议</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getPurchasableProductsWithCategories } from '@/api/product.js'

export default {
  name: 'SalesProjects',
  data() {
    return {
      loading: false,
      refreshing: false,
      productList: [],
      filteredProductList: [],
      selectedCategory: '全部',
      categoryList: [],
      searchKeyword: '',
      listHeight: 'calc(100vh - 480rpx)'
    }
  },
  computed: {
    ...mapState(['StatusBar', 'staffInfo']),
    ...mapGetters(['getCurrentSelectedCompany']),

    totalProducts() {
      return this.productList.length
    },

    totalCategories() {
      return this.categoryList.length
    }
  },
  onLoad() {
    this.loadProductList()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },

    async loadProductList() {
      try {
        this.loading = true

        const staffInfo = this.$store.state.staffInfo
        if (!staffInfo || !staffInfo.mobile) {
          throw new Error('员工信息不存在，请重新登录')
        }

        const selectedCompany = this.$store.getters.getCurrentSelectedCompany
        if (!selectedCompany) {
          throw new Error('请先选择公司')
        }

        // 使用门店产品API，确保显示门店支持的产品（与代客预约页面保持一致）
        const result = await getPurchasableProductsWithCategories({
          page: 1,
          size: 1000  // 获取所有产品
        })

        if (result && result.categories) {
          this.processCategorizedData(result.categories)
        } else if (result && result.products) {
          // 如果返回的是产品列表格式，转换为分类格式
          this.processProductsData(result.products)
        } else {
          this.productList = []
          this.filteredProductList = []
          this.categoryList = []
        }
      } catch (error) {
        console.error('加载失败:', error)
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    processCategorizedData(categories) {
      const allProducts = []
      const categorySet = new Set()

      categories.forEach(category => {
        if (category.products && Array.isArray(category.products)) {
          category.products.forEach(product => {
            product.service_skill_name = category.name
            allProducts.push(product)
          })
          categorySet.add(category.name)
        }
      })

      this.productList = allProducts
      this.categoryList = Array.from(categorySet)
      this.updateFilteredProducts()
    },

    processProductsData(products) {
      // 处理产品列表格式的数据，按分类分组
      const categoryMap = new Map()
      const categorySet = new Set()

      products.forEach(product => {
        const categoryName = product.service_skill_name || product.service_skill_main_name || '其他服务'
        categorySet.add(categoryName)

        if (!categoryMap.has(categoryName)) {
          categoryMap.set(categoryName, [])
        }
        categoryMap.get(categoryName).push(product)
      })

      // 转换为分类格式
      const categories = Array.from(categoryMap.entries()).map(([name, products]) => ({
        name,
        products
      }))

      this.processCategorizedData(categories)
    },

    updateFilteredProducts() {
      let filtered = this.productList

      // 分类筛选
      if (this.selectedCategory !== '全部') {
        filtered = filtered.filter(product => {
          const categoryName = product.service_skill_name || product.service_skill_main_name || '其他服务'
          return categoryName === this.selectedCategory
        })
      }

      // 搜索筛选
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.trim().toLowerCase()
        filtered = filtered.filter(product => {
          const productName = (product.product_name || product.name || '').toLowerCase()
          const categoryName = (product.service_skill_name || product.service_skill_main_name || '其他服务').toLowerCase()
          return productName.includes(keyword) || categoryName.includes(keyword)
        })
      }

      this.filteredProductList = filtered
    },

    handleSearch() {
      this.updateFilteredProducts()
    },

    clearSearch() {
      this.searchKeyword = ''
      this.updateFilteredProducts()
    },

    handleCategoryChange(category) {
      this.selectedCategory = category
      this.updateFilteredProducts()
    },

    viewProductDetail(product) {
      const productName = product.product_name || product.name
      const category = product.service_skill_name || product.service_skill_main_name || '其他服务'
      let content = `产品名称：${productName}\n服务分类：${category}`

      if (product.sku_info && product.sku_info.now_price) {
        content += `\n价格：¥${product.sku_info.now_price}${product.sku_info.type_price_unit || '/次'}`
      }

      uni.showModal({
        title: '产品详情',
        content: content,
        showCancel: false,
        confirmText: '知道了'
      })
    },

    async onRefresh() {
      this.refreshing = true
      try {
        await this.loadProductList()
      } finally {
        this.refreshing = false
      }
    }
  }
}
</script>

<style scoped>
.page {
  background: #f5f7fb;
  min-height: 100vh;
}

/* 头部样式 */
.header-section {
  position: relative;
  height: calc(180rpx + var(--status-bar-height));
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #fdd118 0%, #f5a623 100%);
}

.header-content {
  position: relative;
  z-index: 2;
  padding-top: var(--status-bar-height);
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 180rpx;
  padding: 0 32rpx;
}

.nav-left, .nav-right {
  width: 80rpx;
  display: flex;
  justify-content: center;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

/* 统计信息样式 */
.stats-section {
  margin: -60rpx 24rpx 24rpx;
  position: relative;
  z-index: 3;
}

.stats-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 600;
  color: #fdd118;
  line-height: 1;
}

.stats-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 分类筛选样式 */
.category-section {
  margin: 0 24rpx 24rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  gap: 16rpx;
  padding: 0 8rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #fff;
  border-radius: 12rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #fff9e6;
  border: 2rpx solid #fdd118;
}

.category-item text {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

.category-item.active text {
  color: #fdd118;
  font-weight: 500;
}

/* 搜索框样式 */
.search-section {
  margin: 0 24rpx 24rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12rpx;
  padding: 0 24rpx;
  height: 80rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.search-input {
  flex: 1;
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
}

.search-clear {
  margin-left: 16rpx;
  padding: 8rpx;
}

/* 网格产品列表样式 */
.grid-product-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 24rpx;
  justify-content: space-between;
}

.grid-product-item {
  width: calc(50% - 8rpx);
  margin-bottom: 16rpx;
  box-sizing: border-box;
}

.product-card {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.product-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.product-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 20rpx 0;
}

.product-icon {
  width: 48rpx;
  height: 48rpx;
  background: #fff9e6;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-category-tag {
  font-size: 20rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-content {
  padding: 16rpx 20rpx 24rpx;
}

.product-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  min-height: 76rpx;
}

.product-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 20rpx;
  color: #ff4757;
  font-weight: 500;
}

.price-value {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: 600;
  margin: 0 4rpx;
}

.price-unit {
  font-size: 20rpx;
  color: #999;
}

.product-price-placeholder {
  height: 32rpx;
  display: flex;
  align-items: center;
}

.price-text {
  font-size: 24rpx;
  color: #999;
}

.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}


</style>
