#!/usr/bin/env python3
"""
AI图片生成控制器
提供产品主图一键生成API接口
"""

import logging
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.service.ai_image_service import AIImageService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from utils.response_util import ResponseUtil
from exceptions.exception import BusinessException, ValidationException
from config.get_db import get_db

logger = logging.getLogger(__name__)

# 创建路由器
ai_image_controller = APIRouter(prefix="/api/v1/ai-image", tags=["AI图片生成"])


class GenerateImageRequest(BaseModel):
    """生成图片请求模型"""
    service_name: str = Field(..., min_length=1, max_length=100, description="服务名称/产品名称")


class GenerateImageResponse(BaseModel):
    """生成图片响应模型"""
    success: bool = Field(..., description="是否成功")
    image_url: str = Field(None, description="生成的图片URL")
    message: str = Field(..., description="结果消息")


@ai_image_controller.post("/generate-product-image", 
                         summary="生成产品主图", 
                         response_model=GenerateImageResponse)
async def generate_product_image(
    request_data: GenerateImageRequest,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    生成产品主图
    
    基于用户输入的产品名称，调用AI工作流生成对应的产品主图
    
    Args:
        request_data: 请求数据，包含服务名称
        db: 数据库会话
        current_user: 当前登录用户
        
    Returns:
        GenerateImageResponse: 生成结果
        
    Raises:
        HTTPException: 当生成失败时抛出异常
    """
    try:
        logger.info(f"用户 {current_user.user.id} 请求生成产品主图，服务名称: {request_data.service_name}")
        
        # 调用AI图片生成服务
        result = await AIImageService.generate_product_image(request_data.service_name)
        
        if result["success"]:
            logger.info(f"产品主图生成成功: {result['image_url']}")
            return ResponseUtil.success(
                data={
                    "success": True,
                    "image_url": result["image_url"],
                    "message": result["message"]
                },
                msg="图片生成成功"
            )
        else:
            logger.warning(f"产品主图生成失败: {result['message']}")
            return ResponseUtil.error(
                msg=result["message"],
                data={
                    "success": False,
                    "image_url": None,
                    "message": result["message"]
                }
            )
            
    except ValidationException as e:
        logger.warning(f"参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except BusinessException as e:
        logger.error(f"业务异常: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"生成产品主图异常: {str(e)}")
        return ResponseUtil.error(msg="图片生成失败，请稍后重试")


@ai_image_controller.post("/health", summary="健康检查")
async def health_check():
    """
    AI图片生成服务健康检查
    
    Returns:
        dict: 服务状态信息
    """
    try:
        return ResponseUtil.success(
            data={
                "service": "AI图片生成服务",
                "status": "healthy",
                "timestamp": "2025-07-30T12:00:00Z"
            },
            msg="服务正常"
        )
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return ResponseUtil.error(msg="服务异常")
