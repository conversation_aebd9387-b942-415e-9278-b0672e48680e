import { post, get } from '../utlis/require.js'

/**
 * 员工端开户申请相关API
 */

// 员工提交开户申请
export const submitStaffAccountApplication = (data) => {
  return post('/api/v1/staff/account/application/submit', data, { contentType: 'application/json' })
}

// 员工查询开户申请状态
export const getStaffApplicationStatus = () => {
  return get('/api/v1/staff/account/application/status')
}

// 员工获取开户申请详情
export const getStaffApplicationDetail = (uuid) => {
  return get('/api/v1/staff/account/application/detail', { uuid })
}

// 员工检查开户状态
export const checkStaffAccountStatus = () => {
  return get('/api/v1/staff/account/application/status/check')
}

// 员工身份证正面OCR识别（从临时文件）
export const recognizeStaffIdCardFrontFromTempFile = (tempFilePath) => {
  return post('/api/v1/staff/ocr/idcard/front-temp', { temp_file_path: tempFilePath }, { contentType: 'application/json' })
}

// 员工身份证背面OCR识别（从临时文件）
export const recognizeStaffIdCardBackFromTempFile = (tempFilePath) => {
  return post('/api/v1/staff/ocr/idcard/back-temp', { temp_file_path: tempFilePath }, { contentType: 'application/json' })
}

// 员工银行卡OCR识别（从临时文件）
export const recognizeStaffBankCardFromTempFile = (tempFilePath) => {
  return post('/api/v1/staff/ocr/bankcard-temp', { temp_file_path: tempFilePath }, { contentType: 'application/json' })
}

// 员工身份证正面OCR识别（从URL）
export const recognizeStaffIdCardFrontFromUrl = (imageUrl) => {
  return post('/api/v1/staff/ocr/idcard/front', { imageUrl }, { contentType: 'application/json' })
}

// 员工身份证背面OCR识别（从URL）
export const recognizeStaffIdCardBackFromUrl = (imageUrl) => {
  return post('/api/v1/staff/ocr/idcard/back', { imageUrl }, { contentType: 'application/json' })
}

// 员工银行卡OCR识别（从URL）
export const recognizeStaffBankCardFromUrl = (imageUrl) => {
  return post('/api/v1/staff/ocr/bankcard', { imageUrl }, { contentType: 'application/json' })
}
