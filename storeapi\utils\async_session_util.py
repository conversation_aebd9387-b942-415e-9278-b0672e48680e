"""
异步会话管理工具类
用于统一处理SQLAlchemy异步会话的上下文管理和错误处理
"""
import functools
from typing import Callable, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from utils.log_util import logger


class AsyncSessionUtil:
    """异步会话管理工具类"""
    
    @staticmethod
    async def ensure_session_active(db: AsyncSession) -> bool:
        """
        确保异步会话处于活跃状态
        
        :param db: 异步数据库会话
        :return: 会话是否活跃
        """
        try:
            await db.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"异步会话验证失败: {str(e)}")
            return False
    
    @staticmethod
    def safe_str(obj: Any) -> str:
        """
        安全地将对象转换为字符串，避免访问已失效的数据库对象
        
        :param obj: 要转换的对象
        :return: 字符串表示
        """
        try:
            if hasattr(obj, '__str__'):
                return str(obj)
            else:
                return repr(obj)
        except Exception:
            return f"<{type(obj).__name__} object>"
    
    @staticmethod
    def safe_exception_message(e: Exception) -> str:
        """
        安全地获取异常消息，避免访问可能已失效的数据库对象
        
        :param e: 异常对象
        :return: 安全的异常消息
        """
        try:
            # 尝试获取异常消息
            if hasattr(e, 'message') and e.message:
                return str(e.message)
            elif hasattr(e, 'args') and e.args:
                return str(e.args[0])
            else:
                return str(e)
        except Exception:
            # 如果获取异常消息失败，返回异常类型
            return f"{type(e).__name__}: 无法获取详细错误信息"


def async_db_operation(func: Callable) -> Callable:
    """
    异步数据库操作装饰器
    自动处理异步会话验证和安全异常处理
    
    :param func: 被装饰的异步函数
    :return: 装饰后的函数
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # 查找AsyncSession参数
        db_session = None
        for arg in args:
            if isinstance(arg, AsyncSession):
                db_session = arg
                break
        
        if not db_session:
            for value in kwargs.values():
                if isinstance(value, AsyncSession):
                    db_session = value
                    break
        
        # 如果找到数据库会话，验证其活跃状态
        if db_session:
            is_active = await AsyncSessionUtil.ensure_session_active(db_session)
            if not is_active:
                logger.warning(f"异步会话不活跃，方法: {func.__name__}")
        
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            # 安全地记录异常
            safe_error_msg = AsyncSessionUtil.safe_exception_message(e)
            logger.error(f"异步数据库操作异常 [{func.__name__}]: {safe_error_msg}")
            raise
    
    return wrapper


class SafeExceptionHandler:
    """安全异常处理器"""
    
    @staticmethod
    def handle_service_exception(e: Exception, operation_name: str) -> str:
        """
        处理服务异常，返回安全的错误消息
        
        :param e: 异常对象
        :param operation_name: 操作名称
        :return: 安全的错误消息
        """
        safe_msg = AsyncSessionUtil.safe_exception_message(e)
        logger.error(f"{operation_name}异常: {safe_msg}")
        return f"{operation_name}失败: {safe_msg}"
    
    @staticmethod
    def handle_login_exception(e: Exception, login_type: str) -> str:
        """
        处理登录异常，返回安全的错误消息
        
        :param e: 异常对象
        :param login_type: 登录类型
        :return: 安全的错误消息
        """
        safe_msg = AsyncSessionUtil.safe_exception_message(e)
        logger.error(f"{login_type}登录异常: {safe_msg}")
        return f"登录失败: {safe_msg}"


# 使用示例：
# 
# 1. 装饰器使用：
# @async_db_operation
# async def some_db_method(self, db: AsyncSession, ...):
#     # 方法会自动验证异步会话并安全处理异常
#     pass
#
# 2. 手动会话验证：
# if not await AsyncSessionUtil.ensure_session_active(db):
#     raise ServiceException("数据库会话不可用")
#
# 3. 安全异常处理：
# except Exception as e:
#     error_msg = SafeExceptionHandler.handle_service_exception(e, "操作名称")
#     raise ServiceException(message=error_msg)
