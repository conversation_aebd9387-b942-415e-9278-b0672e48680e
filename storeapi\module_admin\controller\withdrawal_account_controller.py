"""
提现账户管理控制器
"""
from fastapi import APIRouter, Depends, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any
from config.get_db import get_db
from utils.response_util import ResponseUtil
from exceptions.exception import BusinessException, ValidationException
from utils.log_util import logger
from module_admin.service.internal_user_login_service import InternalUserLoginService
from module_admin.service.withdrawal_account_service import WithdrawalAccountService

# 创建提现账户相关的API路由
withdrawal_account_controller = APIRouter(
    prefix="/api/v1/payment/withdrawal/accounts",
    tags=["提现账户管理"],
    dependencies=[Depends(InternalUserLoginService.get_current_user)]
)


@withdrawal_account_controller.get('', summary="获取提现账户列表")
async def get_withdrawal_accounts(
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取提现账户列表接口
    
    获取当前登录用户的提现账户信息列表
    
    Args:
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        提现账户信息列表
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 获取提现账户信息，公司: {company_uuid}")

        # 调用服务获取提现账户信息
        withdrawal_accounts = await WithdrawalAccountService.get_withdrawal_accounts_service(
            query_db, company_uuid
        )
        
        return ResponseUtil.success(
            msg="获取提现账户信息成功",
            data=withdrawal_accounts
        )
        
    except ValidationException as e:
        logger.error(f"获取提现账户信息参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"获取提现账户信息业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取提现账户信息系统异常: {str(e)}")
        return ResponseUtil.error(
            msg=f"获取提现账户信息失败: {str(e)}"
        )


@withdrawal_account_controller.get('/{account_id}', summary="获取提现账户详情")
async def get_withdrawal_account_detail(
    account_id: int,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """获取提现账户详情接口
    
    根据账户ID获取提现账户的详细信息
    
    Args:
        account_id: 账户ID
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        提现账户详情
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 获取账户 {account_id} 详情，公司: {company_uuid}")

        # 调用服务获取账户详情
        account_detail = await WithdrawalAccountService.get_withdrawal_account_detail_service(
            query_db, company_uuid, account_id
        )
        
        return ResponseUtil.success(
            msg="获取账户详情成功",
            data=account_detail
        )
        
    except ValidationException as e:
        logger.error(f"获取账户详情参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"获取账户详情业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"获取账户详情系统异常: {str(e)}")
        return ResponseUtil.error(
            msg=f"获取账户详情失败: {str(e)}"
        )


@withdrawal_account_controller.post('', summary="新增提现账户")
async def create_withdrawal_account(
    account_data: Dict[str, Any] = Body(..., description="提现账户数据"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """新增提现账户接口
    
    创建新的提现账户信息
    
    Args:
        account_data: 提现账户数据
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        新增账户结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id
        current_user_name = getattr(current_user.user, 'user_name', None) or getattr(current_user.user, 'name', None)

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        if not current_user_name:
            logger.warning("当前用户姓名获取失败")
            raise ValidationException(message="当前用户姓名获取失败")

        # 验证必要字段
        required_fields = ['accountName', 'accountType', 'bankName', 'bankAccount', 'accountHolder']
        for field in required_fields:
            if not account_data.get(field):
                raise ValidationException(message=f"缺少必要字段: {field}")

        # 验证账户类型
        account_type = account_data.get('accountType')
        if account_type not in [1, 2]:
            raise ValidationException(message="账户类型必须为1(企业对公)或2(个人)")

        logger.info(f"用户 {current_user_id} 新增提现账户，银行: {account_data.get('bankName')}，公司: {company_uuid}")

        # 调用服务创建提现账户
        account_result = await WithdrawalAccountService.create_withdrawal_account_service(
            query_db, company_uuid, current_user_id, current_user_name, account_data
        )
        
        return ResponseUtil.success(
            msg="新增提现账户成功",
            data=account_result
        )
        
    except ValidationException as e:
        logger.error(f"新增提现账户参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"新增提现账户业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"新增提现账户系统异常: {str(e)}")
        return ResponseUtil.error(
            msg=f"新增提现账户失败: {str(e)}"
        )


@withdrawal_account_controller.put('/{account_id}', summary="修改提现账户")
async def update_withdrawal_account(
    account_id: int,
    account_data: Dict[str, Any] = Body(..., description="提现账户数据"),
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """修改提现账户接口
    
    根据账户ID修改提现账户信息
    
    Args:
        account_id: 账户ID
        account_data: 提现账户数据
        current_user: 当前登录用户信息
        query_db: 数据库会话
        
    Returns:
        修改结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 修改提现账户 {account_id}，公司: {company_uuid}")

        # 调用服务修改提现账户
        account_result = await WithdrawalAccountService.update_withdrawal_account_service(
            query_db, company_uuid, current_user_id, account_id, account_data
        )
        
        return ResponseUtil.success(
            msg="修改提现账户成功",
            data=account_result
        )
        
    except ValidationException as e:
        logger.error(f"修改提现账户参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"修改提现账户业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"修改提现账户系统异常: {str(e)}")
        return ResponseUtil.error(
            msg=f"修改提现账户失败: {str(e)}"
        )


@withdrawal_account_controller.delete('/{account_id}', summary="删除提现账户")
async def delete_withdrawal_account(
    account_id: int,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """删除提现账户接口

    根据账户ID删除提现账户（软删除）

    Args:
        account_id: 账户ID
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        删除结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 删除提现账户 {account_id}，公司: {company_uuid}")

        # 调用服务删除提现账户
        delete_result = await WithdrawalAccountService.delete_withdrawal_account_service(
            query_db, company_uuid, current_user_id, account_id
        )

        return ResponseUtil.success(
            msg="删除提现账户成功",
            data=delete_result
        )

    except ValidationException as e:
        logger.error(f"删除提现账户参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"删除提现账户业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"删除提现账户系统异常: {str(e)}")
        return ResponseUtil.error(
            msg=f"删除提现账户失败: {str(e)}"
        )


@withdrawal_account_controller.post('/{account_id}/default', summary="设置默认提现账户")
async def set_default_withdrawal_account(
    account_id: int,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """设置默认提现账户接口

    将指定账户设置为默认提现账户

    Args:
        account_id: 账户ID
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        设置结果
    """
    try:
        # 获取当前用户信息
        current_user_id = getattr(current_user.user, 'uuid', None) or getattr(current_user.user, 'user_uuid', None)
        company_uuid = current_user.user.company_id

        if not current_user_id:
            logger.warning("当前用户ID获取失败")
            raise ValidationException(message="当前用户ID获取失败")

        if not company_uuid:
            logger.warning("当前用户没有关联的公司信息")
            raise ValidationException(message="用户没有关联的公司信息")

        logger.info(f"用户 {current_user_id} 设置默认提现账户 {account_id}，公司: {company_uuid}")

        # 调用服务设置默认账户
        set_result = await WithdrawalAccountService.set_default_account_service(
            query_db, company_uuid, account_id
        )

        return ResponseUtil.success(
            msg="设置默认账户成功",
            data=set_result
        )

    except ValidationException as e:
        logger.error(f"设置默认账户参数验证异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except BusinessException as e:
        logger.error(f"设置默认账户业务异常: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code)
    except Exception as e:
        logger.error(f"设置默认账户系统异常: {str(e)}")
        return ResponseUtil.error(
            msg=f"设置默认账户失败: {str(e)}"
        )
