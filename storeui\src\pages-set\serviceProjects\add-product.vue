<template>
  <view class="page">
    <appHead left fixed title="新建产品"></appHead>

    <scroll-view scroll-y style="height: calc(100vh - 88rpx);">
      <!-- 基本信息 -->
      <view class="section">
        <view class="section-title">基本信息</view>
        <view class="form-container">
          <!-- 产品名称 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>产品名称</text>
            </view>
            <input
              class="form-input"
              v-model="formData.productName"
              placeholder="请输入产品名称"
              maxlength="50"
            />
          </view>

          <!-- 服务分类 -->
          <view class="form-item clickable" @click="handleSkillClick">
            <view class="form-label">
              <text class="required">*</text>
              <text>服务分类</text>
            </view>
            <view class="form-value">
              <text class="value-text" :class="{ placeholder: !selectedSkill }">
                {{ selectedSkill ? selectedSkill.name : '请选择服务分类' }}
              </text>
              <u-icon name="arrow-right" size="16" color="#c8c9cc" />
            </view>
          </view>
        </view>
      </view>

      <!-- 购买设置 -->
      <view class="section">
        <view class="section-title">购买设置</view>
        <view class="form-container">
          <!-- 最低购买量 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>最低购买量</text>
            </view>
            <input
              class="form-input number-input"
              v-model.number="formData.minNumber"
              placeholder="请输入最低购买量"
              type="number"
              :min="1"
            />
          </view>

          <!-- 最高购买量 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>最高购买量</text>
            </view>
            <input
              class="form-input number-input"
              v-model.number="formData.maxNumber"
              placeholder="请输入最高购买量"
              type="number"
              :min="1"
            />
          </view>

          <!-- 可预约时间 -->
          <view class="form-item">
            <view class="form-label">
              <text>可预约时间</text>
            </view>
            <view class="form-value">
              <view class="time-range-simple">
                <picker
                  mode="time"
                  :value="formData.startTime"
                  @change="onStartTimeChange"
                  class="time-picker-simple"
                >
                  <text class="time-text">{{ formData.startTime }}</text>
                </picker>
                <text class="time-separator">-</text>
                <picker
                  mode="time"
                  :value="formData.endTime"
                  @change="onEndTimeChange"
                  class="time-picker-simple"
                >
                  <text class="time-text">{{ formData.endTime }}</text>
                </picker>
              </view>
              <u-icon name="arrow-right" size="14" color="#c8c9cc" />
            </view>
          </view>
        </view>
      </view>

      <!-- 详细信息 -->
      <view class="section">
        <view class="section-title">详细信息</view>
        <view class="form-container">
          <!-- 现价 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>现价</text>
            </view>
            <input
              class="form-input number-input"
              v-model.number="formData.nowPrice"
              placeholder="请输入现价"
              type="digit"
            />
          </view>

          <!-- 会员价 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>会员价</text>
            </view>
            <input
              class="form-input number-input"
              v-model.number="formData.vipPrice"
              placeholder="请输入会员价"
              type="digit"
            />
          </view>

          <!-- 服务时长 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>服务时长(分钟)</text>
            </view>
            <input
              class="form-input number-input"
              v-model.number="formData.duration"
              placeholder="请输入服务时长"
              type="number"
            />
          </view>

          <!-- 价格单位 -->
          <view class="form-item clickable" @click="showPriceUnitPicker = true">
            <view class="form-label">
              <text class="required">*</text>
              <text>价格单位</text>
            </view>
            <view class="form-value">
              <text class="value-text" :class="{ placeholder: !formData.priceUnit }">
                {{ formData.priceUnit || '请选择价格单位' }}
              </text>
              <u-icon name="arrow-right" size="14" color="#c8c9cc" />
            </view>
          </view>

          <!-- 提成类型 -->
          <view class="form-item clickable" @click="showCommissionTypePicker = true">
            <view class="form-label">
              <text class="required">*</text>
              <text>提成类型</text>
            </view>
            <view class="form-value">
              <text class="value-text" :class="{ placeholder: formData.commissionType === null }">
                {{ getCommissionTypeText() }}
              </text>
              <u-icon name="arrow-right" size="14" color="#c8c9cc" />
            </view>
          </view>

          <!-- 提成数值 -->
          <view class="form-item">
            <view class="form-label">
              <text class="required">*</text>
              <text>{{ getCommissionLabel() }}</text>
            </view>
            <view class="form-input-container">
              <input
                class="form-input number-input"
                v-model.number="formData.defineCommission"
                :placeholder="getCommissionPlaceholder()"
                type="digit"
              />
              <text class="input-unit">{{ getCommissionUnit() }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 图片设置 -->
      <view class="section">
        <view class="section-title">图片设置</view>
        <view class="form-container">
          <!-- 产品主图 -->
          <view class="form-item image-item">
            <view class="form-label-with-action">
              <view class="form-label">
                <text class="required">*</text>
                <text>产品主图</text>
              </view>
              <view class="generate-btn" :class="{ generating: generating }" @click="handleGenerateImage">
                <text class="generate-btn-text">{{ generating ? '正在生成中...' : '一键生成' }}</text>
              </view>
            </view>
            <view class="image-upload-container">
              <view v-if="formData.mainImage" class="image-preview main-image" @click="previewImage(formData.mainImage)">
                <image :src="formData.mainImage" mode="aspectFill" />
                <view class="image-delete" @click.stop="removeMainImage">
                  <u-icon name="close" size="16" color="#fff" />
                </view>
              </view>
              <view v-else class="upload-btn main-upload" @click="uploadMainImage">
                <u-icon name="plus" size="40" color="#c8c9cc" />
                <text class="upload-text">上传主图</text>
              </view>
            </view>
          </view>

          <!-- 产品详情图 -->
          <view class="form-item image-item">
            <view class="form-label">
              <text>产品详情图</text>
            </view>
            <view class="detail-images-container">
              <view class="image-upload-list">
                <view
                  v-for="(image, index) in formData.detailImages"
                  :key="index"
                  class="image-preview detail-image"
                  @click="previewImage(image)"
                >
                  <image :src="image" mode="aspectFill" />
                  <view class="image-delete" @click.stop="removeDetailImage(index)">
                    <u-icon name="close" size="16" color="#fff" />
                  </view>
                </view>
                <view v-if="formData.detailImages.length < 5" class="upload-btn detail-upload" @click="uploadDetailImage">
                  <u-icon name="plus" size="30" color="#c8c9cc" />
                  <text class="upload-text small">添加图片</text>
                </view>
              </view>
              <text class="form-tip">最多上传5张详情图</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部提交按钮 -->
      <view class="submit-section">
        <button class="submit-btn" @click="handleSubmit">
          {{ submitting ? '创建中...' : '创建产品' }}
        </button>
      </view>
    </scroll-view>

    <!-- 技能选择弹窗 -->
    <view v-if="showSkillPopup" class="popup-mask" @click="showSkillPopup = false">
      <view class="popup-content" @click.stop>
        <view class="popup-header">
          <text class="popup-title">选择服务分类</text>
          <view class="close-btn" @click="showSkillPopup = false">
            <u-icon name="close" size="20" color="#999" />
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-container">
          <view class="search-box">
            <u-icon name="search" size="16" color="#999" />
            <input
              class="search-input"
              v-model="searchKeyword"
              placeholder="搜索服务分类"
              @input="onSearchInput"
              confirm-type="search"
            />
            <view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
              <u-icon name="close-circle-fill" size="16" color="#ccc" />
            </view>
          </view>
        </view>

        <scroll-view scroll-y class="skill-list" style="max-height: 50vh;">
          <view v-if="loading" class="loading-text">加载中...</view>
          <view v-else-if="filteredSkillOptions.length === 0 && searchKeyword" class="empty-text">
            未找到匹配的服务分类
          </view>
          <view v-else-if="filteredSkillOptions.length === 0" class="empty-text">暂无可选择的服务分类</view>

          <view
            v-for="skill in filteredSkillOptions"
            :key="skill.id"
            class="skill-item"
            :class="{ active: selectedSkill && selectedSkill.id === skill.id }"
            @click="selectSkill(skill)"
          >
            <text class="skill-name">{{ skill.name }}</text>
            <view v-if="selectedSkill && selectedSkill.id === skill.id" class="check-icon">
              <u-icon name="checkmark" size="20" color="#fdd118" />
            </view>
          </view>
        </scroll-view>

        <view class="popup-footer">
          <button class="confirm-btn" @click="confirmSkillSelection">确定</button>
        </view>
      </view>
    </view>

    <!-- 价格单位选择弹窗 -->
    <view v-if="showPriceUnitPicker" class="popup-mask" @click="showPriceUnitPicker = false">
      <view class="popup-content" @click.stop>
        <view class="popup-header">
          <text class="popup-title">选择价格单位</text>
          <view class="close-btn" @click="showPriceUnitPicker = false">
            <u-icon name="close" size="20" color="#999" />
          </view>
        </view>

        <view class="unit-list">
          <view
            v-for="unit in priceUnits"
            :key="unit"
            class="unit-item"
            :class="{ active: formData.priceUnit === unit }"
            @click="selectPriceUnit(unit)"
          >
            <text class="unit-name">{{ unit }}</text>
            <view v-if="formData.priceUnit === unit" class="check-icon">
              <u-icon name="checkmark" size="20" color="#fdd118" />
            </view>
          </view>
        </view>

        <view class="popup-footer">
          <button class="confirm-btn" @click="confirmPriceUnitSelection">确定</button>
        </view>
      </view>
    </view>

    <!-- 提成类型选择弹窗 -->
    <view v-if="showCommissionTypePicker" class="popup-mask" @click="showCommissionTypePicker = false">
      <view class="popup-content" @click.stop>
        <view class="popup-header">
          <text class="popup-title">选择提成类型</text>
          <view class="close-btn" @click="showCommissionTypePicker = false">
            <u-icon name="close" size="20" color="#999" />
          </view>
        </view>

        <view class="unit-list">
          <view
            v-for="type in commissionTypes"
            :key="type.value"
            class="unit-item"
            :class="{ active: formData.commissionType === type.value }"
            @click="selectCommissionType(type.value)"
          >
            <view class="commission-type-info">
              <text class="unit-name">{{ type.label }}</text>
              <text class="type-desc">{{ type.desc }}</text>
            </view>
            <view v-if="formData.commissionType === type.value" class="check-icon">
              <u-icon name="checkmark" size="20" color="#fdd118" />
            </view>
          </view>
        </view>

        <view class="popup-footer">
          <button class="confirm-btn" @click="confirmCommissionTypeSelection">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getCompanySkills } from '../../api/skill.js'
import { post } from '../../utlis/require.js'
import { generateProductImage } from '../../api/ai-image.js'


export default {
  data() {
    return {
      showSkillPopup: false,
      showPriceUnitPicker: false,
      showCommissionTypePicker: false,
      skillOptions: [], // 技能选项列表
      filteredSkillOptions: [], // 过滤后的技能选项列表
      selectedSkill: null, // 选中的技能
      loading: false,
      submitting: false,
      generating: false, // 图片生成中状态
      searchKeyword: '', // 搜索关键词
      priceUnits: ['次', '人', '台'], // 价格单位选项
      commissionTypes: [ // 提成类型选项
        { value: 1, label: '固定金额', desc: '按固定金额计算提成' },
        { value: 0, label: '百分比', desc: '按销售额百分比计算提成' }
      ],
      formData: {
        productName: '', // 产品名称
        mainImage: '', // 产品主图URL
        mainImageId: null, // 产品主图ID
        detailImages: [], // 产品详情图URL列表
        detailImageIds: [], // 产品详情图ID列表
        minNumber: 1, // 最低购买量
        maxNumber: 10, // 最高购买量
        startTime: '08:00', // 开始时间
        endTime: '18:00', // 结束时间
        // 详细信息
        nowPrice: '', // 现价
        vipPrice: '', // 会员价
        duration: 60, // 服务时长(分钟)
        priceUnit: '', // 价格单位
        commissionType: null, // 提成类型：1-固定金额，0-百分比
        defineCommission: '' // 提成数值
      }
    }
  },

  computed: {
    // 是否可以提交
    canSubmit() {
      return this.formData.productName.trim() &&
             this.selectedSkill &&
             this.formData.minNumber >= 1 &&
             this.formData.maxNumber >= 1 &&
             this.formData.minNumber <= this.formData.maxNumber &&
             this.formData.nowPrice > 0 &&
             this.formData.vipPrice > 0 &&
             this.formData.duration > 0 &&
             this.formData.priceUnit &&
             this.formData.commissionType !== null &&
             this.formData.defineCommission >= 0 &&
             !this.submitting
    }
  },
  
  onLoad() {
    this.loadSkillOptions()
  },
  
  methods: {
    // 处理技能选择点击
    handleSkillClick() {
      console.log('点击技能选择，当前技能数量:', this.skillOptions.length)
      this.showSkillPopup = true
      // 重置搜索状态
      this.searchKeyword = ''
      this.filteredSkillOptions = [...this.skillOptions]
      console.log('弹窗状态:', this.showSkillPopup)
    },

    // 加载技能选项
    async loadSkillOptions() {
      try {
        this.loading = true
        const result = await getCompanySkills()
        this.skillOptions = result || []
        this.filteredSkillOptions = [...this.skillOptions] // 初始化过滤列表
        console.log('技能选项加载成功:', this.skillOptions)
      } catch (error) {
        console.error('加载技能选项失败:', error)
        uni.showToast({
          title: error.message || '加载技能选项失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 选择技能
    selectSkill(skill) {
      console.log('选择技能:', skill)
      this.selectedSkill = skill
    },
    
    // 确认技能选择
    confirmSkillSelection() {
      if (!this.selectedSkill) {
        uni.showToast({
          title: '请选择服务分类',
          icon: 'none'
        })
        return
      }

      this.showSkillPopup = false
      console.log('选中的技能:', this.selectedSkill)
    },

    // 搜索输入处理
    onSearchInput() {
      this.filterSkillOptions()
    },

    // 过滤技能选项
    filterSkillOptions() {
      if (!this.searchKeyword.trim()) {
        this.filteredSkillOptions = [...this.skillOptions]
      } else {
        const keyword = this.searchKeyword.toLowerCase()
        this.filteredSkillOptions = this.skillOptions.filter(skill =>
          skill.name.toLowerCase().includes(keyword)
        )
      }
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.filteredSkillOptions = [...this.skillOptions]
    },

    // 开始时间变化
    onStartTimeChange(e) {
      this.formData.startTime = e.detail.value
      console.log('开始时间:', this.formData.startTime)
    },

    // 结束时间变化
    onEndTimeChange(e) {
      this.formData.endTime = e.detail.value
      console.log('结束时间:', this.formData.endTime)
    },

    // 选择价格单位
    selectPriceUnit(unit) {
      this.formData.priceUnit = unit
      console.log('选择价格单位:', unit)
    },

    // 确认价格单位选择
    confirmPriceUnitSelection() {
      if (!this.formData.priceUnit) {
        uni.showToast({
          title: '请选择价格单位',
          icon: 'none'
        })
        return
      }
      this.showPriceUnitPicker = false
    },

    // 获取提成类型显示文本
    getCommissionTypeText() {
      if (this.formData.commissionType === null) {
        return '请选择提成类型'
      }
      const type = this.commissionTypes.find(t => t.value === this.formData.commissionType)
      return type ? type.label : '请选择提成类型'
    },

    // 获取提成标签
    getCommissionLabel() {
      return this.formData.commissionType === 1 ? '提成金额' : '提成比例'
    },

    // 获取提成占位符
    getCommissionPlaceholder() {
      if (this.formData.commissionType === 1) {
        return '请输入提成金额'
      } else if (this.formData.commissionType === 0) {
        return '请输入提成百分比'
      }
      return '请先选择提成类型'
    },

    // 获取提成单位
    getCommissionUnit() {
      if (this.formData.commissionType === 1) {
        return '元'
      } else if (this.formData.commissionType === 0) {
        return '%'
      }
      return ''
    },

    // 选择提成类型
    selectCommissionType(type) {
      this.formData.commissionType = type
      // 切换类型时清空之前的数值
      this.formData.defineCommission = ''
      console.log('选择提成类型:', type)
    },

    // 确认提成类型选择
    confirmCommissionTypeSelection() {
      if (this.formData.commissionType === null) {
        uni.showToast({
          title: '请选择提成类型',
          icon: 'none'
        })
        return
      }
      this.showCommissionTypePicker = false
    },

    // 一键生成图片处理函数
    async handleGenerateImage() {
      // 检查产品名称是否已输入
      const productName = this.formData.productName?.trim()
      if (!productName) {
        uni.showToast({
          title: '请先输入产品名称',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 检查产品名称长度
      if (productName.length < 2) {
        uni.showToast({
          title: '产品名称至少需要2个字符',
          icon: 'none',
          duration: 2000
        })
        return
      }

      if (productName.length > 50) {
        uni.showToast({
          title: '产品名称不能超过50个字符',
          icon: 'none',
          duration: 2000
        })
        return
      }

      // 防止重复点击
      if (this.generating) {
        return
      }

      try {
        this.generating = true

        console.log('开始生成产品主图，产品名称:', this.formData.productName.trim())

        // 调用AI图片生成接口
        const result = await generateProductImage({
          service_name: this.formData.productName.trim()
        })

        if (result && result.success && result.image_url) {
          // 生成成功，直接使用OBS图片URL（后端已经处理了下载和上传）
          this.formData.mainImage = result.image_url
          this.formData.mainImageId = null // AI生成的图片没有数据库ID

          uni.showToast({
            title: 'AI图片生成成功！',
            icon: 'success',
            duration: 2000
          })

          console.log('产品主图生成成功:', result.image_url)

          // 可选：震动反馈
          try {
            uni.vibrateShort()
          } catch (e) {
            // 忽略震动错误
          }
        } else {
          // 生成失败
          const errorMsg = (result && result.message) || '图片生成失败'
          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000
          })
          console.error('产品主图生成失败:', result)
        }

      } catch (error) {
        console.error('生成产品主图异常:', error)

        let errorMsg = '图片生成失败，请稍后重试'
        if (error.message) {
          if (error.message.includes('timeout') || error.message.includes('超时')) {
            errorMsg = 'AI生成超时，请稍后重试'
          } else if (error.message.includes('network') || error.message.includes('网络')) {
            errorMsg = '网络连接失败，请检查网络'
          } else if (error.message.includes('AI服务')) {
            errorMsg = 'AI服务暂时不可用，请稍后重试'
          } else if (error.message.includes('参数')) {
            errorMsg = '产品名称格式有误，请重新输入'
          } else {
            errorMsg = error.message
          }
        }

        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
      } finally {
        this.generating = false
      }
    },

    // 下载AI生成的图片并上传到OBS
    async downloadAndUploadImage(imageUrl) {
      try {
        console.log('开始下载AI生成的图片:', imageUrl)

        // 下载图片到本地临时文件
        const downloadResult = await new Promise((resolve, reject) => {
          uni.downloadFile({
            url: imageUrl,
            success: (res) => {
              if (res.statusCode === 200) {
                console.log('图片下载成功:', res.tempFilePath)
                resolve(res.tempFilePath)
              } else {
                reject(new Error(`下载失败，状态码: ${res.statusCode}`))
              }
            },
            fail: (error) => {
              console.error('图片下载失败:', error)
              reject(new Error('图片下载失败'))
            }
          })
        })

        // 上传到我们的服务器（OBS）
        const uploadResult = await this.uploadImageToServer(downloadResult, 'main')

        console.log('AI生成图片上传成功:', uploadResult)
        return {
          success: true,
          file_url: uploadResult.file_url,
          id: uploadResult.id,
          message: '图片保存成功'
        }

      } catch (error) {
        console.error('下载并上传图片失败:', error)
        return {
          success: false,
          file_url: null,
          id: null,
          message: error.message || '图片保存失败'
        }
      }
    },

    // 上传产品主图
    uploadMainImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          try {
            uni.showLoading({ title: '上传中...' })

            // 调用产品图片上传接口
            const uploadResult = await this.uploadImageToServer(res.tempFilePaths[0], 'main')
            this.formData.mainImage = uploadResult.file_url
            this.formData.mainImageId = uploadResult.id

            uni.hideLoading()
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            })
          } catch (error) {
            uni.hideLoading()
            console.error('上传图片失败:', error)
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            })
          }
        },
        fail: (error) => {
          console.error('选择图片失败:', error)
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    // 移除产品主图
    removeMainImage() {
      this.formData.mainImage = ''
    },

    // 上传产品详情图
    uploadDetailImage() {
      const remainCount = 5 - this.formData.detailImages.length
      uni.chooseImage({
        count: remainCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          try {
            uni.showLoading({ title: '上传中...' })

            // 批量上传详情图
            for (let i = 0; i < res.tempFilePaths.length; i++) {
              const uploadResult = await this.uploadImageToServer(
                res.tempFilePaths[i],
                'detail'
              )
              this.formData.detailImages.push(uploadResult.file_url)
              this.formData.detailImageIds.push(uploadResult.id)
            }

            uni.hideLoading()
            uni.showToast({
              title: '上传成功',
              icon: 'success'
            })
          } catch (error) {
            uni.hideLoading()
            console.error('上传图片失败:', error)
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            })
          }
        },
        fail: (error) => {
          console.error('选择图片失败:', error)
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          })
        }
      })
    },

    // 移除详情图
    removeDetailImage(index) {
      this.formData.detailImages.splice(index, 1)
      this.formData.detailImageIds.splice(index, 1)
    },

    // 上传图片到服务器
    async uploadImageToServer(filePath, imageType) {
      try {
        // 获取当前产品名称，如果没有则使用默认值
        const productName = this.formData.productName.trim() || '新产品'

        console.log('开始上传图片:', { filePath, imageType, productName })

        // 根据图片类型生成remark
        const remark = imageType === 'main'
          ? `产品主图-${productName}`
          : `产品详情图-${productName}`

        // 使用带token验证的文件上传接口，会创建数据库记录并获取当前用户信息
        const uploadResult = await this.uploadFileWithAuth(filePath, {
          showLoading: false, // 外层已经有loading了
          showToast: false,   // 外层会处理错误提示
          route: 'product',   // 指定文件路径为产品相关
          remark: remark      // 指定备注格式
        })

        if (uploadResult.success) {
          console.log('上传成功:', uploadResult)

          // 返回数据库ID和图片URL，格式与原接口保持一致
          return {
            file_url: uploadResult.url,
            id: uploadResult.data.id || uploadResult.fileInfo.id || 1 // 数据库记录ID
          }
        } else {
          throw new Error(uploadResult.message || '上传失败')
        }
      } catch (error) {
        console.error('上传图片失败:', error)
        throw error
      }
    },

    // 带token验证的文件上传方法（专门用于产品图片上传）
    uploadFileWithAuth(filePath, options = {}) {
      return new Promise((resolve, reject) => {
        // 默认配置
        const uploadConfig = {
          showLoading: true,
          loadingTitle: '上传中...',
          showToast: true,
          route: null,
          remark: null,
          ...options
        }

        // 获取配置的基础URL
        let baseUrl
        try {
          baseUrl = this.$store.state.baseUrl || uni.getStorageSync('baseUrl')
          if (!baseUrl) {
            const config = require('../../setConfig.js')
            baseUrl = config.host
          }
        } catch (error) {
          console.warn('获取baseUrl失败', error)
          baseUrl = 'https://your-api-domain.com'
        }

        // 构建formData
        const formData = {}
        if (uploadConfig.route) {
          formData.route = uploadConfig.route
        }
        if (uploadConfig.remark) {
          formData.remark = uploadConfig.remark
        }

        // 获取token
        const token = this.$store.state.token || uni.getStorageSync('token')

        uni.uploadFile({
          url: `${baseUrl}/api/v1/file/upload`, // 使用需要token验证的文件上传接口
          filePath: filePath,
          name: 'file',
          formData: formData,
          header: {
            'Authorization': `Bearer ${token}`
          },
          success: (uploadRes) => {
            try {
              const result = JSON.parse(uploadRes.data)

              if (result.code === 200 && result.data) {
                // 构造统一的文件信息对象
                const fileInfo = {
                  id: result.data.id,
                  file_name: result.data.file_name || filePath.split('/').pop(),
                  file_url: result.data.file_url,
                  file_size: result.data.file_size || 0,
                  file_type: result.data.file_type || 'unknown'
                }

                const successResult = {
                  success: true,
                  url: result.data.file_url,
                  data: result.data,
                  fileInfo: fileInfo
                }

                resolve(successResult)
              } else {
                const errorMsg = result.message || '上传失败'
                reject(new Error(errorMsg))
              }
            } catch (error) {
              reject(new Error('解析上传结果失败'))
            }
          },
          fail: (error) => {
            reject(new Error('网络请求失败'))
          }
        })
      })
    },

    // 预览图片
    previewImage(current) {
      const urls = this.formData.mainImage ? [this.formData.mainImage, ...this.formData.detailImages] : this.formData.detailImages
      uni.previewImage({
        current,
        urls
      })
    },

    // 提交表单
    async handleSubmit() {
      if (!this.canSubmit) {
        return
      }

      // 检查主图是否上传
      if (!this.formData.mainImage) {
        uni.showToast({
          title: '请先上传产品主图',
          icon: 'none'
        })
        return
      }

      try {
        this.submitting = true

        // 构建提交数据
        const submitData = {
          product_name: this.formData.productName.trim(),
          service_skill_id: this.selectedSkill.id,
          service_skill_name: this.selectedSkill.name,
          service_skill_main_id: this.selectedSkill.parent_id,
          main_image_id: this.formData.mainImageId,
          main_image_url: this.formData.mainImage, // 添加主图URL，用于AI生成的图片
          detail_image_ids: this.formData.detailImageIds,
          min_number: this.formData.minNumber,
          max_number: this.formData.maxNumber,
          buy_notes: `购买须知：线下支付
        为了保证您的权益，平台所有服务均以在线订单为凭证，私下交易无法享受权益保障。

        预定服务
        预定服务需提前24小时,且在购买之日起,30天内使用完毕,逾期无效不可再服务。

        是否支持修改/取消服务
        因服务人员上门路途较远，若您在服务开始前2小时内取消/修改服务，会造成服务人员没有订单，您需支付50元退改费并可以保留本次服务权益,希望您谅解，如有不便，请提前操作哦。

        无法开始服务
        因停水或您个人原因导致无法服务,您只需支付50元,并可保留本次服务权益。`,
          buy_agreement: `为保障您的权益，下单前请在线预览并勾选本协议：

我们提供专业家庭保洁服务，持证保洁员100%通过平台安全培训，使用环保认证清洁剂，标准化流程覆盖全屋深度清洁。服务包含：厨房油污分解、卫浴除菌、玻璃增亮、家具养护等12项基础服务，另可定制母婴级深度清洁套餐。

选择我们，您将享受：
✅ 服务前免费上门评估，方案透明无隐形消费
✅ 保洁过程中可随时在线沟通调整细节
✅ 贴心记录家庭特殊需求（如宠物区消毒、过敏源清洁）
✅ 完成后30分钟内免费复扫不满意部位
✅ 专属客服全程跟进，购买商业责任保险

现在注册即赠首次服务体验券，点击预约即享24小时极速响应！让专业与温暖，为您的家焕新呼吸`,
          business_hours: `${this.formData.startTime}-${this.formData.endTime}`,
          // SKU信息
          sku_info: {
            name: this.formData.productName.trim(), // 使用产品名称作为SKU名称
            now_price: parseFloat(this.formData.nowPrice),
            vip_price: parseFloat(this.formData.vipPrice),
            duration: parseInt(this.formData.duration),
            type_price_unit: this.formData.priceUnit,
            define_commission: parseFloat(this.formData.defineCommission),
            commission_type: this.formData.commissionType // 使用选择的提成类型
          }
        }

        console.log('提交数据:', submitData)

        // 调用创建产品API
        await post('/api/v1/product/create', submitData, {
          contentType: 'application/json'
        })

        uni.showToast({
          title: '产品创建成功',
          icon: 'success'
        })

        // 通知上一页刷新数据
        uni.$emit('productCreated')

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('创建产品失败:', error)
        uni.showToast({
          title: error.message || '创建失败',
          icon: 'none'
        })
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.page {
  background: #f8f9fa;
  min-height: 100vh;
}

/* 分组样式 */
.section {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  padding: 24rpx 32rpx 16rpx;
  font-weight: 500;
}

.form-container {
  background: #fff;
  margin: 0 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 144rpx;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item.clickable {
  cursor: pointer;
}

.form-item.clickable:active {
  background-color: #f8f9fa;
}

.form-item.image-item {
  align-items: flex-start;
  flex-direction: column;
  padding: 32rpx;
}

.form-label {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  min-width: 160rpx;
}

.form-label-with-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 24rpx;
}

.generate-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #fdd118 0%, #f4c430 100%);
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.25);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(253, 209, 24, 0.3);
}

.generate-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.4);
  background: linear-gradient(135deg, #e6c115 0%, #d4a017 100%);
}

.generate-btn.generating {
  background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
  pointer-events: none;
  opacity: 0.8;
}

.generate-btn-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.required {
  color: #ff4757;
  margin-right: 8rpx;
  font-size: 32rpx;
}

.form-input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  text-align: right;
  padding: 24rpx 0;
  border: none;
  outline: none;
  min-height: 80rpx;
  line-height: 1.5;
  box-sizing: border-box;
  height: 80rpx;
}

.form-input.number-input {
  text-align: right;
}

.form-input-container {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.form-input-container .form-input {
  flex: 1;
  margin-right: 16rpx;
}

.input-unit {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  min-width: 40rpx;
}

.form-input.full-width {
  width: 100%;
  text-align: left;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.form-value {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.value-text {
  font-size: 32rpx;
  color: #333;
  margin-right: 16rpx;
}

.value-text.placeholder {
  color: #999;
}

/* 图片上传样式 */
.image-upload-container {
  width: 100%;
  margin-top: 24rpx;
}

.detail-images-container {
  width: 100%;
  margin-top: 24rpx;
}

.image-upload-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.image-preview {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #f0f0f0;
}

.image-preview.main-image {
  width: 200rpx;
  height: 200rpx;
}

.image-preview.detail-image {
  width: 160rpx;
  height: 160rpx;
}

.image-preview image {
  width: 100%;
  height: 100%;
}

.image-delete {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.upload-btn {
  border: 2rpx dashed #e4e7ed;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafbfc;
  transition: all 0.3s ease;
}

.upload-btn:active {
  background-color: #f0f0f0;
  border-color: #ccc;
}

.upload-btn.main-upload {
  width: 200rpx;
  height: 200rpx;
}

.upload-btn.detail-upload {
  width: 160rpx;
  height: 160rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #c8c9cc;
  margin-top: 8rpx;
}

.form-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
  display: block;
  width: 100%;
}

/* 时间选择器样式 */
.time-range-simple {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.time-picker-simple {
  padding: 8rpx 12rpx;
  background: #f8f9fa;
  border-radius: 6rpx;
  border: 1rpx solid #e4e7ed;
}

.time-text {
  font-size: 32rpx;
  color: #333;
}

.time-separator {
  font-size: 28rpx;
  color: #666;
  margin: 0 8rpx;
}

/* 单位选择器样式 */
.unit-list {
  padding: 0;
}

.unit-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.unit-item:last-child {
  border-bottom: none;
}

.unit-item.active {
  background-color: #fff9e6;
}

.unit-name {
  font-size: 32rpx;
  color: #333;
}

.unit-item.active .unit-name {
  color: #fdd118;
  font-weight: 600;
}

/* 提成类型选择样式 */
.commission-type-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.type-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 提交按钮 */
.submit-section {
  padding: 40rpx 20rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #fdd118 !important;
  color: #333 !important;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.submit-btn:active {
  background-color: #e6c115 !important;
}

/* 弹窗样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: flex-end;
}

.popup-content {
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 24rpx 24rpx 0 0;
  padding: 0;
  width: 100%;
  max-height: 85vh;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #e8eaed;
  position: sticky;
  top: 0;
  z-index: 10;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  background: linear-gradient(135deg, #333 0%, #666 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.close-btn {
  padding: 12rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.05);
}

.close-btn:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

/* 搜索框样式 */
.search-container {
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1rpx solid #e8eaed;
}

.search-box {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 32rpx;
  padding: 20rpx 28rpx;
  border: 2rpx solid #e4e7ed;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.search-box:focus-within {
  border-color: #fdd118;
  box-shadow: 0 4rpx 16rpx rgba(253, 209, 24, 0.15);
  transform: translateY(-2rpx);
}

.search-box .u-icon {
  color: #666;
  transition: color 0.3s ease;
}

.search-box:focus-within .u-icon {
  color: #fdd118;
}

.search-input {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  margin-left: 20rpx;
  border: none;
  outline: none;
  background: transparent;
  line-height: 1.4;
}

.search-input::placeholder {
  color: #999;
  font-size: 28rpx;
}

.clear-btn {
  margin-left: 20rpx;
  padding: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.clear-btn:active {
  opacity: 1;
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.05);
}

.skill-list {
  padding: 0 32rpx;
  background: #ffffff;
}

.loading-text,
.empty-text {
  text-align: center;
  padding: 60rpx 0;
  font-size: 28rpx;
  color: #999;
  background: #fafbfc;
  margin: 20rpx 0;
  border-radius: 16rpx;
  border: 1rpx dashed #e4e7ed;
}

.skill-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 24rpx;
  border-bottom: 1rpx solid #f5f6f7;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
  background: #ffffff;
}

.skill-item:hover {
  background-color: #f8f9fa;
}

.skill-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.skill-item.active {
  background: linear-gradient(135deg, #fff9e6 0%, #fffbf0 100%);
  border: 1rpx solid #fdd118;
  box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.1);
  transform: translateX(8rpx);
}

.skill-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
}

.skill-item.active .skill-name {
  color: #e6a23c;
  font-weight: 600;
}

.check-icon {
  display: flex;
  align-items: center;
}

.popup-footer {
  padding: 32rpx;
  background: #ffffff;
  border-top: 1rpx solid #e8eaed;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.confirm-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #fdd118 0%, #f4c430 100%);
  color: #333;
  border: none;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 700;
  box-shadow: 0 8rpx 24rpx rgba(253, 209, 24, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.confirm-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.confirm-btn:active {
  background: linear-gradient(135deg, #e6c115 0%, #d4a017 100%);
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.4);
}

.confirm-btn:active::before {
  left: 100%;
}
</style>
