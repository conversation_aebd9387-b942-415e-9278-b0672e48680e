"""
统一错误处理工具类
用于标准化API错误响应格式，提升用户体验
"""

from typing import Dict, Any, Optional
from utils.log_util import logger
from utils.response_util import ResponseUtil
from exceptions.exception import BusinessException, ValidationException


class ErrorHandlerUtil:
    """统一错误处理工具类"""
    
    # 错误消息映射表 - 将技术错误转换为用户友好的消息
    ERROR_MESSAGE_MAP = {
        # 数据库相关错误
        "connection": "系统繁忙，请稍后重试",
        "timeout": "请求超时，请稍后重试", 
        "duplicate": "数据已存在，请勿重复操作",
        "not_found": "数据不存在或已被删除",
        "foreign_key": "数据关联异常，请检查相关信息",
        
        # 业务逻辑错误
        "insufficient_balance": "余额不足，请先充值",
        "already_purchased": "该线索已被购买",
        "already_shared": "该线索已经共享过",
        "permission_denied": "权限不足，无法执行此操作",
        "invalid_status": "当前状态不允许此操作",
        
        # 参数验证错误
        "required_field": "必填字段不能为空",
        "invalid_format": "数据格式不正确",
        "invalid_range": "数值超出允许范围",
        
        # 系统错误
        "internal_error": "系统内部错误，请联系管理员",
        "service_unavailable": "服务暂时不可用，请稍后重试"
    }
    
    @classmethod
    def handle_controller_error(
        cls, 
        error: Exception, 
        operation: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        统一处理控制器层错误
        
        Args:
            error: 异常对象
            operation: 操作描述（如"线索购买"、"订单共享"）
            user_id: 用户ID（用于日志记录）
            
        Returns:
            标准化的错误响应
        """
        try:
            # 记录详细错误日志
            error_context = f"操作: {operation}"
            if user_id:
                error_context += f", 用户ID: {user_id}"
            
            logger.error(f"{error_context}, 错误: {str(error)}")
            
            # 根据异常类型返回不同的响应
            if isinstance(error, ValidationException):
                return cls._handle_validation_error(error, operation)
            elif isinstance(error, BusinessException):
                return cls._handle_business_error(error, operation)
            else:
                return cls._handle_system_error(error, operation)
                
        except Exception as e:
            # 错误处理器本身出错时的兜底处理
            logger.error(f"错误处理器异常: {str(e)}")
            return ResponseUtil.error(msg=f"{operation}失败，请稍后重试")
    
    @classmethod
    def _handle_validation_error(
        cls, 
        error: ValidationException, 
        operation: str
    ) -> Dict[str, Any]:
        """处理参数验证错误"""
        message = error.message or "参数验证失败"
        
        # 优化常见验证错误的提示
        if "不能为空" in message:
            message = cls.ERROR_MESSAGE_MAP["required_field"]
        elif "格式" in message:
            message = cls.ERROR_MESSAGE_MAP["invalid_format"]
        elif "范围" in message or "超出" in message:
            message = cls.ERROR_MESSAGE_MAP["invalid_range"]
            
        return ResponseUtil.error(msg=f"{operation}失败: {message}")
    
    @classmethod
    def _handle_business_error(
        cls, 
        error: BusinessException, 
        operation: str
    ) -> Dict[str, Any]:
        """处理业务逻辑错误"""
        message = error.message or "业务处理失败"
        
        # 优化常见业务错误的提示
        user_friendly_message = cls._get_user_friendly_message(message)
        
        return ResponseUtil.error(msg=f"{operation}失败: {user_friendly_message}")
    
    @classmethod
    def _handle_system_error(
        cls, 
        error: Exception, 
        operation: str
    ) -> Dict[str, Any]:
        """处理系统错误"""
        error_str = str(error).lower()
        
        # 根据错误内容判断错误类型
        if "connection" in error_str or "connect" in error_str:
            message = cls.ERROR_MESSAGE_MAP["connection"]
        elif "timeout" in error_str:
            message = cls.ERROR_MESSAGE_MAP["timeout"]
        elif "duplicate" in error_str:
            message = cls.ERROR_MESSAGE_MAP["duplicate"]
        else:
            message = cls.ERROR_MESSAGE_MAP["internal_error"]
            
        return ResponseUtil.error(msg=f"{operation}失败: {message}")
    
    @classmethod
    def _get_user_friendly_message(cls, technical_message: str) -> str:
        """将技术错误消息转换为用户友好的消息"""
        message_lower = technical_message.lower()
        
        # 检查常见的业务错误关键词
        for key, friendly_msg in cls.ERROR_MESSAGE_MAP.items():
            if key in message_lower:
                return friendly_msg
                
        # 如果没有匹配到，检查是否包含特定关键词
        if "余额" in technical_message and "不足" in technical_message:
            return cls.ERROR_MESSAGE_MAP["insufficient_balance"]
        elif "已购买" in technical_message or "已被购买" in technical_message:
            return cls.ERROR_MESSAGE_MAP["already_purchased"]
        elif "已共享" in technical_message:
            return cls.ERROR_MESSAGE_MAP["already_shared"]
        elif "权限" in technical_message:
            return cls.ERROR_MESSAGE_MAP["permission_denied"]
        elif "状态" in technical_message and "不允许" in technical_message:
            return cls.ERROR_MESSAGE_MAP["invalid_status"]
        else:
            # 如果都没有匹配到，返回原始消息（但去掉技术细节）
            return cls._clean_technical_message(technical_message)
    
    @classmethod
    def _clean_technical_message(cls, message: str) -> str:
        """清理技术消息中的敏感信息"""
        # 移除常见的技术错误前缀
        prefixes_to_remove = [
            "sqlalchemy.exc.",
            "pymysql.err.",
            "Exception:",
            "Error:",
            "Traceback"
        ]
        
        cleaned_message = message
        for prefix in prefixes_to_remove:
            if cleaned_message.startswith(prefix):
                cleaned_message = cleaned_message[len(prefix):].strip()
                
        # 限制消息长度
        if len(cleaned_message) > 100:
            cleaned_message = cleaned_message[:100] + "..."
            
        return cleaned_message or "操作失败，请稍后重试"


class SuccessHandlerUtil:
    """统一成功处理工具类"""
    
    @classmethod
    def handle_success(
        cls,
        operation: str,
        data: Optional[Dict[str, Any]] = None,
        custom_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        统一处理成功响应
        
        Args:
            operation: 操作描述
            data: 返回数据
            custom_message: 自定义成功消息
            
        Returns:
            标准化的成功响应
        """
        message = custom_message or f"{operation}成功"
        
        if data:
            return ResponseUtil.success(data=data, msg=message)
        else:
            return ResponseUtil.success(msg=message)
