<template>
  <view class="modern-staff-dashboard" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 现代化头部 -->
    <view class="dashboard-header">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 角色切换按钮 -->
        <view class="role-switch-container">
          <view class="role-switch-btn" @click="switchToStoreMode">
            <text class="switch-text">切换到管理端</text>
            <u-icon name="arrow-right" color="#fff" size="12"></u-icon>
          </view>
        </view>

        <view class="top-nav">
          <view class="header-left">
            <view class="contact-manager-btn" @click="contactManager">
              <u-icon name="phone" color="#fff" size="20"></u-icon>
              <text>联系店长</text>
            </view>

          </view>
          <view class="title">接单系统</view>
          <view class="header-right">
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="dashboard-content">
      <!-- 标签页 -->
      <view class="modern-tabs">
        <view class="tab-item" :class="{ active: currentTab === 'grab' }" @click="switchTab('grab')">
          <text>待接单</text>
          <view class="tab-line" v-if="currentTab === 'grab'"></view>
        </view>
        <view class="tab-item" :class="{ active: currentTab === 'waiting' }" @click="switchTab('waiting')">
          <text>待服务</text>
          <view class="tab-line" v-if="currentTab === 'waiting'"></view>
        </view>
        <view class="tab-item" :class="{ active: currentTab === 'completed' }" @click="switchTab('completed')">
          <text>已完成</text>
          <view class="tab-line" v-if="currentTab === 'completed'"></view>
        </view>
        <view class="tab-item" :class="{ active: currentTab === 'canceled' }" @click="switchTab('canceled')">
          <text>已取消</text>
          <view class="tab-line" v-if="currentTab === 'canceled'"></view>
        </view>
      </view>

      <!-- 提示信息 -->
      <view class="notice-bar">
        <view class="notice-icon">
          <u-icon name="volume" color="#fdd118" size="16"></u-icon>
        </view>
        <text>接单后请尽快确认，服务过程中记得拍照！</text>
      </view>

      <!-- 订单列表 -->
      <view class="order-list">


        <!-- 加载状态 -->
        <view v-if="loading && orderList.length === 0" class="loading-container">
          <u-loading-icon mode="circle" color="#fdd118" size="40"></u-loading-icon>
          <text class="loading-text">加载中...</text>
        </view>



        <!-- 订单项 -->
        <view class="order-item-simple" v-for="(order, index) in orderList" :key="index" wx:key="index" style="background: #fff; padding: 24rpx; margin-bottom: 20rpx; border-radius: 16rpx; box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);">

          <!-- 订单头部 -->
          <view style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20rpx; padding-bottom: 16rpx; border-bottom: 1rpx solid #f0f0f0;">
            <view style="display: flex; align-items: center;">
              <text style="font-size: 32rpx; font-weight: bold; color: #333;">🛍️ {{ order.product_name || '未知服务' }}</text>
            </view>
            <view style="background: rgba(253, 209, 24, 0.1); padding: 6rpx 12rpx; border-radius: 12rpx;">
              <text style="font-size: 24rpx; color: #fdd118; font-weight: 500;">{{ order.order_status_name || '未知状态' }}</text>
            </view>
          </view>

          <!-- 客户信息 -->
          <view style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16rpx; background: rgba(248, 249, 250, 0.8); padding: 16rpx; border-radius: 12rpx;">
            <view style="flex: 1;">
              <view style="display: flex; align-items: center; margin-bottom: 8rpx;">
                <text style="font-size: 24rpx; color: #999; margin-right: 8rpx;">客户姓名：</text>
                <text style="font-size: 28rpx; color: #333; font-weight: 500;">👤 {{ order.customer_name || '未知客户' }}</text>
              </view>
              <view style="display: flex; align-items: center;">
                <text style="font-size: 24rpx; color: #999; margin-right: 8rpx;">联系电话：</text>
                <text style="font-size: 26rpx; color: #666;">📱 {{ order.customer_mobile || '暂无电话' }}</text>
              </view>
            </view>
            <view v-if="order.customer_mobile" class="function-btn" style="background: #4caf50; padding: 12rpx; border-radius: 50%; margin-left: 16rpx;" @click="callCustomer" :data-phone="order.customer_mobile">
              <text style="font-size: 24rpx; color: #fff;">📞</text>
            </view>
          </view>

          <!-- 服务地址 -->
          <view style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16rpx; background: rgba(248, 249, 250, 0.8); padding: 16rpx; border-radius: 12rpx;">
            <view style="flex: 1;">
              <view style="display: flex; align-items: flex-start; margin-bottom: 8rpx;">
                <text style="font-size: 24rpx; color: #999; margin-right: 8rpx;">服务地址：</text>
                <text style="font-size: 26rpx; color: #666; line-height: 1.4; flex: 1;">📍 {{ order.service_address || '未知地址' }}</text>
              </view>
              <view v-if="order.distance !== undefined" style="display: flex; align-items: center;">
                <text style="font-size: 22rpx; color: #ff9800; font-weight: 500;">🚗 距离约 {{ order.distance }}km</text>
              </view>
            </view>
            <view class="function-btn" style="background: #2196f3; padding: 12rpx; border-radius: 50%; margin-left: 16rpx;" @click="openMapForOrder" :data-order-index="index">
              <text style="font-size: 24rpx; color: #fff;">🗺️</text>
            </view>
          </view>

          <!-- 服务时间 -->
          <view style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16rpx; background: rgba(248, 249, 250, 0.8); padding: 16rpx; border-radius: 12rpx;">
            <view style="flex: 1;">
              <view style="display: flex; align-items: center;">
                <text style="font-size: 24rpx; color: #999; margin-right: 8rpx;">服务时间：</text>
                <text style="font-size: 26rpx; color: #666;">🕐 {{ order.service_date || '未知日期' }} {{ order.service_hour || '未知时间' }}</text>
              </view>
            </view>
            <view class="function-btn" style="background: #ff9800; padding: 12rpx; border-radius: 50%; margin-left: 16rpx;" @click="copyOrderInfo" :data-order-index="index">
              <text style="font-size: 24rpx; color: #fff;">📋</text>
            </view>
          </view>

          <!-- 提成信息 -->
          <view v-if="order.commission_amount" style="display: flex; align-items: center; margin-bottom: 20rpx; background: linear-gradient(135deg, rgba(253, 209, 24, 0.05) 0%, rgba(255, 128, 27, 0.05) 100%); padding: 16rpx; border-radius: 12rpx; border: 1rpx solid rgba(253, 209, 24, 0.2);">
            <text style="font-size: 24rpx; color: #999; margin-right: 8rpx;">我的提成：</text>
            <text style="font-size: 30rpx; color: #fdd118; font-weight: bold;">💰 ¥{{ order.commission_amount }}</text>
          </view>

          <!-- 订单详情 -->
          <view style="margin-bottom: 20rpx; padding: 16rpx; background: rgba(248, 249, 250, 0.8); border-radius: 12rpx;">
            <view style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8rpx;">
              <view style="display: flex; align-items: center; flex: 1;">
                <text style="font-size: 24rpx; color: #999;">订单号：</text>
                <text style="font-size: 24rpx; color: #333; margin-left: 8rpx;">📋 {{ order.order_number }}</text>
              </view>
              <view class="function-btn" style="background: #ff9800; padding: 8rpx 12rpx; border-radius: 8rpx; margin-left: 16rpx;" @click="copyOrderNumber" :data-order-number="order.order_number">
                <text style="font-size: 20rpx; color: #fff;">📋 复制</text>
              </view>
            </view>
            <view v-if="order.service_remark" style="display: flex; align-items: flex-start;">
              <text style="font-size: 24rpx; color: #999;">备注：</text>
              <text style="font-size: 24rpx; color: #333; margin-left: 8rpx; line-height: 1.4;">💬 {{ order.service_remark }}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view style="display: flex; gap: 16rpx;" v-if="currentTab === 'grab'">
            <view class="action-button" style="flex: 1; background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%); text-align: center; padding: 18rpx; border-radius: 12rpx; box-shadow: 0 4rpx 16rpx rgba(253, 209, 24, 0.3);" @click="handleGrabOrder" :data-order-index="index">
              <text style="color: #fff; font-size: 28rpx; font-weight: 500;">✅ 确认接单</text>
            </view>
            <view class="action-button" style="flex: 1; background: linear-gradient(135deg, #ff5252 0%, #ff7676 100%); text-align: center; padding: 18rpx; border-radius: 12rpx; box-shadow: 0 4rpx 16rpx rgba(255, 82, 82, 0.3);" @click="handleRejectOrder" :data-order-index="index">
              <text style="color: #fff; font-size: 28rpx; font-weight: 500;">❌ 拒绝接单</text>
            </view>
          </view>

          <!-- 待服务页面按钮 - 根据订单状态显示不同按钮 -->
          <view style="display: flex; gap: 16rpx;" v-else-if="currentTab === 'waiting'">
            <!-- 状态40（已派单）：显示联系客户和前往服务地址 -->
            <template v-if="order.order_status === 40">
              <view class="action-button" style="flex: 1; background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%); text-align: center; padding: 18rpx; border-radius: 12rpx; box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);" @click="handleContactCustomer" :data-order-index="index">
                <text style="color: #fff; font-size: 28rpx; font-weight: 500;">📞 联系客户</text>
              </view>
              <view class="action-button" style="flex: 1; background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%); text-align: center; padding: 18rpx; border-radius: 12rpx; box-shadow: 0 4rpx 16rpx rgba(253, 209, 24, 0.3);" @click="handleGoToService" :data-order-index="index">
                <text style="color: #fff; font-size: 28rpx; font-weight: 500;">🚗 前往服务地址</text>
              </view>
            </template>

            <!-- 状态50（执行中）：只显示我已到达开始服务 -->
            <template v-else-if="order.order_status === 50">
              <view class="action-button" style="width: 100%; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); text-align: center; padding: 18rpx; border-radius: 12rpx; box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);" @click="handleStartService" :data-order-index="index">
                <text style="color: #fff; font-size: 28rpx; font-weight: 500;">🎯 我已到达 开始服务</text>
              </view>
            </template>

            <!-- 状态60（开始服务）：显示联系客户和结束服务 -->
            <template v-else-if="order.order_status === 60">
              <view class="action-button" style="flex: 1; background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%); text-align: center; padding: 18rpx; border-radius: 12rpx; box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);" @click="handleContactCustomer" :data-order-index="index">
                <text style="color: #fff; font-size: 28rpx; font-weight: 500;">📞 联系客户</text>
              </view>
              <view class="action-button" style="flex: 1; background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); text-align: center; padding: 18rpx; border-radius: 12rpx; box-shadow: 0 4rpx 16rpx rgba(255, 152, 0, 0.3);" @click="handleEndService" :data-order-index="index">
                <text style="color: #fff; font-size: 28rpx; font-weight: 500;">✅ 结束服务</text>
              </view>
            </template>

            <!-- 其他状态：显示联系客户和查看详情 -->
            <template v-else>
              <view class="action-button" style="flex: 1; background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%); text-align: center; padding: 18rpx; border-radius: 12rpx; box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);" @click="handleContactCustomer" :data-order-index="index">
                <text style="color: #fff; font-size: 28rpx; font-weight: 500;">📞 联系客户</text>
              </view>
              <view class="action-button" style="flex: 1; background: rgba(253, 209, 24, 0.1); border: 1rpx solid #fdd118; text-align: center; padding: 18rpx; border-radius: 12rpx;" @click="handleViewDetail" :data-order-index="index">
                <text style="color: #fdd118; font-size: 28rpx; font-weight: 500;">👁️ 查看详情</text>
              </view>
            </template>
          </view>

          <!-- 已完成和已取消页面不显示任何按钮 -->
          <view style="display: flex; gap: 16rpx;" v-else-if="currentTab === 'canceled'">
            <view class="action-button" style="flex: 1; background: rgba(253, 209, 24, 0.1); border: 1rpx solid #fdd118; text-align: center; padding: 18rpx; border-radius: 12rpx;" @click="handleViewDetail" :data-order-index="index">
              <text style="color: #fdd118; font-size: 28rpx; font-weight: 500;">👁️ 查看详情</text>
            </view>
          </view>

          <!-- 已完成页面不显示任何按钮 -->
          <view v-else-if="currentTab === 'completed'" style="display: flex; justify-content: center; padding: 20rpx;">
            <text style="color: #999; font-size: 24rpx;">服务已完成</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="!loading && orderList.length === 0" class="empty-container">
          <u-icon name="file-text" size="60" color="#ccc"></u-icon>
          <text class="empty-text">暂无{{ getTabName() }}订单</text>
          <view class="refresh-btn" @click="refreshData">
            <text>点击刷新</text>
          </view>
        </view>

        <!-- 没有更多提示 -->
        <view class="no-more" v-if="!loading && orderList.length > 0 && !hasMore">
          <text>没有更多了</text>
        </view>
      </view>
    </view>

    <!-- 开始服务弹窗 -->
    <view v-if="startServiceModal.show" class="start-service-modal" @click="closeStartServiceModal">
      <view class="modal-content" @click.stop>
        <!-- 弹窗头部 -->
        <view class="modal-header">
          <text class="modal-title">开始服务</text>
          <view class="close-btn" @click="closeStartServiceModal">✕</view> 
        </view>

        <!-- 位置验证状态 -->
        <view class="location-status" :class="startServiceModal.locationValid ? 'valid' : 'invalid'">
          <view class="status-icon">
            <text v-if="startServiceModal.locationValid">✓</text>
            <text v-else>⚠</text>
          </view>
          <view class="status-content">
            <text class="status-title">{{ startServiceModal.locationValid ? '位置验证通过' : '位置距离过远' }}</text>
            <text class="status-desc">{{ startServiceModal.distance }}</text>
          </view>
        </view>

        <!-- 提示信息 -->
        <view class="tip-message">
          <text>{{ startServiceModal.locationValid ? '当前位置距离客户的服务地址较近，请对周围环境拍照上传后继续操作' : '当前位置距离客户的服务地址过远，请移动后再试，如果是系统误判，请对周围环境拍照上传后继续操作' }}</text>
        </view>

        <!-- 图片上传区域 -->
        <view class="upload-section">
          <view class="section-title">
            <text>拍照上传客户环境</text>
            <text class="required">*</text>
          </view>

          <view class="upload-area" @click="chooseImage">
            <view v-if="!startServiceModal.uploadedImage" class="upload-placeholder">
              <view class="upload-icon">📷</view>
              <text class="upload-text">拍照上传</text>
            </view>
            <image v-else :src="startServiceModal.uploadedImage" class="uploaded-image" mode="aspectFit"></image>
          </view>

          <view class="upload-tip">
            <text>请拍摄客户服务环境照片，确保照片清晰可见</text>
          </view>
        </view>

        <!-- 订单信息 -->
        <view class="order-info" v-if="startServiceModal.order">
          <view class="info-item">
            <text class="label">订单号：</text>
            <text class="value">{{ startServiceModal.order.order_number }}</text>
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="modal-actions">
          <view class="action-button cancel" @click="closeStartServiceModal">
            <text>取消</text>
          </view>
          <view class="action-button confirm" :class="{ disabled: !startServiceModal.uploadedImage }" @click="confirmStartService">
            <text>确认开始服务</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部TabBar -->
    <view class="staff-tabbar">
      <view class="tab-item active">
        <image src="/static/img/tab/map_a.png" mode="aspectFit"></image>
        <text>服务</text>
      </view>
      <view class="tab-item" @click="navigateTo('/pages-staff/home/<USER>')">
        <image src="/static/img/tab/my.png" mode="aspectFit"></image>
        <text>我的</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { getStaffOrderList, getStaffOrderStatistics, acceptStaffOrder, rejectStaffOrder, updateStaffOrderStatus, startService } from '@/api/staff-order.js';

export default {
  data() {
    return {
      currentTab: 'grab',
      orderList: [],
      loading: false,
      statistics: {
        pending_confirm: 0,
        in_service: 0,
        completed: 0,
        total: 0,
        total_commission: 0
      },
      currentPage: 1,
      pageSize: 20,
      hasMore: true,
      isInitialized: false,  // 是否已初始化
      lastLoadTime: 0,       // 上次加载时间
      currentLocation: null, // 当前位置
      forceRefresh: false,   // 是否强制刷新
      // 开始服务弹窗数据
      startServiceModal: {
        show: false,
        order: null,
        locationValid: false,
        distance: '',
        uploadedImage: '',
        uploadedImageUrl: '', // 服务器返回的图片URL
        uploadedFileInfo: null, // 完整文件信息
        uploading: false
      }
    };
  },
  computed: {
    ...mapState(['storeInfo', 'staffInfo', 'currentRole', 'StatusBar']),
    ...mapGetters(['hasDualRole', 'getCurrentSelectedCompany']),
    // 当前公司信息（优先使用选中的公司，其次使用默认公司）
    currentCompanyInfo() {
      console.log('计算currentCompanyInfo - 开始');

      // 1. 优先使用当前选中的公司（用户主动切换的）
      const selectedCompany = this.getCurrentSelectedCompany;
      console.log('Vuex中的选中公司:', selectedCompany);

      if (selectedCompany && selectedCompany.store_id && selectedCompany.store_uuid) {
        console.log('使用Vuex中的选中公司:', selectedCompany);
        return {
          company_id: selectedCompany.company_id,
          store_id: selectedCompany.store_id,
          store_uuid: selectedCompany.store_uuid,
          store_name: selectedCompany.store_name || selectedCompany.name,
          name: selectedCompany.store_name || selectedCompany.name,
          isDefault: selectedCompany.isDefault || false
        };
      }

      // 2. 检查本地存储中的选中公司
      const localSelectedCompany = uni.getStorageSync('currentSelectedCompany');
      console.log('本地存储中的选中公司:', localSelectedCompany);

      if (localSelectedCompany && localSelectedCompany.store_id && localSelectedCompany.store_uuid) {
        console.log('使用本地存储中的选中公司:', localSelectedCompany);
        // 同步到Vuex
        this.$store.commit('setCurrentSelectedCompany', localSelectedCompany);
        return {
          company_id: localSelectedCompany.company_id,
          store_id: localSelectedCompany.store_id,
          store_uuid: localSelectedCompany.store_uuid,
          store_name: localSelectedCompany.store_name || localSelectedCompany.name,
          name: localSelectedCompany.store_name || localSelectedCompany.name,
          isDefault: localSelectedCompany.isDefault || false
        };
      }

      // 3. 如果没有选中公司，使用默认公司
      if (this.staffInfo && this.staffInfo.companies) {
        const defaultCompany = this.staffInfo.companies.find(company => company.is_default_company === 1) ||
                              this.staffInfo.companies[0];

        console.log('使用默认公司:', defaultCompany);

        if (defaultCompany) {
          const companyInfo = {
            company_id: defaultCompany.company_id,
            store_id: defaultCompany.store_id,
            store_uuid: defaultCompany.store_uuid,
            store_name: defaultCompany.store_name,
            name: defaultCompany.store_name,
            isDefault: defaultCompany.is_default_company === 1
          };

          // 将默认公司保存到Vuex和本地存储
          this.$store.commit('setCurrentSelectedCompany', companyInfo);
          uni.setStorageSync('currentSelectedCompany', companyInfo);

          return companyInfo;
        }
      }

      console.warn('无法获取任何公司信息');
      return null;
    }
  },
  onShow() {
    console.log('员工端首页onShow - 检查是否需要刷新数据');
    console.log('当前状态:', {
      currentTab: this.currentTab,
      orderListLength: this.orderList.length,
      loading: this.loading,
      isInitialized: this.isInitialized
    });

    // 检查用户角色，如果不是员工则跳转到管理端首页
    this.checkUserRoleAndRedirect();

    // 检查是否需要强制刷新（来自公司切换）
    this.checkForceRefresh();
  },
  onLoad(options) {
    console.log('员工端首页onLoad - 页面加载，参数:', options);

    // 监听公司切换事件
    uni.$on('companyChanged', this.handleCompanyChanged);

    // 监听服务完成事件
    uni.$on('serviceCompleted', this.handleServiceCompleted);

    // 检查是否是强制刷新
    if (options && (options.refresh === '1' || options.timestamp)) {
      console.log('检测到强制刷新参数，将重新加载数据');
      this.forceRefresh = true;
    }

    // onLoad 只做基础初始化，不加载数据
    // 数据加载由 onShow 统一处理
  },
  onUnload() {
    // 移除事件监听
    uni.$off('companyChanged', this.handleCompanyChanged);
    uni.$off('serviceCompleted', this.handleServiceCompleted);
  },

  // 微信小程序转发功能
  onShareAppMessage() {
    let shareobj = {
      title: '家政服务好帮手，进来逛逛吧~', //分享的标题
      path: '/pages/login/login?tg=' + uni.getStorageSync('tg') + '&shareScene=' + uni.getStorageSync('scene'), //好友点击分享之后跳转的页面
      imageUrl: "https://xyj-public-static.obs.cn-east-3.myhuaweicloud.com/UISCK/jingang/shareCover/share_02.png", //分享的图片
    };
    return shareobj;
  },
  methods: {
    // 联系店长按钮点击事件
    async contactManager() {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '获取联系方式...'
        });

        // 调用API获取门店联系方式
        const { getStoreContact } = require('../../api/staff-auth.js');
        const result = await getStoreContact();

        uni.hideLoading();

        if (result && result.phone) {
          // 直接拨号，不显示确认弹窗
          uni.makePhoneCall({
            phoneNumber: result.phone,
            success: () => {
              console.log('拨号成功');
            },
            fail: (err) => {
              console.error('拨号失败:', err);
              uni.showToast({
                title: '拨号失败',
                icon: 'none'
              });
            }
          });
        } else {
          uni.showToast({
            title: '门店暂未设置联系电话',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('获取门店联系方式失败:', error);
        uni.showToast({
          title: error.message || '获取联系方式失败',
          icon: 'none'
        });
      }
    },



    // 检查用户角色并重定向
    checkUserRoleAndRedirect() {
      try {
        // 从storage中获取用户角色和相关token
        const currentRole = uni.getStorageSync('currentRole');
        const staffInfo = uni.getStorageSync('staffInfo');
        const staffToken = uni.getStorageSync('staffToken');
        const storeInfo = uni.getStorageSync('storeInfo');
        const token = uni.getStorageSync('token');

        console.log('=== 员工端角色检查 ===');
        console.log('当前角色:', currentRole);
        console.log('员工信息:', staffInfo ? '存在' : '不存在');
        console.log('员工Token:', staffToken ? '存在' : '不存在');
        console.log('门店信息:', storeInfo ? '存在' : '不存在');
        console.log('门店Token:', token ? '存在' : '不存在');

        // 如果没有任何token，跳转到登录页
        if (!token && !staffToken) {
          console.log('未登录，跳转到登录页');
          uni.reLaunch({
            url: '/pages/login/login'
          });
          return;
        }

        // 优先基于currentRole判断，只有明确设置为store时才跳转
        if (currentRole === 'store') {
          console.log('当前角色为门店端，跳转到门店端首页');
          uni.reLaunch({
            url: '/pages/home/<USER>'
          });
          return;
        }

        // 如果没有设置角色但有门店信息，且没有员工信息，则跳转到门店端
        if (!currentRole && storeInfo && !staffInfo) {
          console.log('检测到仅有门店身份，跳转到门店端首页');
          uni.setStorageSync('currentRole', 'store');
          this.$store.commit('Updates', { currentRole: 'store' });
          uni.reLaunch({
            url: '/pages/home/<USER>'
          });
          return;
        }

        // 其他情况（员工角色或有员工信息），继续加载员工端数据
        console.log('员工端用户，继续加载数据');
        console.log('最终确认：保持在员工端页面');
        this.continueLoadData();
      } catch (error) {
        console.error('检查用户角色失败:', error);
        // 如果检查失败，默认加载员工端数据
        console.log('角色检查失败，默认加载员工端数据');
        this.continueLoadData();
      }
    },

    // 继续加载数据的逻辑
    continueLoadData() {
      // 确保Vuex状态与本地存储同步
      this.syncVuexStateFromStorage();

      // 如果已经初始化且距离上次加载超过30秒，则刷新数据
      const now = Date.now();
      if (this.isInitialized && (now - this.lastLoadTime > 30000)) {
        console.log('距离上次加载超过30秒，刷新数据');
        this.refreshData();
      } else if (!this.isInitialized) {
        console.log('首次显示，初始化数据');
        this.loadInitialData();
      } else {
        console.log('数据较新，无需刷新');
      }

      // 获取当前位置（用于计算距离）
      this.getCurrentLocation();
    },

    // 同步Vuex状态与本地存储
    syncVuexStateFromStorage() {
      try {
        console.log('开始同步Vuex状态与本地存储');

        const localCompany = uni.getStorageSync('currentSelectedCompany');
        const vuexCompany = this.$store.state.currentSelectedCompany;

        console.log('本地存储的公司信息:', localCompany);
        console.log('Vuex中的公司信息:', vuexCompany);

        // 如果本地存储有数据，但Vuex没有，则同步到Vuex
        if (localCompany && !vuexCompany) {
          console.log('从本地存储恢复当前选中公司到Vuex:', localCompany);
          this.$store.commit('setCurrentSelectedCompany', localCompany);
        }
        // 如果Vuex有数据，但本地存储没有，则同步到本地存储
        else if (vuexCompany && !localCompany) {
          console.log('从Vuex同步当前选中公司到本地存储:', vuexCompany);
          uni.setStorageSync('currentSelectedCompany', vuexCompany);
        }
        // 如果两者都有但不一致，以本地存储为准（用户最新选择）
        else if (localCompany && vuexCompany && localCompany.company_id !== vuexCompany.company_id) {
          console.log('本地存储与Vuex不一致，以本地存储为准:', localCompany);
          this.$store.commit('setCurrentSelectedCompany', localCompany);
        }

        console.log('状态同步完成');
      } catch (error) {
        console.error('同步Vuex状态失败:', error);
      }
    },

    // 处理公司切换事件
    handleCompanyChanged(newCompanyInfo) {
      console.log('收到公司切换事件:', newCompanyInfo);

      // 清除当前数据
      this.orderList = [];
      this.statistics = {
        pending: 0,
        accepted: 0,
        inProgress: 0,
        completed: 0
      };

      // 重置状态
      this.isInitialized = false;
      this.lastLoadTime = 0;

      // 强制重新加载数据
      setTimeout(() => {
        this.loadInitialData();
      }, 500);
    },

    // 处理服务完成事件
    handleServiceCompleted(eventData) {
      console.log('收到服务完成事件:', eventData);

      // 刷新当前页面数据
      this.refreshData();

      // 显示提示信息
      uni.showToast({
        title: '订单状态已更新',
        icon: 'success',
        duration: 1500
      });
    },

    // 检查是否需要强制刷新
    checkForceRefresh() {
      if (this.forceRefresh) {
        console.log('执行强制刷新');
        this.forceRefresh = false;

        // 清除数据并重新加载
        this.orderList = [];
        this.statistics = {
          pending: 0,
          accepted: 0,
          inProgress: 0,
          completed: 0
        };
        this.isInitialized = false;
        this.lastLoadTime = 0;

        // 延迟加载，确保状态同步完成
        setTimeout(() => {
          this.loadInitialData();
        }, 300);
      }
    },

    navigateTo(url) {
      uni.navigateTo({
        url,
      });
    },

    // 初始化数据加载
    async loadInitialData() {
      if (this.loading) {
        console.log('正在加载中，跳过重复请求');
        return;
      }

      try {
        console.log('开始初始化数据加载');

        // 检查门店信息
        const currentCompany = this.currentCompanyInfo;
        if (!currentCompany || !currentCompany.store_id || !currentCompany.store_uuid) {
          console.log('门店信息不完整，引导用户选择门店');
          uni.showModal({
            title: '选择门店',
            content: '请先选择要查看的门店',
            confirmText: '去选择',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                uni.navigateTo({
                  url: '/pages-staff/home/<USER>'
                });
              }
            }
          });
          return;
        }

        await Promise.all([
          this.loadOrderList(),
          this.loadStatistics()
        ]);

        this.isInitialized = true;
        this.lastLoadTime = Date.now();
        console.log('初始化数据加载完成');
      } catch (error) {
        console.error('初始化数据加载失败:', error);
      }
    },

    // 更新订单状态
    async updateOrderStatus(orderNumber, newStatus) {
      try {
        const response = await updateStaffOrderStatus(orderNumber, newStatus);
        return response;
      } catch (error) {
        console.error('更新订单状态失败:', error);
        throw error;
      }
    },

    // 刷新数据（onShow时调用）
    async refreshData() {
      if (this.loading) {
        console.log('正在加载中，跳过重复刷新请求');
        return;
      }

      console.log('开始刷新数据');
      this.currentPage = 1;
      this.hasMore = true;
      await this.loadInitialData();
    },

    // 获取当前tab对应的订单状态
    getCurrentTabStatus() {
      const statusMap = {
        'grab': '20',        // 派单待确认
        'waiting': '40,50,60', // 已派单、执行中、开始服务
        'completed': '80,90',  // 已完成、已评价
        'canceled': '99'       // 已取消
      };
      return statusMap[this.currentTab] || null;
    },

    // 获取当前tab的中文名称
    getTabName() {
      const nameMap = {
        'grab': '待接单',
        'waiting': '待服务',
        'completed': '已完成',
        'canceled': '已取消'
      };
      return nameMap[this.currentTab] || '未知';
    },

    // 加载订单列表
    async loadOrderList() {
      if (this.loading) return;

      try {
        this.loading = true;
        console.log(`加载${this.currentTab}订单列表，页码: ${this.currentPage}`);

        const status = this.getCurrentTabStatus();
        console.log('请求参数:', {
          page: this.currentPage,
          size: this.pageSize,
          status: status
        });

        // 获取当前选中门店信息
        const currentCompany = this.currentCompanyInfo;
        if (!currentCompany || !currentCompany.store_id || !currentCompany.store_uuid) {
          console.error('当前门店信息不完整:', currentCompany);
          uni.showToast({
            title: '请先选择门店',
            icon: 'none'
          });
          return;
        }

        console.log('当前选中门店信息:', {
          store_id: currentCompany.store_id,
          store_uuid: currentCompany.store_uuid,
          store_name: currentCompany.store_name
        });

        const response = await getStaffOrderList({
          page: this.currentPage,
          size: this.pageSize,
          status: status,
          store_id: currentCompany.store_id,
          store_uuid: currentCompany.store_uuid
        });

        console.log('API响应:', response);

        // 修复数据结构处理
        let responseData;
        if (response && response.data) {
          // 如果response有data字段，说明是嵌套结构
          responseData = response.data;
        } else if (response && response.list) {
          // 如果response直接有list字段，说明已经被require.js处理过了
          responseData = response;
        } else {
          responseData = response || {};
        }

        console.log('处理后的响应数据:', responseData);

        if (responseData) {
          const newOrders = responseData.list || [];
          console.log('获取到的订单数据:', newOrders);
          console.log('订单数量:', newOrders.length);

          if (this.currentPage === 1) {
            this.orderList = newOrders;
            console.log('首页加载，直接替换orderList');
          } else {
            this.orderList = [...this.orderList, ...newOrders];
            console.log('追加数据到orderList');
          }

          // 检查是否还有更多数据
          this.hasMore = newOrders.length === this.pageSize;

          console.log(`${this.currentTab}订单列表加载成功，当前共${this.orderList.length}条`);
          console.log('最终orderList:', this.orderList);

          // 添加详细的数据检查
          if (this.orderList.length > 0) {
            console.log('第一条订单数据详情:', this.orderList[0]);
            console.log('订单号:', this.orderList[0].order_number);
            console.log('产品名称:', this.orderList[0].product_name);
            console.log('客户姓名:', this.orderList[0].customer_name);
          }

          // 强制更新视图
          this.$forceUpdate();

          // 延迟再次强制更新，确保视图渲染
          setTimeout(() => {
            console.log('延迟强制更新视图');
            console.log('延迟更新时的orderList:', this.orderList);
            console.log('延迟更新时的orderList长度:', this.orderList.length);
            this.$forceUpdate();
          }, 100);

          // 再次延迟更新，确保数据绑定
          setTimeout(() => {
            console.log('第二次延迟强制更新视图');
            console.log('第二次延迟更新时的orderList:', this.orderList);
            this.$forceUpdate();

            // 计算距离
            this.calculateDistances();
          }, 500);
        } else {
          console.error('API响应格式错误:', response);
        }
      } catch (error) {
        console.error('加载订单列表失败:', error);
        uni.showToast({
          title: '加载订单失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        console.log('loading状态已设置为false');
      }
    },

    // 加载统计信息
    async loadStatistics() {
      try {
        const response = await getStaffOrderStatistics();

        if (response && response.data) {
          this.statistics = response.data;
          console.log('订单统计加载成功:', this.statistics);
        }
      } catch (error) {
        console.error('加载统计信息失败:', error);
      }
    },

    switchTab(tab) {
      if (this.currentTab === tab) return;

      console.log(`切换到${tab}标签`);
      console.log('切换前状态:', {
        currentTab: this.currentTab,
        orderListLength: this.orderList.length
      });

      this.currentTab = tab;
      this.currentPage = 1;
      this.hasMore = true;
      this.orderList = [];

      console.log('切换后状态:', {
        currentTab: this.currentTab,
        orderListLength: this.orderList.length
      });

      // 加载对应状态的订单数据
      this.loadOrderList();
      // 注意：这里只加载订单列表，不重复加载统计信息
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '未知时间';
      try {
        const date = new Date(timeStr);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });
      } catch (error) {
        return timeStr;
      }
    },

    // 获取订单状态对应的CSS类
    getStatusClass(status) {
      const statusMap = {
        10: 'status-accepted',      // 已接单
        20: 'status-pending',       // 派单待确认
        30: 'status-rejected',      // 拒绝接单
        40: 'status-dispatched',    // 已派单
        50: 'status-in-progress',   // 执行中
        60: 'status-serving',       // 开始服务
        70: 'status-service-end',   // 服务结束
        80: 'status-completed',     // 已完成
        90: 'status-evaluated',     // 已评价
        99: 'status-cancelled'      // 已取消
      };
      return statusMap[status] || 'status-unknown';
    },

    // 确认接单
    async grabOrder(order) {
      uni.showModal({
        title: '确认接单',
        content: `确定要接此单吗？\n订单号：${order.order_number}\n服务：${order.product_name}\n提成：¥${order.commission_amount || '0'}`,
        success: async res => {
          if (res.confirm) {
            try {
              // 显示加载提示
              uni.showLoading({
                title: '接单中...',
                mask: true
              });

              // 调用确认接单API
              const response = await acceptStaffOrder(order.order_number);

              uni.hideLoading();

              if (response) {
                uni.showToast({
                  title: '接单成功',
                  icon: 'success',
                });
                // 接单成功后跳转到待服务页面
                this.switchTab('waiting');
              }
            } catch (error) {
              uni.hideLoading();
              console.error('接单失败:', error);

              // 显示错误信息
              const errorMsg = error.message || error.msg || '接单失败，请重试';
              uni.showToast({
                title: errorMsg,
                icon: 'none',
                duration: 3000
              });
            }
          }
        },
      });
    },
    // 拒绝接单
    async rejectOrder(order) {
      uni.showModal({
        title: '拒绝接单',
        content: `确定要拒绝此单吗？\n订单号：${order.order_number}\n服务：${order.product_name}`,
        success: async res => {
          if (res.confirm) {
            try {
              // 显示加载提示
              uni.showLoading({
                title: '处理中...',
                mask: true
              });

              // 调用拒绝接单API
              const response = await rejectStaffOrder(order.order_number, '员工主动拒绝');

              uni.hideLoading();

              if (response) {
                uni.showToast({
                  title: '已拒绝接单',
                  icon: 'success',
                });
                // 刷新当前列表
                this.refreshData();
              }
            } catch (error) {
              uni.hideLoading();
              console.error('拒绝接单失败:', error);

              // 显示错误信息
              const errorMsg = error.message || error.msg || '拒绝接单失败，请重试';
              uni.showToast({
                title: errorMsg,
                icon: 'none',
                duration: 3000
              });
            }
          }
        },
      });
    },

    // 查看订单详情
    viewOrderDetail(order) {
      uni.navigateTo({
        url: `/pages-staff/order/detail?orderNumber=${order.order_number}`
      });
    },

    // 联系客户
    contactCustomer(order) {
      if (!order.customer_mobile) {
        uni.showToast({
          title: '客户电话为空',
          icon: 'none'
        });
        return;
      }

      uni.showActionSheet({
        itemList: ['拨打电话', '发送短信'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 拨打电话
            uni.makePhoneCall({
              phoneNumber: order.customer_mobile
            });
          } else if (res.tapIndex === 1) {
            // 发送短信
            uni.showToast({
              title: '短信功能开发中',
              icon: 'none'
            });
          }
        }
      });
    },

    // 前往服务地址
    async goToService(order) {
      uni.showModal({
        title: '前往服务地址',
        content: `确定现在已经准备好前往了吗？\n订单号：${order.order_number}\n服务地址：${order.service_address || '未知地址'}`,
        success: async res => {
          if (res.confirm) {
            try {
              // 显示加载提示
              uni.showLoading({
                title: '更新状态中...',
                mask: true
              });

              // 调用更新订单状态API（将状态从40更新为50）
              const response = await this.updateOrderStatus(order.order_number, 50);

              uni.hideLoading();

              if (response) {
                uni.showToast({
                  title: '已开始前往服务地址',
                  icon: 'success',
                });
                // 刷新当前列表
                this.refreshData();
              }
            } catch (error) {
              uni.hideLoading();
              console.error('更新订单状态失败:', error);

              // 显示错误信息
              const errorMsg = error.message || error.msg || '更新状态失败，请重试';
              uni.showToast({
                title: errorMsg,
                icon: 'none',
                duration: 3000
              });
            }
          }
        },
      });
    },

    // 开始服务
    async startService(order) {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '获取位置中...',
          mask: true
        });

        // 获取当前位置
        const currentLocation = await this.getCurrentLocationPromise();
        uni.hideLoading();

        // 验证位置距离
        const isLocationValid = this.validateServiceLocation(currentLocation, order);

        // 显示位置验证和图片上传弹窗
        this.showStartServiceModal(order, currentLocation, isLocationValid);

      } catch (error) {
        uni.hideLoading();
        console.error('获取位置失败:', error);

        // 位置获取失败，询问是否继续
        uni.showModal({
          title: '位置获取失败',
          content: '无法获取当前位置，是否继续开始服务？',
          success: res => {
            if (res.confirm) {
              // 跳过位置验证，直接显示图片上传弹窗
              this.showStartServiceModal(order, null, false);
            }
          }
        });
      }
    },

    // 获取当前位置（Promise版本）
    getCurrentLocationPromise() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          success: (res) => {
            resolve({
              latitude: res.latitude,
              longitude: res.longitude
            });
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },

    // 验证服务位置距离
    validateServiceLocation(currentLocation, order) {
      if (!currentLocation || !order.service_lat || !order.service_lng) {
        return false;
      }

      const distance = this.calculateDistance(
        currentLocation.latitude,
        currentLocation.longitude,
        parseFloat(order.service_lat),
        parseFloat(order.service_lng)
      );

      // 距离小于1000米（1公里）认为位置正确
      return distance <= 1.0;
    },

    // 显示开始服务弹窗
    showStartServiceModal(order, currentLocation, isLocationValid) {
      const distanceText = currentLocation && order.service_lat && order.service_lng
        ? `距离服务地址约${this.calculateDistance(
            currentLocation.latitude,
            currentLocation.longitude,
            parseFloat(order.service_lat),
            parseFloat(order.service_lng)
          ).toFixed(1)}公里`
        : '无法计算距离';

      // 设置弹窗数据
      this.startServiceModal = {
        show: true,
        order: order,
        locationValid: isLocationValid,
        distance: distanceText,
        uploadedImage: '',
        uploadedImageUrl: '',
        uploadedFileInfo: null,
        uploading: false
      };
    },

    // 拨打客户电话
    callCustomer(e) {
      const phoneNumber = e.currentTarget.dataset.phone;
      console.log('拨打电话，号码:', phoneNumber);

      if (!phoneNumber) {
        uni.showToast({
          title: '电话号码为空',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: phoneNumber,
        success: () => {
          console.log('拨打电话成功');
        },
        fail: (err) => {
          console.error('拨打电话失败:', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },

    // 直接拨打电话（保留原方法）
    makePhoneCall(phoneNumber) {
      console.log('拨打电话，号码:', phoneNumber);

      if (!phoneNumber) {
        uni.showToast({
          title: '电话号码为空',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: phoneNumber,
        success: () => {
          console.log('拨打电话成功');
        },
        fail: (err) => {
          console.error('拨打电话失败:', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },

    // 打开地图（通过事件）
    openMapForOrder(e) {
      const orderIndex = e.currentTarget.dataset.orderIndex;
      const order = this.orderList[orderIndex];
      this.openMap(order);
    },

    // 打开地图
    openMap(order) {
      try {
        if (!order) {
          console.error('订单信息为空');
          uni.showToast({
            title: '订单信息错误',
            icon: 'none'
          });
          return;
        }

        const latitude = order.service_lat ? parseFloat(order.service_lat) : null;
        const longitude = order.service_lng ? parseFloat(order.service_lng) : null;
        const address = order.service_address || '未知地址';

        console.log('地图信息:', { latitude, longitude, address });

        // 检查是否有经纬度信息
        if (latitude && longitude && !isNaN(latitude) && !isNaN(longitude)) {
          // 直接使用uni.openLocation打开系统地图
          uni.openLocation({
            latitude: latitude,
            longitude: longitude,
            name: '服务地址',
            address: address,
            scale: 16,
            success: () => {
              console.log('打开地图成功');
            },
            fail: (err) => {
              console.error('打开地图失败:', err);
              // 如果打开失败，提供备选方案
              this.showMapActionSheet(address, latitude, longitude);
            }
          });
        } else {
          // 没有经纬度信息，尝试地址搜索
          console.log('没有经纬度信息，尝试地址搜索');
          this.searchAddressAndOpenMap(address);
        }
      } catch (error) {
        console.error('打开地图异常:', error);
        uni.showToast({
          title: '打开地图失败',
          icon: 'none'
        });
      }
    },

    // 显示地图操作选项
    showMapActionSheet(address, latitude, longitude) {
      uni.showActionSheet({
        itemList: ['重试打开地图', '复制地址', '取消'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 重试打开地图
            setTimeout(() => {
              uni.openLocation({
                latitude: latitude,
                longitude: longitude,
                name: '服务地址',
                address: address,
                scale: 16
              });
            }, 500);
          } else if (res.tapIndex === 1) {
            // 复制地址
            this.copyAddress(address);
          }
        }
      });
    },

    // 搜索地址并打开地图
    searchAddressAndOpenMap(address) {
      uni.showActionSheet({
        itemList: ['复制地址', '手动搜索'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 复制地址
            this.copyAddress(address);
          } else if (res.tapIndex === 1) {
            // 提示手动搜索
            uni.showModal({
              title: '地址信息',
              content: `请在地图应用中搜索：${address}`,
              showCancel: false,
              confirmText: '知道了'
            });
          }
        }
      });
    },

    // 复制订单号
    copyOrderNumber(e) {
      const orderNumber = e.currentTarget.dataset.orderNumber;
      console.log('复制订单号:', orderNumber);

      if (!orderNumber) {
        uni.showToast({
          title: '订单号为空',
          icon: 'none'
        });
        return;
      }

      uni.setClipboardData({
        data: orderNumber,
        success: () => {
          uni.showToast({
            title: '订单号已复制',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },

    // 复制订单信息
    copyOrderInfo(e) {
      const orderIndex = e.currentTarget.dataset.orderIndex;
      console.log('复制订单信息，订单索引:', orderIndex);

      if (orderIndex === undefined || !this.orderList[orderIndex]) {
        uni.showToast({
          title: '订单信息不存在',
          icon: 'none'
        });
        return;
      }

      const order = this.orderList[orderIndex];

      // 格式化服务时间为 YYYY-MM-DD HH:mm:ss 格式
      const formatServiceTime = (serviceDate, serviceHour) => {
        if (!serviceDate) return '未知时间';

        try {
          // 处理服务日期
          let formattedDate = '';
          if (serviceDate.includes('-')) {
            // 如果已经是 YYYY-MM-DD 格式
            formattedDate = serviceDate;
          } else {
            // 如果是其他格式，尝试转换
            const date = new Date(serviceDate);
            if (!isNaN(date.getTime())) {
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              formattedDate = `${year}-${month}-${day}`;
            } else {
              formattedDate = serviceDate;
            }
          }

          // 处理服务时间
          let formattedTime = '00:00:00';
          if (serviceHour) {
            if (serviceHour.includes(':')) {
              // 如果已经包含冒号，检查是否需要补充秒
              formattedTime = serviceHour.includes(':00:') ? serviceHour : serviceHour + ':00';
            } else {
              // 如果只是小时数，补充分钟和秒
              formattedTime = serviceHour + ':00:00';
            }
          }

          return `${formattedDate} ${formattedTime}`;
        } catch (error) {
          console.error('格式化服务时间失败:', error);
          return `${serviceDate || '未知日期'} ${serviceHour || '未知时间'}`;
        }
      };

      // 组装复制内容
      const copyContent = `服务产品：${order.product_name || '未知产品'}
服务时间：${formatServiceTime(order.service_date, order.service_hour)}
客户信息：${order.customer_name || '未知客户'}（${order.customer_mobile || '暂无电话'}）
服务地址：${order.service_address || '未知地址'}`;

      console.log('准备复制的内容:', copyContent);

      uni.setClipboardData({
        data: copyContent,
        success: () => {
          uni.showToast({
            title: '订单信息已复制',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },

    // 复制地址
    copyAddress(address) {
      uni.setClipboardData({
        data: address,
        success: () => {
          uni.showToast({
            title: '地址已复制',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },

    // 地图打开失败时的备选方案
    fallbackMapOptions(address) {
      uni.showActionSheet({
        itemList: ['复制地址', '手动搜索地址'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 复制地址到剪贴板
            this.copyAddress(address);
          } else if (res.tapIndex === 1) {
            // 提示使用第三方地图
            uni.showModal({
              title: '提示',
              content: `请手动在地图应用中搜索：${address}`,
              showCancel: false
            });
          }
        }
      });
    },

    // 处理接单
    handleGrabOrder(e) {
      const orderIndex = e.currentTarget.dataset.orderIndex;
      const order = this.orderList[orderIndex];
      this.grabOrder(order);
    },

    // 处理拒绝接单
    handleRejectOrder(e) {
      const orderIndex = e.currentTarget.dataset.orderIndex;
      const order = this.orderList[orderIndex];
      this.rejectOrder(order);
    },

    // 处理查看详情
    handleViewDetail(e) {
      const orderIndex = e.currentTarget.dataset.orderIndex;
      const order = this.orderList[orderIndex];
      this.viewOrderDetail(order);
    },

    // 处理联系客户
    handleContactCustomer(e) {
      const orderIndex = e.currentTarget.dataset.orderIndex;
      const order = this.orderList[orderIndex];
      this.contactCustomer(order);
    },

    // 处理前往服务地址
    handleGoToService(e) {
      const orderIndex = e.currentTarget.dataset.orderIndex;
      const order = this.orderList[orderIndex];
      this.goToService(order);
    },

    // 处理开始服务
    handleStartService(e) {
      const orderIndex = e.currentTarget.dataset.orderIndex;
      const order = this.orderList[orderIndex];
      this.startService(order);
    },

    // 处理结束服务
    handleEndService(e) {
      const orderIndex = e.currentTarget.dataset.orderIndex;
      const order = this.orderList[orderIndex];
      this.endService(order);
    },

    // 结束服务
    endService(order) {
      // 跳转到服务记录页面
      uni.navigateTo({
        url: `/pages-staff/service/record?orderNumber=${order.order_number}`
      });
    },

    // 获取当前位置
    getCurrentLocation() {
      uni.getLocation({
        type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
        success: (res) => {
          console.log('获取当前位置成功:', res);
          this.currentLocation = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          // 计算所有订单的距离
          this.calculateDistances();
        },
        fail: (err) => {
          console.log('获取位置失败:', err);
          // 位置获取失败不影响其他功能
        }
      });
    },

    // 计算所有订单的距离
    calculateDistances() {
      if (!this.currentLocation || !this.orderList.length) {
        return;
      }

      this.orderList.forEach(order => {
        if (order.service_lat && order.service_lng) {
          const distance = this.calculateDistance(
            this.currentLocation.latitude,
            this.currentLocation.longitude,
            parseFloat(order.service_lat),
            parseFloat(order.service_lng)
          );
          // 使用Vue.set确保响应式更新
          this.$set(order, 'distance', distance);
        }
      });
    },

    // 关闭开始服务弹窗
    closeStartServiceModal() {
      this.startServiceModal.show = false;
      this.startServiceModal.order = null;
      this.startServiceModal.uploadedImage = '';
      this.startServiceModal.uploadedImageUrl = '';
      this.startServiceModal.uploadedFileInfo = null;
      this.startServiceModal.uploading = false;
    },

    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.uploadImage(tempFilePath);
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
          uni.showToast({
            title: '取消图片选择',
            icon: 'none'
          });
        }
      });
    },

    // 上传图片
    async uploadImage(filePath) {
      try {
        this.startServiceModal.uploading = true;
        uni.showLoading({
          title: '上传中...',
          mask: true
        });

        // 上传图片到服务器
        const uploadResult = await this.uploadImageToServer(filePath);

        if (uploadResult.success) {
          this.startServiceModal.uploadedImage = uploadResult.url;
          this.startServiceModal.uploadedImageUrl = uploadResult.url; // 保存服务器URL
          this.startServiceModal.uploadedFileInfo = uploadResult.fileInfo; // 保存完整文件信息

          uni.hideLoading();
          uni.showToast({
            title: '上传成功',
            icon: 'success'
          });
        } else {
          throw new Error(uploadResult.message || '上传失败');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('上传图片失败:', error);
        uni.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        });
      } finally {
        this.startServiceModal.uploading = false;
      }
    },

    // 上传图片到服务器
    uploadImageToServer(filePath) {
      return new Promise((resolve) => {
        uni.uploadFile({
          url: this.$baseUrl + '/api/v1/public/upload',
          filePath: filePath,
          name: 'file',
          success: (uploadRes) => {
            try {
              const result = JSON.parse(uploadRes.data);
              if (result.code === 200 && result.data && result.data.url) {
                // 构造文件信息对象
                const fileInfo = {
                  file_name: result.data.fileName || filePath.split('/').pop(),
                  file_url: result.data.url,
                  file_size: result.data.fileSize || 0,
                  file_type: result.data.fileType || 'image'
                };
                resolve({
                  success: true,
                  url: result.data.url,
                  fileInfo: fileInfo
                });
              } else {
                resolve({ success: false, message: '上传失败' });
              }
            } catch (error) {
              resolve({ success: false, message: '响应解析失败' });
            }
          },
          fail: () => {
            resolve({ success: false, message: '网络请求失败' });
          }
        });
      });
    },

    // 确认开始服务
    async confirmStartService() {
      if (!this.startServiceModal.uploadedFileInfo) {
        uni.showToast({
          title: '请先上传环境照片',
          icon: 'none'
        });
        return;
      }

      try {
        uni.showLoading({
          title: '开始服务中...',
          mask: true
        });

        // 调用开始服务API，传递文件信息
        const response = await startService(
          this.startServiceModal.order.order_number,
          [this.startServiceModal.uploadedFileInfo]
        );

        uni.hideLoading();

        if (response) {
          uni.showToast({
            title: '已开始服务',
            icon: 'success'
          });

          // 关闭弹窗并刷新数据
          this.closeStartServiceModal();
          this.refreshData();
        }
      } catch (error) {
        uni.hideLoading();
        console.error('开始服务失败:', error);

        const errorMsg = error.message || error.msg || '开始服务失败，请重试';
        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      }
    },



    // 计算两点间距离（Haversine公式）
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371; // 地球半径（公里）
      const dLat = this.deg2rad(lat2 - lat1);
      const dLon = this.deg2rad(lon2 - lon1);
      const a =
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      const distance = R * c; // 距离（公里）

      // 返回保留一位小数的距离
      return Math.round(distance * 10) / 10;
    },

    // 角度转弧度
    deg2rad(deg) {
      return deg * (Math.PI/180);
    },



    // 切换到管理端
    switchToStoreMode() {
      console.log('=== 开始切换到管理端 ===');
      console.log('当前角色:', uni.getStorageSync('currentRole'));
      console.log('门店信息:', uni.getStorageSync('storeInfo') ? '存在' : '不存在');
      console.log('员工信息:', uni.getStorageSync('staffInfo') ? '存在' : '不存在');

      uni.showModal({
        title: '切换到管理端',
        content: '确定要切换到管理端吗？',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.handleStoreSwitch();
          }
        }
      });
    },

    // 处理管理端切换逻辑
    async handleStoreSwitch() {
      try {
        // 1. 检查本地缓存是否有管理端账号信息
        const cachedToken = uni.getStorageSync('token');
        const cachedUser = uni.getStorageSync('user');

        console.log('检查本地缓存:', {
          hasToken: !!cachedToken,
          hasUser: !!cachedUser
        });

        if (cachedToken && cachedUser) {
          // 有缓存，直接跳转
          console.log('发现本地管理端缓存，直接跳转');
          this.jumpToStorePage();
          return;
        }

        // 2. 获取当前员工的手机号
        const staffInfo = uni.getStorageSync('staffInfo');
        if (!staffInfo || !staffInfo.mobile) {
          uni.showToast({
            title: '获取员工信息失败',
            icon: 'none'
          });
          return;
        }

        const mobile = staffInfo.mobile;
        console.log('员工手机号:', mobile);

        // 3. 显示加载提示
        uni.showLoading({
          title: '切换中...'
        });

        // 4. 调用切换到管理端API
        const { switchToStoreAccount } = require('@/api/staff-auth.js');
        console.log('开始调用切换到管理端API');
        const switchResult = await switchToStoreAccount({ mobile: mobile });

        uni.hideLoading();

        if (switchResult.access_token) {
          // 切换成功，跳转到管理端
          console.log('管理端切换成功');
          this.jumpToStorePage();
        } else {
          uni.showToast({
            title: '切换失败，请重试',
            icon: 'none'
          });
        }

      } catch (error) {
        console.error('切换到管理端失败:', error);
        uni.hideLoading();

        // 根据错误类型显示不同提示
        let errorMessage = '切换失败，请重试';

        if (error.message) {
          if (error.message.includes('暂无管理端权限')) {
            errorMessage = '该手机号暂无管理端权限';
          } else if (error.message.includes('账号已被冻结')) {
            errorMessage = '管理端账号已被冻结';
          } else if (error.message.includes('未关联门店信息')) {
            errorMessage = '用户未关联门店信息';
          } else if (error.message.includes('切换失败')) {
            errorMessage = error.message;
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    },

    // 跳转到管理端页面
    jumpToStorePage() {
      // 导入缓存清理工具
      const { cleanupAfterRoleSwitch } = require('@/utlis/auth.js');

      // 执行角色切换后的缓存清理
      cleanupAfterRoleSwitch('store');

      console.log('角色已设置为管理端，缓存已清理');

      // 跳转到门店端首页
      uni.reLaunch({
        url: '/pages/home/<USER>'
      });

      uni.showToast({
        title: '已切换到管理端',
        icon: 'success'
      });
    },
    // 切换身份功能已移至role-switcher-pill组件中
  },
};
</script>

<style lang="scss" scoped>
// 现代化员工端仪表板样式
.modern-staff-dashboard {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f7fb 0%, #ffffff 100%);
  padding-bottom: 120rpx;
}

// 头部区域
.dashboard-header {
  position: relative;
  padding-bottom: 40rpx;
  margin-bottom: 30rpx;
  // 添加状态栏安全区域
  padding-top: calc(var(--status-bar-height) + 20rpx);

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 0;
  }

  .top-nav {
    display: flex;
    align-items: center;
    justify-content: space-between; /* 左右两侧分布 */
    padding: 0;
    height: 100rpx;
    position: relative; /* 为绝对定位的标题提供定位上下文 */

    .header-left {
      display: flex;
      align-items: center;
      gap: 12rpx; /* 按钮之间的间距 */
      z-index: 2; /* 确保按钮在标题之上 */

      .contact-manager-btn {
        display: flex;
        align-items: center;
        gap: 6rpx;
        padding: 10rpx 18rpx;
        border-radius: 25rpx;
        background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
        box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
        border: 1rpx solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
          box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.4);
        }

        text {
          font-size: 24rpx;
          color: #fff;
          font-weight: 600;
          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
        }
      }


    }

    .header-right {
      width: auto; /* 改为自动宽度 */
      padding-right: 0;
      display: flex;
      justify-content: flex-end;
      align-items: center;


      z-index: 2; /* 确保右侧内容在标题之上 */

      .current-company {
        text {
          font-size: 24rpx;
          color: #fff;
          font-weight: 500;
          text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
        }
      }
    }
  }

  // 角色切换按钮容器（员工端）
  .role-switch-container {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 16rpx;
    padding: 0 4rpx;

    .role-switch-btn {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
      backdrop-filter: blur(10rpx);
      border: none;
      border-radius: 24rpx;
      padding: 10rpx 20rpx;
      display: flex;
      align-items: center;
      gap: 8rpx;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1),
                  inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
      }

      &:active {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.25) 100%);
        transform: scale(0.96);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15),
                    inset 0 1rpx 0 rgba(255, 255, 255, 0.4);

        &::before {
          left: 100%;
        }
      }

      .switch-text {
        font-size: 24rpx;
        color: #fff;
        font-weight: 600;
        text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
        letter-spacing: 0.5rpx;
      }
    }
  }

  .header-content {
    position: relative;
    z-index: 2;

    .title {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
      text-align: center;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
      z-index: 1; /* 确保标题在背景之上，但在按钮之下 */
      white-space: nowrap; /* 防止文字换行 */
    }
  }
}

// 内容区域
.dashboard-content {
  padding: 0 20rpx;
}

// 现代化Tab样式
.modern-tabs {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  overflow: hidden;

  .tab-item {
    flex: 1;
    height: 80rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;

    text {
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    &.active {
      background: rgba(253, 209, 24, 0.1);

      text {
        color: #fdd118;
        font-weight: 600;
      }
    }

    .tab-line {
      position: absolute;
      bottom: 0;
      width: 40rpx;
      height: 4rpx;
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      border-radius: 2rpx;
    }

    &:active {
      background: rgba(253, 209, 24, 0.05);
    }
  }
}

// 通知栏样式
.notice-bar {
  background: rgba(255, 249, 230, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  padding: 20rpx 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.1);
  border: 1rpx solid rgba(255, 149, 0, 0.1);

  .notice-icon {
    flex-shrink: 0;
  }

  text {
    font-size: 26rpx;
    color: #ff9500;
    line-height: 1.4;
    flex: 1;
  }
}

// 订单列表样式
.order-list {
  padding: 0;
}

// 加载状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

// 空状态样式
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-text {
    margin: 20rpx 0;
    font-size: 28rpx;
    color: #999;
    text-align: center;
    line-height: 1.5;
  }

  .refresh-btn {
    padding: 20rpx 40rpx;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    color: #fff;
    border-radius: 30rpx;
    font-size: 28rpx;
    font-weight: 500;
    margin-top: 20rpx;

    &:active {
      transform: scale(0.95);
    }
  }
}

.order-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  }
}

// 新的订单卡片样式
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;

  .order-status-badge {
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
    font-size: 22rpx;
    font-weight: 500;

    &.status-pending {
      background: rgba(255, 193, 7, 0.1);
      color: #ffc107;
      border: 1rpx solid rgba(255, 193, 7, 0.3);
    }

    &.status-dispatched {
      background: rgba(33, 150, 243, 0.1);
      color: #2196f3;
      border: 1rpx solid rgba(33, 150, 243, 0.3);
    }

    &.status-in-progress, &.status-serving {
      background: rgba(76, 175, 80, 0.1);
      color: #4caf50;
      border: 1rpx solid rgba(76, 175, 80, 0.3);
    }

    &.status-completed, &.status-evaluated {
      background: rgba(156, 39, 176, 0.1);
      color: #9c27b0;
      border: 1rpx solid rgba(156, 39, 176, 0.3);
    }

    &.status-cancelled, &.status-rejected {
      background: rgba(244, 67, 54, 0.1);
      color: #f44336;
      border: 1rpx solid rgba(244, 67, 54, 0.3);
    }
  }

  .order-time {
    font-size: 24rpx;
    color: #999;
  }
}

.service-info {
  margin-bottom: 16rpx;

  .service-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8rpx;

    .service-name {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      flex: 1;
    }

    .service-type {
      padding: 4rpx 8rpx;
      background: rgba(253, 209, 24, 0.1);
      color: #fdd118;
      border-radius: 8rpx;
      font-size: 20rpx;
      font-weight: 500;
    }
  }

  .service-meta {
    display: flex;
    align-items: center;
    gap: 12rpx;

    .product-type {
      font-size: 24rpx;
      color: #666;
    }

    .buy-num {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
  padding: 12rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12rpx;

  .customer-avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;

    image {
      width: 100%;
      height: 100%;
    }

    .default-avatar {
      width: 100%;
      height: 100%;
      background: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .customer-details {
    flex: 1;

    .customer-name {
      display: flex;
      align-items: center;
      gap: 8rpx;
      margin-bottom: 4rpx;

      text:first-child {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
      }

      .customer-phone {
        font-size: 24rpx;
        color: #666;
      }
    }

    .customer-address {
      display: flex;
      align-items: center;
      gap: 4rpx;

      text {
        font-size: 24rpx;
        color: #999;
        line-height: 1.3;
      }
    }
  }
}

.service-schedule {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;

  .schedule-item {
    display: flex;
    align-items: center;
    gap: 6rpx;

    text {
      font-size: 26rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

.amount-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx;
  background: linear-gradient(135deg, rgba(253, 209, 24, 0.05) 0%, rgba(255, 128, 27, 0.05) 100%);
  border-radius: 12rpx;
  border: 1rpx solid rgba(253, 209, 24, 0.1);

  .amount-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .amount-label {
      font-size: 22rpx;
      color: #666;
      margin-bottom: 4rpx;
    }

    .amount-value {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .commission-highlight {
    .commission-value {
      font-size: 32rpx;
      font-weight: 700;
      color: #fdd118;
      text-shadow: 0 2rpx 4rpx rgba(253, 209, 24, 0.3);
    }
  }
}

.order-details {
  margin-bottom: 16rpx;

  .detail-row {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;

    .detail-label {
      font-size: 24rpx;
      color: #666;
      width: 120rpx;
      flex-shrink: 0;
    }

    .detail-value {
      font-size: 24rpx;
      color: #333;
      flex: 1;
      line-height: 1.4;
    }
  }
}

.order-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  text {
    font-size: 32rpx;
    font-weight: 500;
  }

  .order-distance {
    display: flex;
    align-items: center;

    text {
      font-size: 24rpx;
      color: #fdd118;
      margin-left: 4rpx;
    }
  }
}

.order-details {
  .detail-item {
    display: flex;
    margin-bottom: 10rpx;

    .label {
      font-size: 28rpx;
      color: #666;
      width: 100rpx;
    }

    .value {
      font-size: 28rpx;
      color: #333;
      flex: 1;
    }
  }
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f5f5f5;

  .publisher {
    font-size: 24rpx;
    color: #999;
  }

  .btn-group {
    display: flex;
    align-items: center;
  }

  .action-btn {
    color: #fff;
    margin-left: 12rpx;
    padding: 14rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 100rpx;
    box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    text {
      margin-left: 6rpx;
      color: #fff;
      font-weight: 500;
      position: relative;
      z-index: 2;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s ease;
      z-index: 1;
    }

    &:active {
      transform: scale(0.95);
      box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.2);

      &::before {
        left: 100%;
      }
    }
  }

  .confirm-btn {
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  }

  .reject-btn {
    background: linear-gradient(135deg, #ff5252 0%, #ff7676 100%);
  }

  .detail-btn {
    background: rgba(253, 209, 24, 0.1);
    border: 1rpx solid #fdd118;

    text {
      color: #fdd118 !important;
    }

    &::before {
      background: linear-gradient(90deg, transparent, rgba(253, 209, 24, 0.2), transparent);
    }
  }

  .contact-btn {
    background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
  }
}

// 空状态和加载更多样式
.no-more,
.empty {
  text-align: center;
  padding: 60rpx 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.05);

  text {
    font-size: 28rpx;
    color: #999;
    font-weight: 500;
  }
}

// 底部TabBar样式
.staff-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;

    image {
      width: 44rpx;
      height: 44rpx;
      margin-bottom: 6rpx;
      transition: all 0.3s ease;
    }

    text {
      font-size: 24rpx;
      color: #999;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    &.active {
      text {
        color: #fdd118;
        font-weight: 600;
      }

      image {
        transform: scale(1.1);
      }
    }

    &:active {
      background: rgba(253, 209, 24, 0.05);
    }
  }
}

// 简化版订单卡片的额外样式
.order-item-simple {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 15rpx rgba(0,0,0,0.15);
  }
}

// 功能按钮样式
.function-btn {
  transition: all 0.2s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.9);
    opacity: 0.7;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);
  }

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
  }
}

// 主要操作按钮样式
.action-button {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }

  &:active {
    transform: scale(0.96);
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.2);

    &::before {
      left: 100%;
    }
  }

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.15);
  }
}

// 开始服务弹窗样式
.start-service-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40rpx;

  .modal-content {
    background: #fff;
    border-radius: 20rpx;
    width: 100%;
    max-width: 600rpx;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }

    .close-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      color: #999;
      border-radius: 50%;
      background: #f5f5f5;
    }
  }

  .location-status {
    display: flex;
    align-items: center;
    padding: 30rpx;
    margin: 20rpx 30rpx;
    border-radius: 12rpx;

    &.valid {
      background: rgba(76, 175, 80, 0.1);
      border: 1rpx solid rgba(76, 175, 80, 0.3);
    }

    &.invalid {
      background: rgba(255, 152, 0, 0.1);
      border: 1rpx solid rgba(255, 152, 0, 0.3);
    }

    .status-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #fff;

      .valid & {
        background: #4caf50;
      }

      .invalid & {
        background: #ff9800;
      }
    }

    .status-content {
      flex: 1;

      .status-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        margin-bottom: 8rpx;

        .valid & {
          color: #4caf50;
        }

        .invalid & {
          color: #ff9800;
        }
      }

      .status-desc {
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .tip-message {
    padding: 20rpx 30rpx;
    margin: 0 30rpx 20rpx;
    background: rgba(33, 150, 243, 0.1);
    border-radius: 12rpx;

    text {
      font-size: 28rpx;
      color: #2196f3;
      line-height: 1.5;
    }
  }

  .upload-section {
    padding: 0 30rpx 20rpx;

    .section-title {
      margin-bottom: 20rpx;

      text {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .required {
        color: #f44336;
        margin-left: 8rpx;
      }
    }

    .upload-area {
      width: 100%;
      height: 300rpx;
      border: 2rpx dashed #ddd;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fafafa;
      margin-bottom: 20rpx;

      .upload-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;

        .upload-icon {
          font-size: 60rpx;
          margin-bottom: 20rpx;
        }

        .upload-text {
          font-size: 28rpx;
          color: #666;
        }
      }

      .uploaded-image {
        width: 100%;
        height: 100%;
        border-radius: 12rpx;
        object-fit: contain;
        background: #fff;
      }
    }

    .upload-tip {
      text {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .order-info {
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 20rpx;
    margin: 0 30rpx 20rpx;

    .info-item {
      display: flex;
      margin-bottom: 10rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 28rpx;
        color: #666;
        width: 120rpx;
      }

      .value {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }
    }
  }

  .modal-actions {
    display: flex;
    padding: 30rpx;
    gap: 20rpx;

    .action-button {
      flex: 1;
      height: 88rpx;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      font-weight: 600;

      &.cancel {
        background: #f5f5f5;
        color: #666;
      }

      &.confirm {
        background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
        color: #fff;
        box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);

        &.disabled {
          background: #ccc;
          box-shadow: none;
        }
      }
    }
  }
}
</style>

