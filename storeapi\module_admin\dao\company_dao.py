from sqlalchemy import select, and_, func, text
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Tuple, Dict, Any
from exceptions.exception import QueryException, DatabaseException
from utils.log_util import logger
from datetime import datetime
from module_admin.entity.do.company import Company

class CompanyDao:
    """公司数据访问层"""
    
    @staticmethod
    async def get_account_balance(db: AsyncSession, company_id: str) -> Dict[str, Any]:
        """
        获取公司账户余额
        
        :param db: 数据库会话
        :param company_id: 公司ID
        :return: 账户余额信息
        """
        try:
            # 由于我们没有实际的公司账户表，这里模拟返回数据
            # 在实际项目中，应该使用SQLAlchemy查询数据库
            
            # 模拟数据
            account_balance = {
                "company_id": company_id,
                "balance": 10000.00,
                "frozen_balance": 0.00,
                "available_balance": 10000.00,
                "credit_limit": 5000.00,
                "total_income": 50000.00,
                "total_expense": 40000.00,
                "last_update_time": "2023-06-01 10:00:00"
            }
            
            return account_balance
            
        except Exception as e:
            logger.error(f"查询公司账户余额失败: {str(e)}")
            # 向上传递异常，不吞噬错误
            raise QueryException(message=f"查询公司账户余额失败: {str(e)}")
    
    @staticmethod
    async def get_insurance_list(db: AsyncSession, page: int, size: int) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取保险列表
        
        :param db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :return: 保险列表和总数
        """
        try:
            # 由于我们没有实际的保险表，这里模拟返回数据
            # 在实际项目中，应该使用SQLAlchemy查询数据库
            
            # 模拟数据
            insurance_list = [
                {
                    "id": "1",
                    "name": "家政人员意外险",
                    "code": "JZYW001",
                    "type": "意外险",
                    "price": 100.00,
                    "period": "1年",
                    "coverage": "意外伤害、意外医疗",
                    "insurer": "平安保险",
                    "description": "为家政人员提供意外伤害和意外医疗保障",
                    "status": "1",
                    "create_time": "2023-05-01 10:00:00",
                    "update_time": "2023-05-01 10:00:00"
                },
                {
                    "id": "2",
                    "name": "家政人员责任险",
                    "code": "JZZR001",
                    "type": "责任险",
                    "price": 200.00,
                    "period": "1年",
                    "coverage": "第三者责任、财产损失",
                    "insurer": "太平洋保险",
                    "description": "为家政人员提供第三者责任和财产损失保障",
                    "status": "1",
                    "create_time": "2023-05-02 10:00:00",
                    "update_time": "2023-05-02 10:00:00"
                }
            ]
            
            # 模拟总数
            total = len(insurance_list)
            
            # 模拟分页
            start = (page - 1) * size
            end = start + size
            insurance_list = insurance_list[start:end]
            
            return insurance_list, total
            
        except Exception as e:
            logger.error(f"查询保险列表失败: {str(e)}")
            # 向上传递异常，不吞噬错误
            raise QueryException(message=f"查询保险列表失败: {str(e)}")
    
    @staticmethod
    async def find_insurance_order_list(
        db: AsyncSession, 
        page: int, 
        size: int, 
        status: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取保险订单列表
        
        :param db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param status: 订单状态
        :return: 保险订单列表和总数
        """
        try:
            # 由于我们没有实际的保险订单表，这里模拟返回数据
            # 在实际项目中，应该使用SQLAlchemy查询数据库
            
            # 模拟数据
            insurance_order_list = [
                {
                    "id": "1",
                    "order_no": "INS20230601001",
                    "insurance_id": "1",
                    "insurance_name": "家政人员意外险",
                    "aunt_id": "4a7968d726f8d0db75c76a9a2cec8269",
                    "aunt_name": "郑思敏",
                    "price": 100.00,
                    "period": "1年",
                    "start_date": "2023-06-01",
                    "end_date": "2024-05-31",
                    "status": "1",
                    "status_name": "已生效",
                    "pay_status": "1",
                    "pay_status_name": "已支付",
                    "pay_time": "2023-06-01 10:00:00",
                    "create_time": "2023-06-01 09:00:00",
                    "update_time": "2023-06-01 10:00:00"
                },
                {
                    "id": "2",
                    "order_no": "INS20230602001",
                    "insurance_id": "2",
                    "insurance_name": "家政人员责任险",
                    "aunt_id": "4a7968d726f8d0db75c76a9a2cec8269",
                    "aunt_name": "郑思敏",
                    "price": 200.00,
                    "period": "1年",
                    "start_date": "2023-06-02",
                    "end_date": "2024-06-01",
                    "status": "1",
                    "status_name": "已生效",
                    "pay_status": "1",
                    "pay_status_name": "已支付",
                    "pay_time": "2023-06-02 10:00:00",
                    "create_time": "2023-06-02 09:00:00",
                    "update_time": "2023-06-02 10:00:00"
                }
            ]
            
            # 如果有状态，进行过滤
            if status:
                insurance_order_list = [order for order in insurance_order_list if order["status"] == status]
            
            # 模拟总数
            total = len(insurance_order_list)
            
            # 模拟分页
            start = (page - 1) * size
            end = start + size
            insurance_order_list = insurance_order_list[start:end]
            
            return insurance_order_list, total
            
        except Exception as e:
            logger.error(f"查询保险订单列表失败: {str(e)}")
            # 向上传递异常，不吞噬错误
            raise QueryException(message=f"查询保险订单列表失败: {str(e)}")
    
    @staticmethod
    async def find_store_detail(db: AsyncSession, store_id: str) -> Dict[str, Any]:
        """
        获取门店详情
        
        :param db: 数据库会话
        :param store_id: 门店ID
        :return: 门店详情
        """
        try:
            # 由于我们没有实际的门店表，这里模拟返回数据
            # 在实际项目中，应该使用SQLAlchemy查询数据库
            
            # 模拟数据
            if store_id == "48499":
                store = {
                    "id": "48499",
                    "uuid": "06512cdd7fef5ab028f755748926e2a3",
                    "name": "厦门小羽佳线索店",
                    "number": "66240808660118017",
                    "company_id": "1",
                    "company_name": "厦门市小羽佳电子商务有限公司",
                    "address": "厦门市思明区湖滨南路76号",
                    "phone": "0592-5178888",
                    "contact": "郑思敏",
                    "contact_mobile": "***********",
                    "logo": "https://media.jiazhengye.cn/FkFCrb8HkyhYjrKSiiJ4IKi7jPOO",
                    "business_hours": "09:00-18:00",
                    "province_id": "14",
                    "province_name": "福建",
                    "city_id": "153",
                    "city_name": "厦门",
                    "area_id": "1315",
                    "area_name": "思明区",
                    "lng": 118.134756,
                    "lat": 24.518694,
                    "status": "1",
                    "status_name": "正常",
                    "create_time": "2023-01-01 00:00:00",
                    "update_time": "2023-01-01 00:00:00"
                }
                return store
            return None
            
        except Exception as e:
            logger.error(f"查询门店详情失败: {str(e)}")
            # 向上传递异常，不吞噬错误
            raise QueryException(message=f"查询门店详情失败: {str(e)}")
    
    @staticmethod
    async def find_store(
        db: AsyncSession, 
        page: int, 
        size: int, 
        keywords: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        查询门店列表
        
        :param db: 数据库会话
        :param page: 页码
        :param size: 每页数量
        :param keywords: 关键词
        :return: 门店列表和总数
        """
        try:
            # 由于我们没有实际的门店表，这里模拟返回数据
            # 在实际项目中，应该使用SQLAlchemy查询数据库
            
            # 模拟数据
            store_list = [
                {
                    "id": "48499",
                    "uuid": "06512cdd7fef5ab028f755748926e2a3",
                    "name": "厦门小羽佳线索店",
                    "number": "66240808660118017",
                    "company_id": "1",
                    "company_name": "厦门市小羽佳电子商务有限公司",
                    "address": "厦门市思明区湖滨南路76号",
                    "phone": "0592-5178888",
                    "contact": "郑思敏",
                    "contact_mobile": "***********",
                    "logo": "https://media.jiazhengye.cn/FkFCrb8HkyhYjrKSiiJ4IKi7jPOO",
                    "business_hours": "09:00-18:00",
                    "province_id": "14",
                    "province_name": "福建",
                    "city_id": "153",
                    "city_name": "厦门",
                    "area_id": "1315",
                    "area_name": "思明区",
                    "lng": 118.134756,
                    "lat": 24.518694,
                    "status": "1",
                    "status_name": "正常",
                    "create_time": "2023-01-01 00:00:00",
                    "update_time": "2023-01-01 00:00:00"
                }
            ]
            
            # 如果有关键词，进行过滤
            if keywords:
                store_list = [store for store in store_list if keywords in store["name"] or keywords in store["address"]]
            
            # 模拟总数
            total = len(store_list)
            
            # 模拟分页
            start = (page - 1) * size
            end = start + size
            store_list = store_list[start:end]
            
            return store_list, total
            
        except Exception as e:
            logger.error(f"查询门店列表失败: {str(e)}")
            # 向上传递异常，不吞噬错误
            raise QueryException(message=f"查询门店列表失败: {str(e)}")
    
    @staticmethod
    async def create_pay_order(db: AsyncSession, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建支付订单
        
        :param db: 数据库会话
        :param order_data: 订单数据
        :return: 创建的订单
        """
        try:
            # 由于我们没有实际的支付订单表，这里模拟返回数据
            # 在实际项目中，应该使用SQLAlchemy插入数据库
            
            # 模拟数据
            order = {
                "id": "3",
                "order_no": "PAY20230603001",
                "amount": order_data.get("amount", 0.00),
                "pay_type": order_data.get("pay_type", "1"),
                "pay_type_name": "微信支付",
                "status": "0",
                "status_name": "待支付",
                "create_time": "2023-06-03 10:00:00",
                "update_time": "2023-06-03 10:00:00"
            }
            
            return order
            
        except Exception as e:
            logger.error(f"创建支付订单失败: {str(e)}")
            # 向上传递异常，不吞噬错误
            raise DatabaseException(message=f"创建支付订单失败: {str(e)}")

    @staticmethod
    async def get_system_versions_with_company_relation(db: AsyncSession, company_uuid: str) -> List[Dict[str, Any]]:
        """
        获取系统版本列表及公司关联信息

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 系统版本列表数据
        """
        try:
            # 查询所有可用的软件版本及公司关联信息
            query = text("""
                SELECT
                    sv.uuid as version_uuid,
                    sv.name as version_name,
                    sv.description,
                    sv.price,
                    sv.sort_order,
                    cvr.expire_time,
                    cvr.status as relation_status,
                    cvr.purchase_time,
                    CASE
                        WHEN cvr.company_uuid IS NULL THEN '购买'
                        WHEN cvr.expire_time < NOW() THEN '续费'
                        ELSE '续费'
                    END as button_status
                FROM software_version sv
                LEFT JOIN company_version_relation cvr
                    ON sv.uuid = cvr.version_uuid
                    AND cvr.company_uuid = :company_uuid
                    AND cvr.status = 1
                WHERE sv.is_available = 1
                ORDER BY sv.sort_order ASC
            """)

            result = await db.execute(query, {"company_uuid": company_uuid})
            rows = result.fetchall()

            versions = []
            for row in rows:
                version_data = {
                    "version_uuid": row.version_uuid,
                    "version_name": row.version_name,
                    "description": row.description,
                    "price": str(row.price),
                    "sort_order": row.sort_order,
                    "expire_time": row.expire_time.strftime("%Y-%m-%d %H:%M:%S") if row.expire_time else None,
                    "relation_status": row.relation_status,
                    "purchase_time": row.purchase_time.strftime("%Y-%m-%d %H:%M:%S") if row.purchase_time else None,
                    "button_status": row.button_status
                }
                versions.append(version_data)

            return versions

        except Exception as e:
            logger.error(f"查询系统版本列表失败: {str(e)}")
            # 向上传递异常，不吞噬错误
            raise QueryException(message=f"查询系统版本列表失败: {str(e)}")

    @staticmethod
    async def get_company_payment_info(db: AsyncSession, company_uuid: str) -> Dict[str, Any]:
        """
        获取公司支付方式列表和账户余额

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 支付方式列表和账户余额信息
        """
        try:
            logger.info(f"查询公司支付信息，company_uuid: {company_uuid}")

            # 查询公司账户余额
            balance_query = """
                SELECT balance
                FROM company
                WHERE id = :company_uuid
            """

            balance_result = await db.execute(text(balance_query), {"company_uuid": company_uuid})
            balance_row = balance_result.fetchone()

            balance = "0.00"
            if balance_row:
                balance = str(balance_row._mapping.get("balance", "0.00"))
                logger.info(f"查询到公司余额: {balance}")
            else:
                logger.warning(f"未找到公司信息，company_uuid: {company_uuid}")

            # 查询公司支付方式列表
            pay_type_query = """
                SELECT
                    id,
                    pay_type_code,
                    pay_type_name,
                    icon_url,
                    is_enabled,
                    is_preset,
                    channel_config,
                    sort_order,
                    remark
                FROM pay_type
                WHERE company_uuid = :company_uuid
                AND is_enabled = 1
                ORDER BY sort_order ASC, id ASC
            """

            pay_type_result = await db.execute(text(pay_type_query), {"company_uuid": company_uuid})
            pay_type_rows = pay_type_result.fetchall()

            # 处理支付方式列表
            pay_types = []
            for row in pay_type_rows:
                pay_type_dict = {}
                for key, value in row._mapping.items():
                    pay_type_dict[key] = value
                pay_types.append(pay_type_dict)

            logger.info(f"查询到支付方式数量: {len(pay_types)}")

            # 构建返回数据
            result = {
                "balance": balance,
                "pay_types": pay_types
            }

            logger.info(f"返回支付信息: {result}")
            return result

        except Exception as e:
            logger.error(f"获取公司支付信息失败: {str(e)}")
            # 向上传递异常，不吞噬错误
            raise QueryException(message=f"获取公司支付信息失败: {str(e)}")

    @staticmethod
    async def preview_software_renewal(
        db: AsyncSession,
        company_uuid: str,
        version_uuid: str,
        renewal_months: int
    ) -> Dict[str, Any]:
        """
        软件续费预览

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :param version_uuid: 软件版本UUID
        :param renewal_months: 续费月数
        :return: 续费预览信息
        """
        try:
            # 调用存储过程进行预览（这里简化为直接查询）
            preview_query = text("""
                SELECT
                    c.balance as current_balance,
                    sv.name as version_name,
                    sv.price as unit_price,
                    cvr.expire_time as current_expire_time,
                    (sv.price * :renewal_years) as total_amount,
                    DATE_ADD(cvr.expire_time, INTERVAL :renewal_years YEAR) as new_expire_time,
                    CASE
                        WHEN c.balance >= (sv.price * :renewal_years) THEN 1
                        ELSE 0
                    END as is_balance_sufficient,
                    CASE
                        WHEN c.balance < (sv.price * :renewal_years) THEN (sv.price * :renewal_years) - c.balance
                        ELSE 0
                    END as shortage_amount
                FROM company c
                CROSS JOIN software_version sv
                LEFT JOIN company_version_relation cvr
                    ON c.id = cvr.company_uuid
                    AND sv.uuid = cvr.version_uuid
                    AND cvr.status = 1
                WHERE c.id = :company_uuid
                    AND sv.uuid = :version_uuid
                    AND sv.is_available = 1
            """)

            result = await db.execute(preview_query, {
                "company_uuid": company_uuid,
                "version_uuid": version_uuid,
                "renewal_years": renewal_months // 12  # 将月数转换为年数
            })
            row = result.fetchone()

            if not row:
                from exceptions.exception import ValidationException
                raise ValidationException(message="公司或软件版本不存在")

            preview_data = {
                "version_name": row.version_name,
                "current_expire_time": row.current_expire_time.strftime("%Y-%m-%d %H:%M:%S") if row.current_expire_time else None,
                "renewal_months": renewal_months,
                "renewal_years": renewal_months // 12,
                "unit_price": str(row.unit_price),
                "total_amount": str(row.total_amount),
                "new_expire_time": row.new_expire_time.strftime("%Y-%m-%d %H:%M:%S") if row.new_expire_time else None,
                "current_balance": str(row.current_balance),
                "is_balance_sufficient": bool(row.is_balance_sufficient),
                "shortage_amount": str(row.shortage_amount) if row.shortage_amount > 0 else None
            }

            return preview_data

        except Exception as e:
            logger.error(f"软件续费预览失败: {str(e)}")
            raise QueryException(message=f"软件续费预览失败: {str(e)}")

    @staticmethod
    async def execute_software_renewal(
        db: AsyncSession,
        company_uuid: str,
        version_uuid: str,
        renewal_months: int,
        operator_id: str,
        operator_name: str,
        remark: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行软件续费

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :param version_uuid: 软件版本UUID
        :param renewal_months: 续费月数
        :param operator_id: 操作人ID
        :param operator_name: 操作人姓名
        :param remark: 续费备注
        :return: 续费执行结果
        """
        try:
            renewal_years = renewal_months // 12  # 转换为年数

            # 1. 检查公司是否存在并获取余额和名称
            company_query = text("""
                SELECT balance, name
                FROM company
                WHERE id = :company_uuid
            """)
            company_result = await db.execute(company_query, {"company_uuid": company_uuid})
            company_row = company_result.fetchone()

            if not company_row:
                from exceptions.exception import ValidationException
                raise ValidationException(message="公司不存在")

            company_balance = float(company_row.balance)
            company_name = company_row.name

            # 2. 检查软件版本是否存在并获取价格
            version_query = text("""
                SELECT price, name
                FROM software_version
                WHERE uuid = :version_uuid AND is_available = 1
            """)
            version_result = await db.execute(version_query, {"version_uuid": version_uuid})
            version_row = version_result.fetchone()

            if not version_row:
                from exceptions.exception import ValidationException
                raise ValidationException(message="软件版本不存在或已下架")

            version_price = float(version_row.price)
            version_name = version_row.name

            # 3. 检查公司是否已购买该软件版本
            relation_query = text("""
                SELECT expire_time
                FROM company_version_relation
                WHERE company_uuid = :company_uuid
                  AND version_uuid = :version_uuid
                  AND status = 1
            """)
            relation_result = await db.execute(relation_query, {
                "company_uuid": company_uuid,
                "version_uuid": version_uuid
            })
            relation_row = relation_result.fetchone()

            if not relation_row:
                from exceptions.exception import ValidationException
                raise ValidationException(message="公司未购买该软件版本，无法续费")

            current_expire_time = relation_row.expire_time

            # 4. 计算续费金额
            total_amount = version_price * renewal_years

            # 5. 检查余额是否充足
            if company_balance < total_amount:
                from exceptions.exception import BusinessException
                raise BusinessException(message=f"余额不足，当前余额：{company_balance}元，需要：{total_amount}元")

            # 6. 生成订单号和交易流水号
            import time
            import random
            timestamp = int(time.time() * 1000)
            order_no = f"PO{timestamp}{random.randint(1000, 9999)}"
            transaction_no = f"TXN{timestamp}{random.randint(1000, 9999)}"

            # 7. 计算续费后的到期时间
            from datetime import datetime, timedelta
            if isinstance(current_expire_time, str):
                current_expire_time = datetime.strptime(current_expire_time, "%Y-%m-%d %H:%M:%S")
            new_expire_time = current_expire_time.replace(year=current_expire_time.year + renewal_years)

            # 8. 扣减公司余额
            balance_after = company_balance - total_amount

            update_balance_query = text("""
                UPDATE company
                SET balance = :balance_after,
                    update_time = NOW()
                WHERE id = :company_uuid
            """)
            await db.execute(update_balance_query, {
                "balance_after": balance_after,
                "company_uuid": company_uuid
            })

            # 9. 延长软件版本有效期
            update_relation_query = text("""
                UPDATE company_version_relation
                SET expire_time = :new_expire_time,
                    update_time = NOW(),
                    remark = CASE
                        WHEN IFNULL(remark, '') = ''
                        THEN CONCAT('续费', :renewal_years, '年，订单号：', :order_no)
                        ELSE CONCAT(remark, '; 续费', :renewal_years, '年，订单号：', :order_no)
                    END
                WHERE company_uuid = :company_uuid
                  AND version_uuid = :version_uuid
                  AND status = 1
            """)
            await db.execute(update_relation_query, {
                "new_expire_time": new_expire_time,
                "renewal_years": renewal_years,
                "order_no": order_no,
                "company_uuid": company_uuid,
                "version_uuid": version_uuid
            })

            # 10. 插入交易流水记录
            transaction_query = text("""
                INSERT INTO company_transaction (
                    company_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    related_order_no, pay_type, operator_id, operator_name,
                    description, remark, transaction_status, created_by
                ) VALUES (
                    :company_uuid, :transaction_no, :business_type, :business_type_name,
                    :transaction_type, :amount, :balance_before, :balance_after,
                    :related_order_no, :pay_type, :operator_id, :operator_name,
                    :description, :remark, :transaction_status, :created_by
                )
            """)

            transaction_description = f"软件续费：{version_name}，续费{renewal_years}年"
            transaction_remark = f"续费{renewal_years}年，从{current_expire_time.strftime('%Y-%m-%d')}延长到{new_expire_time.strftime('%Y-%m-%d')}"

            await db.execute(transaction_query, {
                "company_uuid": company_uuid,
                "transaction_no": transaction_no,
                "business_type": "SOFTWARE_RENEWAL",
                "business_type_name": "软件续费",
                "transaction_type": 2,  # 2=支出
                "amount": total_amount,
                "balance_before": company_balance,
                "balance_after": balance_after,
                "related_order_no": order_no,
                "pay_type": "BALANCE",
                "operator_id": operator_id,
                "operator_name": operator_name,
                "description": transaction_description,
                "remark": transaction_remark,
                "transaction_status": "SUCCESS",  # 余额扣费直接成功
                "created_by": operator_id
            })

            # 11. 显式提交事务
            await db.commit()

            # 12. 返回续费结果
            renewal_data = {
                "order_no": order_no,
                "company_uuid": company_uuid,
                "company_name": company_name,
                "version_name": version_name,
                "renewal_months": renewal_months,
                "renewal_years": renewal_years,
                "unit_price": str(version_price),
                "total_amount": str(total_amount),
                "balance_before": str(company_balance),
                "balance_after": str(balance_after),
                "new_expire_time": new_expire_time.strftime("%Y-%m-%d %H:%M:%S"),
                "transaction_no": transaction_no,
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            return renewal_data

        except Exception as e:
            logger.error(f"执行软件续费失败: {str(e)}")
            raise QueryException(message=f"执行软件续费失败: {str(e)}")

    @staticmethod
    async def check_company_version_expiry(db: AsyncSession, company_uuid: str) -> Dict[str, Any]:
        """
        检查公司版本过期状态

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 版本过期检查结果
        """
        try:
            # 查询公司所有版本的过期时间
            query = text("""
                SELECT
                    cvr.version_uuid,
                    sv.name as version_name,
                    cvr.expire_time,
                    CASE
                        WHEN cvr.expire_time > NOW() THEN 0
                        ELSE 1
                    END as is_expired
                FROM company_version_relation cvr
                JOIN software_version sv ON cvr.version_uuid = sv.uuid
                WHERE cvr.company_uuid = :company_uuid
                AND cvr.status = 1
                ORDER BY cvr.expire_time ASC
            """)

            result = await db.execute(query, {"company_uuid": company_uuid})
            rows = result.fetchall()

            if not rows:
                # 如果没有购买任何版本，视为过期
                return {
                    "all_expired": True,
                    "has_versions": False,
                    "expired_count": 0,
                    "total_count": 0,
                    "versions": [],
                    "message": "未购买任何软件版本"
                }

            versions = []
            expired_count = 0
            total_count = len(rows)

            for row in rows:
                version_info = {
                    "version_uuid": row.version_uuid,
                    "version_name": row.version_name,
                    "expire_time": row.expire_time.strftime("%Y-%m-%d %H:%M:%S") if row.expire_time else None,
                    "is_expired": bool(row.is_expired)
                }
                versions.append(version_info)

                if row.is_expired:
                    expired_count += 1

            # 判断是否所有版本都已过期
            all_expired = expired_count == total_count

            return {
                "all_expired": all_expired,
                "has_versions": True,
                "expired_count": expired_count,
                "total_count": total_count,
                "versions": versions,
                "message": "所有版本都已过期，请联系平台进行续费" if all_expired else "版本状态正常"
            }

        except Exception as e:
            logger.error(f"检查公司版本过期状态失败: {str(e)}")
            raise QueryException(message=f"检查版本过期状态失败: {str(e)}")

    @staticmethod
    async def get_company_balance_info(db: AsyncSession, company_id: str) -> Dict[str, Any]:
        """
        获取公司余额和冻结金额信息

        :param db: 数据库会话
        :param company_id: 公司ID
        :return: 余额信息
        """
        try:
            query = select(Company.balance, Company.frozen_amount).where(
                and_(Company.id == company_id, Company.is_delete == '0')
            )
            result = await db.execute(query)
            row = result.fetchone()

            if not row:
                raise QueryException(message="公司信息不存在")

            available_balance = float(row.balance or 0)  # balance就是可用余额
            frozen_amount = float(row.frozen_amount or 0)
            total_balance = available_balance + frozen_amount  # 账户总余额

            return {
                "company_id": company_id,
                "available_balance": available_balance,  # 可用余额（balance字段）
                "frozen_amount": frozen_amount,          # 冻结金额
                "total_balance": total_balance           # 账户总余额
            }

        except Exception as e:
            logger.error(f"获取公司余额信息失败: {str(e)}")
            raise QueryException(message=f"获取公司余额信息失败: {str(e)}")

    @staticmethod
    async def update_company_frozen_amount(
        db: AsyncSession,
        company_id: str,
        frozen_amount_change: float,
        balance_change: float = 0
    ) -> bool:
        """
        更新公司冻结金额和余额

        :param db: 数据库会话
        :param company_id: 公司ID
        :param frozen_amount_change: 冻结金额变化（正数为增加，负数为减少）
        :param balance_change: 余额变化（正数为增加，负数为减少）
        :return: 是否更新成功
        """
        try:
            # 构建更新SQL
            if balance_change != 0:
                update_sql = text("""
                    UPDATE company
                    SET frozen_amount = frozen_amount + :frozen_amount_change,
                        balance = balance + :balance_change
                    WHERE id = :company_id AND is_delete = '0'
                """)
                params = {
                    "frozen_amount_change": frozen_amount_change,
                    "balance_change": balance_change,
                    "company_id": company_id
                }
            else:
                update_sql = text("""
                    UPDATE company
                    SET frozen_amount = frozen_amount + :frozen_amount_change
                    WHERE id = :company_id AND is_delete = '0'
                """)
                params = {
                    "frozen_amount_change": frozen_amount_change,
                    "company_id": company_id
                }

            result = await db.execute(update_sql, params)
            return result.rowcount > 0

        except Exception as e:
            logger.error(f"更新公司冻结金额失败: {str(e)}")
            raise DatabaseException(message=f"更新公司冻结金额失败: {str(e)}")

    @staticmethod
    async def get_company_withdrawal_config(db: AsyncSession, company_uuid: str) -> Dict[str, Any]:
        """
        获取公司提现配置

        :param db: 数据库会话
        :param company_uuid: 公司UUID
        :return: 提现配置信息
        """
        try:
            logger.info(f"查询公司提现配置，company_uuid: {company_uuid}")

            # 查询公司提现费率配置
            config_query = """
                SELECT
                    id,
                    name,
                    withdrawal_fee_rate
                FROM company
                WHERE id = :company_uuid AND is_delete = '0'
            """

            config_result = await db.execute(text(config_query), {"company_uuid": company_uuid})
            config_row = config_result.fetchone()

            if not config_row:
                logger.warning(f"未找到公司信息，company_uuid: {company_uuid}")
                raise QueryException(message="公司信息不存在")

            # 获取提现费率，如果为空则使用默认值
            withdrawal_fee_rate = float(config_row._mapping.get("withdrawal_fee_rate", 0.0000))
            company_name = config_row._mapping.get("name", "")

            # 构建返回数据
            result = {
                "company_uuid": company_uuid,
                "company_name": company_name,
                "withdrawal_fee_rate": withdrawal_fee_rate,  # 个人提现代扣个税费率
                "withdrawal_fee_rate_percent": f"{withdrawal_fee_rate * 100:.2f}%"  # 转换为百分比显示
            }

            logger.info(f"查询到公司提现配置: {result}")
            return result

        except QueryException:
            # 查询异常直接向上传递
            raise
        except Exception as e:
            logger.error(f"查询公司提现配置失败: {str(e)}")
            raise QueryException(message=f"查询公司提现配置失败: {str(e)}")
