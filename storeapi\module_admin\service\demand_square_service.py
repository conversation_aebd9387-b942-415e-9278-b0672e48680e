from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, update, text
from sqlalchemy.orm import selectinload
from typing import Optional, Tuple, List
from datetime import datetime, timedelta
from decimal import Decimal
import math
import uuid
import time
import random

from module_admin.entity.do.demand_square import DemandSquare
from module_admin.entity.do.service_staff import ServiceStaff
from module_admin.entity.do.order import Order
from module_admin.entity.do.store import Store
from module_admin.entity.do.service_product import ServiceProduct
from module_admin.entity.vo.demand_square_vo import (
    DemandSquareQueryVO,
    DemandSquareResponseVO,
    DemandSquareListResponseVO,
    DemandSquareStatsVO,
    DemandGrabRequestVO,
    DemandGrabResponseVO,
    ShareOrderRequestVO,
    StaffSelectionRequestVO,
    AvailableStaffVO
)
from exceptions.exception import ValidationException, BusinessException, QueryException, DatabaseException
from utils.log_util import logger

class DemandSquareService:
    """需求广场服务类"""
    
    @staticmethod
    async def get_demand_list(
        db: AsyncSession, 
        query_params: DemandSquareQueryVO
    ) -> DemandSquareListResponseVO:
        """
        获取需求列表
        """
        try:
            # 构建查询条件
            current_time = datetime.now()
            conditions = [
                DemandSquare.is_delete == 0,  # 未删除
            ]
            
            # 业务类型筛选
            if query_params.business_type is not None:
                conditions.append(DemandSquare.business_type == query_params.business_type)
            
            # 需求状态筛选
            if query_params.demand_status is not None:
                conditions.append(DemandSquare.demand_status == query_params.demand_status)
            
            # 查询总数
            count_query = select(func.count(DemandSquare.id)).where(and_(*conditions))
            total_result = await db.execute(count_query)
            total = total_result.scalar() or 0
            
            # 计算分页
            offset = (query_params.page - 1) * query_params.size
            pages = math.ceil(total / query_params.size) if total > 0 else 0
            
            # 查询数据
            data_query = (
                select(DemandSquare)
                .where(and_(*conditions))
                .order_by(DemandSquare.create_time.desc())
                .offset(offset)
                .limit(query_params.size)
            )
            
            result = await db.execute(data_query)
            demands = result.scalars().all()
            
            # 转换为响应VO
            demand_list = []
            
            for demand in demands:
                # 根据订单来源和业务类型计算剩余时间（秒）
                remaining_seconds = None

                # 计算过期时间 - 区分订单来源
                if demand.source_order_id:
                    # 门店共享的订单：使用用户设置的自定义过期时间
                    calculated_expire_time = demand.expire_time
                else:
                    # 云平台同步的订单：使用固定过期时间
                    if demand.business_type == 1:  # 到家订单：30分钟
                        calculated_expire_time = demand.create_time + timedelta(minutes=30)
                    elif demand.business_type == 2:  # 到店订单：24小时
                        calculated_expire_time = demand.create_time + timedelta(hours=24)
                    else:
                        # 默认使用数据库中的expire_time
                        calculated_expire_time = demand.expire_time

                # 计算剩余时间和动态状态
                is_expired = calculated_expire_time <= current_time
                if not is_expired:
                    remaining_seconds = int((calculated_expire_time - current_time).total_seconds())

                # 动态设置状态：如果已过期且原状态是待抢单，则设为已过期
                dynamic_status = demand.demand_status
                if is_expired and demand.demand_status == 1:
                    dynamic_status = -1  # 已过期

                # 状态文本
                status_map = {
                    -1: "已过期",
                    1: "待抢单",
                    2: "已抢单",
                    3: "已售出"
                }
                status_text = status_map.get(dynamic_status, "未知")
                
                # 业务类型文本
                business_type_map = {
                    1: "到家服务",
                    2: "到店服务"
                }
                business_type_text = business_type_map.get(demand.business_type, "未知")
                
                demand_vo = DemandSquareResponseVO(
                    id=demand.id,
                    uuid=demand.uuid,
                    platform_order_number=demand.platform_order_number,
                    business_type=demand.business_type,
                    commission_amount=demand.commission_amount,
                    service_project=demand.service_project,
                    service_address=demand.service_address,
                    service_time=demand.service_time,
                    demand_status=dynamic_status,  # 使用动态状态
                    grab_store_uuid=demand.grab_store_uuid,  # 抢单门店UUID
                    grab_store_name=demand.grab_store_name,  # 抢单门店名称
                    grab_user_uuid=demand.grab_user_uuid,    # 抢单用户UUID
                    grab_user_name=demand.grab_user_name,    # 抢单用户名称
                    customer_name=demand.customer_name,
                    customer_phone=demand.customer_phone,
                    service_requirements=demand.service_requirements,
                    source_platform=demand.source_platform,
                    expire_time=calculated_expire_time,  # 使用计算的过期时间
                    create_time=demand.create_time,
                    remaining_seconds=remaining_seconds,
                    status_text=status_text,
                    business_type_text=business_type_text
                )
                demand_list.append(demand_vo)
            
            return DemandSquareListResponseVO(
                total=total,
                page=query_params.page,
                size=query_params.size,
                pages=pages,
                list=demand_list
            )
            
        except Exception as e:
            logger.error(f"获取需求列表失败: {str(e)}")
            raise e
    
    @staticmethod
    async def get_demand_stats(
        db: AsyncSession,
        business_type: Optional[int] = None
    ) -> DemandSquareStatsVO:
        """
        获取需求统计信息
        """
        try:
            # 基础查询条件
            current_time = datetime.now()
            base_conditions = [
                DemandSquare.is_delete == 0,
                DemandSquare.demand_status == 1,  # 待抢单
                # 根据业务类型判断是否过期
                or_(
                    # 到家订单：创建时间+30分钟内有效
                    and_(
                        DemandSquare.business_type == 1,
                        DemandSquare.create_time + timedelta(minutes=30) > current_time
                    ),
                    # 到店订单：创建时间+24小时内有效
                    and_(
                        DemandSquare.business_type == 2,
                        DemandSquare.create_time + timedelta(hours=24) > current_time
                    ),
                    # 其他类型使用原有expire_time逻辑
                    and_(
                        DemandSquare.business_type.notin_([1, 2]),
                        DemandSquare.expire_time > current_time
                    )
                )
            ]
            
            # 总统计
            conditions = base_conditions.copy()
            if business_type is not None:
                conditions.append(DemandSquare.business_type == business_type)
            
            # 查询总数和总金额
            stats_query = select(
                func.count(DemandSquare.id).label('total_count'),
                func.coalesce(func.sum(DemandSquare.commission_amount), 0).label('total_amount')
            ).where(and_(*conditions))
            
            stats_result = await db.execute(stats_query)
            stats = stats_result.first()
            
            # 分类统计
            home_query = select(func.count(DemandSquare.id)).where(
                and_(
                    *base_conditions,
                    DemandSquare.business_type == 1
                )
            )
            home_result = await db.execute(home_query)
            home_count = home_result.scalar() or 0
            
            store_query = select(func.count(DemandSquare.id)).where(
                and_(
                    *base_conditions,
                    DemandSquare.business_type == 2
                )
            )
            store_result = await db.execute(store_query)
            store_count = store_result.scalar() or 0
            
            return DemandSquareStatsVO(
                total_count=stats.total_count or 0,
                total_amount=Decimal(str(stats.total_amount or 0)),
                home_service_count=home_count,
                store_service_count=store_count
            )
            
        except Exception as e:
            logger.error(f"获取需求统计失败: {str(e)}")
            raise e

    @classmethod
    async def purchase_store_lead(
        cls,
        db: AsyncSession,
        demand_uuid: str,
        current_user
    ) -> dict:
        """
        购买到店线索

        Args:
            db: 数据库会话
            demand_uuid: 需求UUID
            current_user: 当前用户

        Returns:
            购买结果
        """
        try:
            from sqlalchemy import text

            # 1. 获取当前用户信息
            user_id = getattr(current_user.user, 'id', None)
            user_uuid = getattr(current_user.user, 'uuid', None)
            user_name = getattr(current_user.user, 'name', None)
            store_uuid = getattr(current_user.user, 'store_uuid', None)

            if not all([user_id, user_uuid, user_name, store_uuid]):
                raise ValidationException(message="用户信息获取失败")

            # 1.1 获取门店名称
            store_query = text("SELECT name FROM store WHERE store_uuid = :store_uuid")
            store_result = await db.execute(store_query, {"store_uuid": store_uuid})
            store_row = store_result.fetchone()
            store_name = store_row.name if store_row else user_name

            # 2. 获取需求信息（不使用FOR UPDATE，避免提前开启事务）
            demand_query = text("""
                SELECT * FROM demand_square
                WHERE uuid = :demand_uuid AND business_type = 2 AND demand_status = 1 AND is_delete = 0
            """)
            demand_result = await db.execute(demand_query, {"demand_uuid": demand_uuid})
            demand = demand_result.fetchone()

            if not demand:
                raise BusinessException(message="线索不存在或已被购买")

            # 3. 验证不能购买自己门店的线索
            # 使用新的original_seller_store_uuid字段
            seller_store_to_check = demand.original_seller_store_uuid or demand.grab_store_uuid
            if seller_store_to_check == store_uuid:
                raise BusinessException(message="不能购买自己门店的线索")

            # 4. 检查公司余额
            balance_query = text("""
                SELECT c.balance
                FROM company c
                JOIN store s ON c.id = s.company_id
                WHERE s.store_uuid = :store_uuid
            """)
            balance_result = await db.execute(balance_query, {"store_uuid": store_uuid})
            balance_row = balance_result.fetchone()

            current_balance = float(balance_row.balance) if balance_row else 0.0
            purchase_amount = float(demand.commission_amount)

            if current_balance < purchase_amount:
                raise BusinessException(message=f"余额不足，当前余额：{current_balance}元，需要：{purchase_amount}元")

            # 5. 开始事务处理 - 适配autocommit=False的Session
            logger.info(f"准备开始线索购买事务: demand_uuid={demand_uuid}, user={user_name}, amount={purchase_amount}")

            # 初始化变量
            new_balance = current_balance - purchase_amount
            new_customer_uuid = None
            transaction_no = cls._generate_transaction_no()

            # 检查Session事务状态
            is_in_transaction = db.in_transaction()
            logger.info(f"当前Session事务状态: {is_in_transaction}")

            try:
                # 由于autocommit=False，Session已经在事务中，直接执行操作
                if not is_in_transaction:
                    # 如果没有在事务中，手动开始
                    await db.begin()
                    logger.info("手动开始事务")
                else:
                    logger.info("Session已在事务中，直接执行操作")

                # 5.1 使用乐观锁更新需求状态（参考家政人广场抢单逻辑）
                logger.info("使用乐观锁更新需求状态为已售出")
                update_demand_query = text("""
                    UPDATE demand_square
                    SET demand_status = 3,
                        buyer_store_uuid = :buyer_store_uuid,
                        grab_store_uuid = :buyer_store_uuid,
                        grab_store_name = :buyer_store_name,
                        grab_user_uuid = :buyer_user_uuid,
                        grab_user_name = :buyer_user_name,
                        grab_time = NOW(),
                        purchase_time = NOW(),
                        update_time = NOW()
                    WHERE uuid = :demand_uuid
                      AND demand_status = 1
                      AND is_delete = 0
                """)

                update_result = await db.execute(update_demand_query, {
                    "demand_uuid": demand_uuid,
                    "buyer_store_uuid": store_uuid,
                    "buyer_store_name": store_name,
                    "buyer_user_uuid": user_uuid,
                    "buyer_user_name": user_name
                })

                # 检查是否更新成功（并发控制）
                if update_result.rowcount == 0:
                    await db.rollback()
                    raise BusinessException(message="线索购买失败，该线索已被其他门店购买")

                logger.info("需求状态更新成功")

                # 5.2 扣除买方余额
                logger.info(f"准备更新余额: {current_balance} -> {new_balance}")
                await cls._update_company_balance(db, store_uuid, new_balance)
                logger.info("余额更新完成")

                # 5.3 创建扣费流水
                logger.info(f"准备创建交易流水: {transaction_no}")
                await cls._create_purchase_transaction(
                    db, store_uuid, user_name, purchase_amount,
                    current_balance, new_balance, demand_uuid, transaction_no
                )
                logger.info("交易流水创建完成")

                # 5.4 给卖方增加资金
                logger.info("准备给卖方增加资金")
                await cls._add_seller_income(
                    db, demand, purchase_amount, transaction_no
                )
                logger.info("卖方资金增加完成")

                # 5.5 复制创建新线索
                logger.info("准备复制线索")
                new_customer_uuid = await cls._copy_lead_from_demand(
                    db, demand, user_uuid, user_name, store_uuid, transaction_no
                )
                logger.info(f"线索复制完成: {new_customer_uuid}")

                # 提交事务
                await db.commit()
                logger.info(f"线索购买事务提交成功: 用户{user_name}购买线索{demand_uuid}，金额{purchase_amount}元")

            except Exception as transaction_error:
                # 回滚事务
                try:
                    await db.rollback()
                    logger.info("线索购买事务已回滚")
                except Exception as rollback_error:
                    logger.error(f"事务回滚失败: {str(rollback_error)}")

                logger.error(f"线索购买事务发生异常: {str(transaction_error)}")
                raise BusinessException(message=f"线索购买失败: {str(transaction_error)}")

            # 验证购买结果
            if new_customer_uuid is None:
                logger.error("线索购买失败：新客户UUID为空，事务可能被回滚")
                raise BusinessException(message="线索购买失败：数据创建异常")

            # 验证数据库状态
            try:
                # 验证余额是否真的被扣减
                balance_verify_query = text("""
                    SELECT c.balance FROM company c
                    JOIN store s ON c.id = s.company_id
                    WHERE s.store_uuid = :store_uuid
                """)
                balance_verify_result = await db.execute(balance_verify_query, {"store_uuid": store_uuid})
                actual_balance = float(balance_verify_result.scalar() or 0)

                if abs(actual_balance - new_balance) > 0.01:  # 允许小数点误差
                    logger.error(f"余额验证失败：期望{new_balance}，实际{actual_balance}")
                    raise BusinessException(message="线索购买失败：余额更新异常")

                # 验证线索状态是否真的被更新
                status_verify_query = text("""
                    SELECT demand_status FROM demand_square
                    WHERE uuid = :demand_uuid
                """)
                status_verify_result = await db.execute(status_verify_query, {"demand_uuid": demand_uuid})
                actual_status = status_verify_result.scalar()

                if actual_status != 3:
                    logger.error(f"线索状态验证失败：期望3，实际{actual_status}")
                    raise BusinessException(message="线索购买失败：状态更新异常")

                # 验证客户记录是否真的被创建
                customer_verify_query = text("""
                    SELECT COUNT(*) FROM customer WHERE uuid = :customer_uuid
                """)
                customer_verify_result = await db.execute(customer_verify_query, {"customer_uuid": new_customer_uuid})
                customer_count = customer_verify_result.scalar()

                if customer_count == 0:
                    logger.error(f"客户记录验证失败：UUID {new_customer_uuid} 不存在")
                    raise BusinessException(message="线索购买失败：客户记录创建异常")

                logger.info(f"线索购买数据验证通过: 余额{actual_balance}, 状态{actual_status}, 客户记录存在")

            except Exception as verify_error:
                logger.error(f"线索购买数据验证失败: {str(verify_error)}")
                raise BusinessException(message=f"线索购买失败：数据验证异常 - {str(verify_error)}")

            logger.info(f"线索购买成功: 用户{user_name}购买线索{demand_uuid}，金额{purchase_amount}元")

            return {
                "success": True,
                "message": "线索购买成功",
                "new_customer_uuid": new_customer_uuid,
                "purchase_amount": purchase_amount,
                "remaining_balance": new_balance
            }

        except ValidationException as e:
            logger.error(f"线索购买参数验证失败: {e.message}")
            raise e
        except BusinessException as e:
            logger.error(f"线索购买业务异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"线索购买服务异常: {str(e)}")
            raise BusinessException(message=f"线索购买失败: {str(e)}")

    @classmethod
    async def get_user_balance(
        cls,
        db: AsyncSession,
        current_user
    ) -> dict:
        """
        查询用户余额

        Args:
            db: 数据库会话
            current_user: 当前用户

        Returns:
            余额信息
        """
        try:
            from sqlalchemy import text

            # 获取用户门店信息
            store_uuid = getattr(current_user.user, 'store_uuid', None)
            if not store_uuid:
                raise ValidationException(message="用户门店信息获取失败")

            # 查询公司余额
            balance_query = text("""
                SELECT c.balance, c.name as company_name, s.name as store_name
                FROM company c
                LEFT JOIN store s ON c.id = s.company_id
                WHERE s.store_uuid = :store_uuid
            """)
            balance_result = await db.execute(balance_query, {"store_uuid": store_uuid})
            balance_info = balance_result.fetchone()

            if not balance_info:
                raise BusinessException(message="余额信息查询失败")

            current_balance = float(balance_info.balance) if balance_info.balance else 0.0

            return {
                "balance": current_balance,
                "company_name": balance_info.company_name,
                "store_name": balance_info.store_name,
                "store_uuid": store_uuid
            }

        except ValidationException as e:
            logger.error(f"余额查询参数验证失败: {e.message}")
            raise e
        except BusinessException as e:
            logger.error(f"余额查询业务异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"余额查询服务异常: {str(e)}")
            raise BusinessException(message=f"余额查询失败: {str(e)}")

    @classmethod
    async def recharge_balance(
        cls,
        db: AsyncSession,
        amount: float,
        current_user
    ) -> dict:
        """
        余额充值（模拟充值）

        Args:
            db: 数据库会话
            amount: 充值金额
            current_user: 当前用户

        Returns:
            充值结果
        """
        try:
            from sqlalchemy import text

            # 参数验证
            if amount <= 0:
                raise ValidationException(message="充值金额必须大于0")

            if amount > 10000:
                raise ValidationException(message="单次充值金额不能超过10000元")

            # 获取用户门店信息
            store_uuid = getattr(current_user.user, 'store_uuid', None)
            user_name = getattr(current_user.user, 'name', None)

            if not all([store_uuid, user_name]):
                raise ValidationException(message="用户信息获取失败")

            # 获取当前余额
            balance_query = text("""
                SELECT c.balance
                FROM company c
                JOIN store s ON c.id = s.company_id
                WHERE s.store_uuid = :store_uuid
            """)
            balance_result = await db.execute(balance_query, {"store_uuid": store_uuid})
            balance_row = balance_result.fetchone()

            current_balance = float(balance_row.balance) if balance_row else 0.0
            new_balance = current_balance + amount

            # 开始事务处理 - 安全的事务管理
            if db.in_transaction():
                logger.info("Session已在事务中，直接执行充值操作")
                # 更新余额
                await cls._update_company_balance(db, store_uuid, new_balance)

                # 创建充值记录
                transaction_no = cls._generate_transaction_no()
                await cls._create_recharge_transaction(
                    db, store_uuid, user_name, amount,
                    current_balance, new_balance, transaction_no
                )
            else:
                logger.info("Session不在事务中，开启新事务")
                async with db.begin():
                    # 更新余额
                    await cls._update_company_balance(db, store_uuid, new_balance)

                    # 创建充值记录
                    transaction_no = cls._generate_transaction_no()
                    await cls._create_recharge_transaction(
                        db, store_uuid, user_name, amount,
                        current_balance, new_balance, transaction_no
                    )

            logger.info(f"余额充值成功: store_uuid={store_uuid}, amount={amount}")

            return {
                "success": True,
                "message": "充值成功",
                "amount": amount,
                "old_balance": current_balance,
                "new_balance": new_balance
            }

        except ValidationException as e:
            logger.error(f"余额充值参数验证失败: {e.message}")
            raise e
        except BusinessException as e:
            logger.error(f"余额充值业务异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"余额充值服务异常: {str(e)}")
            raise BusinessException(message=f"余额充值失败: {str(e)}")

    @classmethod
    async def _create_recharge_transaction(
        cls, db: AsyncSession, store_uuid: str, user_name: str,
        amount: float, old_balance: float, new_balance: float,
        transaction_no: str
    ):
        """创建充值交易记录"""
        from sqlalchemy import text

        # 使用company_transaction表记录交易（匹配实际表结构）
        insert_sql = text("""
            INSERT INTO company_transaction (
                company_uuid, transaction_no, business_type, business_type_name,
                transaction_type, amount, balance_before, balance_after,
                related_order_no, pay_type, operator_id, operator_name,
                description, remark, transaction_status, transaction_time,
                created_by, created_at
            ) SELECT
                c.id, :transaction_no, 'BALANCE_RECHARGE', '余额充值',
                1, :amount, :balance_before, :balance_after,
                :related_order_no, 'RECHARGE', :operator_id, :operator_name,
                :description, :remark, 'SUCCESS', NOW(),
                :created_by, NOW()
            FROM company c
            JOIN store s ON c.id = s.company_id
            WHERE s.store_uuid = :store_uuid
        """)

        await db.execute(insert_sql, {
            "store_uuid": store_uuid,
            "transaction_no": transaction_no,
            "amount": amount,  # 正数表示收入
            "balance_before": old_balance,
            "balance_after": new_balance,
            "related_order_no": transaction_no,  # 使用交易号作为关联订单号
            "operator_id": store_uuid,  # 使用store_uuid作为操作者ID
            "operator_name": user_name,
            "description": f"余额充值，充值金额：{amount}元",
            "remark": f"充值前余额：{old_balance}元，充值后余额：{new_balance}元",
            "created_by": store_uuid
        })

    @classmethod
    async def _update_company_balance(cls, db: AsyncSession, store_uuid: str, new_balance: float):
        """更新公司余额"""
        from sqlalchemy import text

        update_sql = text("""
            UPDATE company c
            JOIN store s ON c.id = s.company_id
            SET c.balance = :new_balance, c.update_time = NOW()
            WHERE s.store_uuid = :store_uuid
        """)
        await db.execute(update_sql, {"store_uuid": store_uuid, "new_balance": new_balance})

    @classmethod
    def _generate_transaction_no(cls) -> str:
        """生成交易流水号"""
        import time
        import random
        timestamp = str(int(time.time()))
        random_num = str(random.randint(1000, 9999))
        return f"LEAD_{timestamp}_{random_num}"

    @classmethod
    async def _create_purchase_transaction(
        cls, db: AsyncSession, store_uuid: str, user_name: str,
        amount: float, old_balance: float, new_balance: float,
        demand_uuid: str, transaction_no: str
    ):
        """创建购买交易记录"""
        from sqlalchemy import text

        # 使用company_transaction表记录交易（匹配实际表结构）
        insert_sql = text("""
            INSERT INTO company_transaction (
                company_uuid, transaction_no, business_type, business_type_name,
                transaction_type, amount, balance_before, balance_after,
                related_order_no, pay_type, operator_id, operator_name,
                description, remark, transaction_status, transaction_time,
                created_by, created_at
            ) SELECT
                c.id, :transaction_no, 'LEAD_PURCHASE', '线索购买',
                2, :amount, :balance_before, :balance_after,
                :related_order_no, 'BALANCE', c.id, :operator_name,
                :description, :remark, 'SUCCESS', NOW(),
                c.id, NOW()
            FROM company c
            JOIN store s ON c.id = s.company_id
            WHERE s.store_uuid = :store_uuid
        """)

        await db.execute(insert_sql, {
            "store_uuid": store_uuid,
            "transaction_no": transaction_no,
            "amount": amount,  # 正数金额，transaction_type=2表示支出
            "balance_before": old_balance,
            "balance_after": new_balance,
            "related_order_no": demand_uuid,
            "operator_name": user_name,
            "description": f"购买到店线索，线索ID：{demand_uuid}",
            "remark": f"购买金额：{amount}元，购买后余额：{new_balance}元"
        })

    @classmethod
    async def _add_seller_income(
        cls, db: AsyncSession, demand, amount: float, transaction_no: str
    ):
        """给卖方增加资金"""
        try:
            # 使用新的original_seller_store_uuid字段获取卖方门店信息
            seller_store_uuid = demand.original_seller_store_uuid or demand.grab_store_uuid

            if not seller_store_uuid:
                logger.warning("无法确定卖方门店，跳过资金增加")
                return

            logger.info(f"卖方门店: {seller_store_uuid}")

            # 查询卖方公司余额
            balance_query = text("""
                SELECT c.id as company_id, c.balance
                FROM company c
                JOIN store s ON c.id = s.company_id
                WHERE s.store_uuid = :store_uuid
            """)
            balance_result = await db.execute(balance_query, {"store_uuid": seller_store_uuid})
            balance_row = balance_result.fetchone()

            if not balance_row:
                logger.warning(f"未找到卖方门店对应的公司信息: {seller_store_uuid}")
                return

            current_balance = float(balance_row.balance)
            new_balance = current_balance + amount

            # 更新卖方余额
            await cls._update_company_balance(db, seller_store_uuid, new_balance)

            # 创建收入流水
            income_transaction_no = f"INCOME_{transaction_no}"
            insert_sql = text("""
                INSERT INTO company_transaction (
                    company_uuid, transaction_no, business_type, business_type_name,
                    transaction_type, amount, balance_before, balance_after,
                    related_order_no, pay_type, operator_id, operator_name,
                    description, remark, transaction_status, transaction_time,
                    created_by, created_at
                ) SELECT
                    c.id, :transaction_no, 'LEAD_SALE', '线索销售',
                    1, :amount, :balance_before, :balance_after,
                    :related_order_no, 'BALANCE', c.id, :operator_name,
                    :description, :remark, 'SUCCESS', NOW(),
                    c.id, NOW()
                FROM company c
                JOIN store s ON c.id = s.company_id
                WHERE s.store_uuid = :store_uuid
            """)

            await db.execute(insert_sql, {
                "store_uuid": seller_store_uuid,
                "transaction_no": income_transaction_no,
                "amount": amount,  # 正数表示收入
                "balance_before": current_balance,
                "balance_after": new_balance,
                "related_order_no": demand.uuid,
                "operator_name": demand.grab_user_name or "系统",
                "description": f"线索销售收入，线索ID：{demand.uuid}",
                "remark": f"销售金额：{amount}元，销售后余额：{new_balance}元"
            })

            logger.info(f"卖方资金增加成功: 门店={seller_store_uuid}, 金额={amount}元, 新余额={new_balance}元")

        except Exception as e:
            logger.error(f"给卖方增加资金失败: {str(e)}")
            raise BusinessException(message=f"给卖方增加资金失败: {str(e)}")

    @classmethod
    async def _copy_lead_from_demand(
        cls, db: AsyncSession, demand, buyer_user_uuid: str,
        buyer_user_name: str, buyer_store_uuid: str, transaction_no: str
    ) -> str:
        """从需求复制创建新线索"""
        from sqlalchemy import text
        import uuid
        from datetime import datetime

        # 生成新的线索UUID
        new_customer_uuid = str(uuid.uuid4()).replace('-', '')
        current_time = datetime.now()

        # 处理服务项目映射（支持多个项目）
        aunt_type_code = demand.service_project
        aunt_names = []

        if ',' in aunt_type_code:
            # 多个服务项目
            type_codes = aunt_type_code.split(',')
            for code in type_codes:
                code = code.strip()
                aunt_name_query = text("""
                    SELECT dict_label FROM sys_dict_data
                    WHERE dict_type = 'aunt_type' AND dict_value = :aunt_type_code
                """)
                aunt_name_result = await db.execute(aunt_name_query, {"aunt_type_code": code})
                aunt_name_row = aunt_name_result.fetchone()
                if aunt_name_row:
                    aunt_names.append(aunt_name_row[0])
                else:
                    # 如果没找到，使用默认映射
                    default_mapping = {
                        "1001": "保姆",
                        "1002": "月嫂",
                        "1003": "小儿推拿师",
                        "1004": "育婴师",
                        "1005": "保安",
                        "1006": "中医师"
                    }
                    aunt_names.append(default_mapping.get(code, code))
        else:
            # 单个服务项目
            aunt_name_query = text("""
                SELECT dict_label FROM sys_dict_data
                WHERE dict_type = 'aunt_type' AND dict_value = :aunt_type_code
            """)
            aunt_name_result = await db.execute(aunt_name_query, {"aunt_type_code": aunt_type_code})
            aunt_name_row = aunt_name_result.fetchone()
            if aunt_name_row:
                aunt_names.append(aunt_name_row[0])
            else:
                # 使用默认映射
                default_mapping = {
                    "1001": "保姆",
                    "1002": "月嫂",
                    "1003": "小儿推拿师",
                    "1004": "育婴师",
                    "1005": "保安",
                    "1006": "中医师"
                }
                aunt_names.append(default_mapping.get(aunt_type_code, aunt_type_code))

        aunt_name = ','.join(aunt_names)
        logger.info(f"服务项目映射: {aunt_type_code} -> {aunt_name}")

        # 解析地址获取城市信息和坐标
        service_address = demand.service_address or ""
        city_name = "未知城市"
        city_id = None
        lng = None
        lat = None

        # 城市提取逻辑
        city_mapping = {
            "厦门": {"name": "厦门", "id": "153"},
            "福州": {"name": "福州", "id": "151"},
            "泉州": {"name": "泉州", "id": "154"},
            "漳州": {"name": "漳州", "id": "155"},
            "龙岩": {"name": "龙岩", "id": "156"},
            "三明": {"name": "三明", "id": "157"},
            "莆田": {"name": "莆田", "id": "158"},
            "南平": {"name": "南平", "id": "159"},
            "宁德": {"name": "宁德", "id": "160"}
        }

        for city, info in city_mapping.items():
            if city in service_address:
                city_name = info["name"]
                city_id = info["id"]
                break

        # 如果是厦门，使用已知的坐标
        if city_name == "厦门" and "民族路33号" in service_address:
            lng = "118.08243"
            lat = "24.44579"
        elif city_name == "厦门":
            # 厦门的默认坐标
            lng = "118.1"
            lat = "24.5"

        logger.info(f"地址解析: {service_address} -> 城市={city_name}, 坐标=({lng}, {lat})")

        # 从demand的remark中提取原始线索ID
        source_lead_id = None
        if demand.remark and "原线索ID：" in demand.remark:
            source_lead_id = demand.remark.split("原线索ID：")[1].strip()
            logger.info(f"提取到原始线索ID: {source_lead_id}")

        # 复制线索到customer表
        insert_sql = text("""
            INSERT INTO customer (
                uuid, name, mobile, aunt_type, aunt_name, address, city, city_id, lng, lat,
                user_uuid, user_name, store_uuid, status, status_name,
                source, remark, source_lead_id, lead_source_type, purchase_transaction_no,
                create_time, update_time, is_delete
            ) VALUES (
                :uuid, :name, :mobile, :aunt_type, :aunt_name, :address, :city, :city_id, :lng, :lat,
                :user_uuid, :user_name, :store_uuid, '1', '待跟进',
                '6', :remark, :source_lead_id, 'purchased', :purchase_transaction_no,
                :create_time, :update_time, '0'
            )
        """)

        await db.execute(insert_sql, {
            "uuid": new_customer_uuid,
            "name": demand.customer_name,
            "mobile": demand.customer_phone,
            "aunt_type": aunt_type_code,
            "aunt_name": aunt_name,  # 使用正确的中文名称
            "address": service_address,
            "city": city_name,  # 解析后的城市名称
            "city_id": city_id,  # 城市ID
            "lng": lng,  # 经度
            "lat": lat,  # 纬度
            "user_uuid": buyer_user_uuid,
            "user_name": buyer_user_name,
            "store_uuid": buyer_store_uuid,
            "remark": f"广场购买线索生成，原需求ID：{demand.uuid}",
            "source_lead_id": source_lead_id,  # 原始线索ID
            "purchase_transaction_no": transaction_no,  # 购买交易号
            "create_time": current_time,
            "update_time": current_time
        })

        logger.info(f"线索复制完成: UUID={new_customer_uuid}, 服务项目={aunt_name}, 来源=广场抢单")
        return new_customer_uuid



    @staticmethod
    async def grab_demand(
        db: AsyncSession,
        grab_data: DemandGrabRequestVO,
        current_user
    ) -> DemandGrabResponseVO:
        """
        抢单处理逻辑
        """
        try:
            # 1. 参数验证
            if not grab_data.demand_uuid:
                raise ValidationException(message="需求UUID不能为空")

            if not grab_data.store_uuid:
                raise ValidationException(message="门店UUID不能为空")

            # 2. 获取当前用户信息
            user_id = getattr(current_user.user, 'id', None)
            user_uuid = getattr(current_user.user, 'uuid', None)
            user_name = getattr(current_user.user, 'name', None) or getattr(current_user.user, 'nick_name', '未知用户')

            if not user_id or not user_uuid:
                raise ValidationException(message="用户信息获取失败")

            logger.info(f"用户 {user_name}({user_id}) 尝试抢单: {grab_data.demand_uuid}")

            # 3. 验证需求和门店信息（优化：合并查询）
            validation_result = await DemandSquareService._validate_grab_prerequisites(
                db, grab_data.demand_uuid, grab_data.store_uuid
            )

            if not validation_result["success"]:
                return DemandGrabResponseVO(
                    success=False,
                    message=validation_result["message"],
                    demand_uuid=grab_data.demand_uuid
                )

            demand = validation_result["demand"]
            store_info = validation_result["store_info"]

            # 7. 开始事务处理抢单
            try:
                # 在事务开始前获取所有需要的demand属性值（优化：避免事务中访问ORM属性）
                demand_business_type = demand.business_type
                demand_uuid_str = grab_data.demand_uuid
                demand_commission_amount = float(demand.commission_amount)
                demand_service_address = demand.service_address
                demand_service_time = demand.service_time
                demand_customer_name = demand.customer_name
                demand_customer_phone = demand.customer_phone
                demand_platform_order_number = demand.platform_order_number
                demand_remark = demand.remark
                demand_lng = demand.lng
                demand_lat = demand.lat
                demand_customer_address = demand.customer_address
                demand_service_requirements = demand.service_requirements

                # 7.1 原子性更新需求状态（使用乐观锁）
                grab_time = datetime.now()
                update_demand_query = update(DemandSquare).where(
                    and_(
                        DemandSquare.uuid == demand_uuid_str,
                        DemandSquare.demand_status == 1  # 确保状态仍为待抢单
                    )
                ).values(
                    demand_status=2,
                    grab_store_uuid=grab_data.store_uuid,
                    grab_store_name=store_info._mapping.get("name"),
                    grab_user_uuid=user_uuid,  # 使用用户的UUID而不是ID
                    grab_user_name=user_name,
                    grab_time=grab_time,
                    update_time=grab_time
                )

                update_result = await db.execute(update_demand_query)

                # 检查是否更新成功（并发控制）
                if update_result.rowcount == 0:
                    return DemandGrabResponseVO(
                        success=False,
                        message="抢单失败，该需求已被其他门店抢走",
                        demand_uuid=demand_uuid_str
                    )

                # 7.2 根据业务类型和是否为共享订单创建不同的记录
                if demand_business_type == 1:  # 到家订单
                    # 检查是否为共享订单
                    if demand.source_order_id:
                        # 共享订单：不创建新订单，只更新需求状态
                        logger.info(f"共享订单抢单成功，不创建新订单: source_order_id={demand.source_order_id}")
                        created_record_id = f"shared_order_{demand.source_order_id}"
                    else:
                        # 普通订单：创建新订单
                        # 生成订单号
                        timestamp = int(time.time())
                        random_num = random.randint(100000, 999999)
                        order_number = f"DS{timestamp}{random_num}"

                        # 创建订单记录
                        await DemandSquareService._create_order_from_demand(
                            db, demand, store_info, order_number, user_id, {
                                "commission_amount": demand_commission_amount,
                                "service_address": demand_service_address,
                                "service_time": demand_service_time,
                                "customer_name": demand_customer_name,
                                "customer_phone": demand_customer_phone,
                                "platform_order_number": demand_platform_order_number,
                                "remark": demand_remark,
                                "lng": demand_lng,
                                "lat": demand_lat,
                                "customer_address": demand_customer_address,
                                "service_requirements": demand_service_requirements
                            }
                        )

                        # 更新需求记录的订单号
                        update_order_number_query = update(DemandSquare).where(
                            DemandSquare.uuid == demand_uuid_str
                        ).values(
                            created_order_number=order_number,
                            update_time=grab_time
                        )
                        await db.execute(update_order_number_query)

                        # 记录操作日志
                        await DemandSquareService._create_order_log(
                            db, order_number, user_id, user_name, "抢单成功"
                        )

                        created_record_id = order_number

                elif demand_business_type == 2:  # 到店订单
                    # 创建customer线索记录
                    customer_uuid = await DemandSquareService._create_customer_lead_from_demand(
                        db, demand, store_info, user_id, user_uuid, user_name
                    )

                    # 更新需求记录的线索UUID
                    update_customer_uuid_query = update(DemandSquare).where(
                        DemandSquare.uuid == demand_uuid_str
                    ).values(
                        created_order_number=customer_uuid,  # 复用这个字段存储线索UUID
                        update_time=grab_time
                    )
                    await db.execute(update_customer_uuid_query)

                    created_record_id = customer_uuid

                else:
                    raise BusinessException(message=f"不支持的业务类型: {demand_business_type}")

                # 🔥 在事务提交前获取所有需要的数据，避免异步错误
                is_shared_order = demand.source_order_id is not None

                # 提交事务
                await db.commit()

                logger.info(f"抢单成功: 用户{user_name}抢到需求{demand_uuid_str}，创建记录{created_record_id}")

                # 🔥 使用预获取的数据，避免访问已失效的ORM对象
                if demand_business_type == 1:
                    if is_shared_order:
                        success_message = "抢单成功，请选择服务人员"
                    else:
                        success_message = "抢单成功，订单已创建"
                else:
                    success_message = "抢单成功，线索已创建"

                return DemandGrabResponseVO(
                    success=True,
                    message=success_message,
                    order_number=created_record_id,
                    demand_uuid=demand_uuid_str,
                    grab_time=grab_time,
                    need_staff_selection=is_shared_order and demand_business_type == 1
                )

            except Exception as e:
                # 回滚事务
                await db.rollback()
                logger.error(f"抢单事务处理失败: {str(e)}")
                raise e

        except ValidationException as e:
            logger.error(f"抢单参数验证失败: {e.message}")
            raise e
        except BusinessException as e:
            logger.error(f"抢单业务异常: {e.message}")
            raise e
        except Exception as e:
            logger.error(f"抢单服务异常: {str(e)}")
            raise BusinessException(message=f"抢单失败: {str(e)}")

    @staticmethod
    async def _validate_grab_prerequisites(
        db: AsyncSession,
        demand_uuid: str,
        store_uuid: str
    ) -> dict:
        """
        验证抢单前置条件（优化：合并多个验证查询）
        """
        try:
            # 合并查询需求和门店信息，优先使用 original_seller_store_uuid 字段
            validation_query = text("""
                SELECT
                    'demand' as type, d.uuid, d.demand_status, d.business_type,
                    d.create_time, d.expire_time, d.source_order_id,
                    NULL as name, NULL as store_uuid, NULL as id,
                    COALESCE(d.original_seller_store_uuid, o.store_uuid) as original_store_uuid
                FROM demand_square d
                LEFT JOIN `order` o ON d.source_order_id = o.id
                WHERE d.uuid = :demand_uuid AND d.is_delete = 0
                UNION ALL
                SELECT
                    'store' as type, NULL as uuid, NULL as demand_status, NULL as business_type,
                    NULL as create_time, NULL as expire_time, NULL as source_order_id,
                    s.name, s.store_uuid, s.id, NULL as original_store_uuid
                FROM store s
                WHERE s.store_uuid = :store_uuid AND s.is_delete = 0 AND s.status = 1
            """)

            validation_result = await db.execute(validation_query, {
                "demand_uuid": demand_uuid,
                "store_uuid": store_uuid
            })
            validation_data = validation_result.fetchall()

            demand_info = None
            store_info = None
            original_store_uuid = None

            for row in validation_data:
                if row.type == 'demand':
                    demand_info = row
                    # 🔥 修复：直接从需求记录中获取原始门店UUID
                    if row.original_store_uuid:
                        original_store_uuid = row.original_store_uuid
                elif row.type == 'store':
                    store_info = row

            # 验证需求是否存在
            if not demand_info:
                return {
                    "success": False,
                    "message": "需求不存在或已删除"
                }

            # 验证需求状态
            if demand_info.demand_status != 1:
                status_text = "已抢单" if demand_info.demand_status == 2 else "已过期"
                return {
                    "success": False,
                    "message": f"需求状态不允许抢单，当前状态: {status_text}"
                }

            # 🔥 验证是否为自抢单（共享订单和线索）
            if original_store_uuid:
                logger.info(f"检查自抢单: 需求UUID={demand_uuid}, 原始门店={original_store_uuid}, 抢单门店={store_uuid}")
                if original_store_uuid == store_uuid:
                    logger.warning(f"阻止自抢单: 门店{store_uuid}尝试抢取自己发布的需求，需求UUID={demand_uuid}")
                    return {
                        "success": False,
                        "message": "不能抢取自己门店发布的需求"
                    }
                else:
                    logger.info(f"允许抢单: 门店{store_uuid}抢取其他门店{original_store_uuid}发布的需求")

            # 验证是否过期 - 根据订单来源使用不同的过期时间逻辑
            current_time = datetime.now()

            if demand_info.source_order_id:
                # 门店共享的订单：使用用户设置的自定义过期时间
                expire_time = demand_info.expire_time
                logger.info(f"门店共享订单过期时间检查: demand_uuid={demand_uuid}, expire_time={expire_time}")
            else:
                # 云平台同步的到家订单：使用固定30分钟过期时间
                if demand_info.business_type == 1:  # 到家订单
                    expire_time = demand_info.create_time + timedelta(minutes=30)
                    logger.info(f"云平台到家订单过期时间检查: demand_uuid={demand_uuid}, create_time={demand_info.create_time}, expire_time={expire_time}")
                elif demand_info.business_type == 2:  # 到店订单
                    expire_time = demand_info.create_time + timedelta(hours=24)
                    logger.info(f"云平台到店订单过期时间检查: demand_uuid={demand_uuid}, create_time={demand_info.create_time}, expire_time={expire_time}")
                else:
                    # 其他类型使用数据库存储的过期时间
                    expire_time = demand_info.expire_time
                    logger.info(f"其他类型订单过期时间检查: demand_uuid={demand_uuid}, expire_time={expire_time}")

            if current_time > expire_time:
                return {
                    "success": False,
                    "message": "需求已过期，无法抢单"
                }

            # 验证门店是否存在
            if not store_info:
                return {
                    "success": False,
                    "message": "门店不存在或已停用"
                }

            # 获取完整的需求对象
            demand_query = select(DemandSquare).where(DemandSquare.uuid == demand_uuid)
            demand_result = await db.execute(demand_query)
            demand = demand_result.scalar_one()

            return {
                "success": True,
                "demand": demand,
                "store_info": store_info
            }

        except Exception as e:
            logger.error(f"验证抢单前置条件失败: {str(e)}")
            return {
                "success": False,
                "message": f"验证失败: {str(e)}"
            }

    @staticmethod
    async def _create_order_from_demand(
        db: AsyncSession,
        demand: DemandSquare,
        store_info,
        order_number: str,
        user_id: int,
        demand_data: Optional[dict] = None
    ):
        """
        从需求创建订单记录（优化：使用预获取的数据避免ORM属性访问）
        """
        try:
            # 使用传入的数据或从demand对象获取（向后兼容）
            if demand_data:
                commission_amount = demand_data["commission_amount"]
                service_address = demand_data["service_address"]
                service_time = demand_data["service_time"]
                customer_name = demand_data["customer_name"]
                customer_phone = demand_data["customer_phone"]
                platform_order_number = demand_data["platform_order_number"]
                remark = demand_data["remark"]
            else:
                commission_amount = float(demand.commission_amount)
                service_address = demand.service_address
                service_time = demand.service_time
                customer_name = demand.customer_name
                customer_phone = demand.customer_phone
                platform_order_number = demand.platform_order_number
                remark = demand.remark

            # 1. 批量查询产品、SKU和客户信息（优化：减少数据库查询次数）
            batch_query = text("""
                SELECT
                    'product' as type, p.id, p.product_name as name, NULL as mobile, NULL as uuid, NULL as sku_id
                FROM product p
                WHERE p.id = :product_id AND p.is_delete = 0
                UNION ALL
                SELECT
                    'product_sku' as type, ps.productid as id, ps.name, NULL as mobile, NULL as uuid, ps.id as sku_id
                FROM product_sku ps
                WHERE ps.productid = :product_id
                UNION ALL
                SELECT
                    'customer' as type, c.id, c.name, c.mobile, c.uuid, NULL as sku_id
                FROM ccuser c
                WHERE c.name = :customer_name AND c.mobile = :customer_phone AND c.is_delete != '1'
                LIMIT 3
            """)
            batch_result = await db.execute(batch_query, {
                "product_id": demand.jingang_product_id,
                "customer_name": customer_name,
                "customer_phone": customer_phone
            })
            batch_data = batch_result.fetchall()

            # 解析查询结果
            product_info = None
            product_sku_info = None
            customer_info = None
            for row in batch_data:
                if row.type == 'product':
                    product_info = row
                elif row.type == 'product_sku':
                    product_sku_info = row
                elif row.type == 'customer':
                    customer_info = row

            # 使用产品名称，如果没有找到产品则使用需求的服务项目
            product_name = product_info.name if product_info else demand.service_project
            product_id = product_info.id if product_info else None
            product_sku_id = product_sku_info.sku_id if product_sku_info else None

            # 2. 处理客户信息
            if customer_info:
                customer_id = customer_info.id
            else:
                # 创建新客户
                customer_id, _ = await DemandSquareService._create_customer_from_demand(
                    db, demand, store_info, user_id
                )

            # 3. 创建客户地址记录（参考代客下单逻辑）
            address_id = None
            if demand_data and demand_data.get("lng") and demand_data.get("lat"):
                address_id = await DemandSquareService._create_customer_address_from_demand(
                    db, customer_id, demand_data
                )

            # 创建订单SQL
            create_order_sql = text("""
                INSERT INTO `order` (
                    order_number, user_id, store_id, store_uuid, store_name,
                    product_id, product_name, product_type, product_type_name, product_sku_id,
                    service_address, service_date, service_type, service_hour,
                    order_status, order_status_name, buy_num, pay_actual,
                    total_pay_actual, source, create_time,
                    remark, service_phone, address_id
                ) VALUES (
                    :order_number, :user_id, :store_id, :store_uuid, :store_name,
                    :product_id, :product_name, :product_type, :product_type_name, :product_sku_id,
                    :service_address, :service_date, 1, :service_hour,
                    10, '已接单', 1, :pay_actual,
                    :total_pay_actual, 'grab', NOW(),
                    :remark, :service_phone, :address_id
                )
            """)

            # 4. 计算service_hour（从服务时间提取小时部分）
            service_hour = "08:00:00"  # 默认值
            if service_time is not None:
                try:
                    if isinstance(service_time, str):
                        from datetime import datetime
                        service_datetime = datetime.fromisoformat(service_time.replace('Z', '+00:00'))
                    else:
                        service_datetime = service_time
                    service_hour = service_datetime.strftime("%H:%M:%S")
                except Exception as e:
                    logger.warning(f"解析服务时间失败，使用默认值: {str(e)}")

            # 5. 执行插入，使用预获取的字段值
            await db.execute(create_order_sql, {
                "order_number": order_number,
                "user_id": customer_id,
                "store_id": store_info._mapping.get("id"),
                "store_uuid": store_info._mapping.get("store_uuid"),
                "store_name": store_info._mapping.get("name"),
                "product_id": product_id,
                "product_name": product_name,
                "product_type": 1,  # 固定为1
                "product_type_name": "服务",  # 固定为"服务"
                "product_sku_id": product_sku_id,
                "service_address": service_address,
                "service_date": service_time,
                "service_hour": service_hour,
                "pay_actual": commission_amount,
                "total_pay_actual": commission_amount,
                "remark": remark or f"抢单生成订单，平台订单号：{platform_order_number}",
                "service_phone": customer_phone,
                "address_id": address_id
            })

            # 4. 创建支付记录
            await DemandSquareService._create_payment_record_from_demand(
                db, order_number, commission_amount
            )

            logger.info(f"订单创建成功: {order_number}")

        except Exception as e:
            logger.error(f"创建订单失败: {str(e)}")
            raise BusinessException(message=f"创建订单失败: {str(e)}")

    @staticmethod
    async def _create_order_log(
        db: AsyncSession,
        order_number: str,
        operator_id: int,
        operator_name: str,
        operation_desc: str
    ):
        """
        创建订单操作日志
        """
        try:
            log_sql = text("""
                INSERT INTO order_logs (
                    order_number, operation_type, operation_desc,
                    operator_id, operator_name, operation_time, create_time
                ) VALUES (
                    :order_number, 1, :operation_desc,
                    :operator_id, :operator_name, NOW(), NOW()
                )
            """)

            await db.execute(log_sql, {
                "order_number": order_number,
                "operation_desc": operation_desc,
                "operator_id": operator_id,
                "operator_name": operator_name
            })

            logger.info(f"订单日志记录成功: {order_number} - {operation_desc}")

        except Exception as e:
            logger.error(f"记录订单日志失败: {str(e)}")
            # 日志记录失败不影响主流程，只记录错误

    @staticmethod
    async def _create_customer_from_demand(
        db: AsyncSession,
        demand: DemandSquare,
        store_info,
        user_id: int
    ) -> tuple[int, str]:
        """
        从需求创建客户记录（参考代客下单逻辑）
        """
        try:
            import uuid

            # 生成客户UUID
            customer_uuid = str(uuid.uuid4())

            # 插入新客户记录（参考代客下单的方式）
            insert_customer_sql = text("""
                INSERT INTO ccuser (
                    uuid, name, mobile, store_uuid, created_by, updated_by,
                    created_at, updated_at, is_delete, status, source
                ) VALUES (
                    :uuid, :name, :mobile, :store_uuid, 'grab_order', 'grab_order',
                    NOW(), NOW(), '0', '1', 'grab'
                )
            """)

            await db.execute(insert_customer_sql, {
                "uuid": customer_uuid,
                "name": demand.customer_name,
                "mobile": demand.customer_phone,
                "store_uuid": store_info._mapping.get("store_uuid")
            })

            # 查询新创建的客户信息（参考代客下单的方式）
            customer_query = text("""
                SELECT id, uuid, name, mobile, store_uuid
                FROM ccuser
                WHERE uuid = :uuid
            """)
            customer_result = await db.execute(customer_query, {"uuid": customer_uuid})
            customer_row = customer_result.fetchone()

            if not customer_row:
                raise BusinessException(message="创建客户后查询失败")

            customer_id = customer_row._mapping.get("id")
            if not customer_id:
                raise BusinessException(message="获取客户ID失败")

            logger.info(f"客户创建成功: {customer_uuid}, ID: {customer_id}")
            return int(customer_id), customer_uuid

        except Exception as e:
            logger.error(f"创建客户失败: {str(e)}")
            raise BusinessException(message=f"创建客户失败: {str(e)}")

    @staticmethod
    async def _create_customer_address_from_demand(
        db: AsyncSession,
        customer_id: int,
        demand_data: dict
    ) -> int:
        """
        从需求创建客户地址记录（参考代客下单逻辑）
        """
        try:
            # 插入地址记录到ccuser_extend表
            insert_address_sql = text("""
                INSERT INTO ccuser_extend (
                    ccuser_id, name, address, address_desc, lng, lat,
                    contact_phone, isDefault, created_by, updated_by,
                    created_at, updated_at
                ) VALUES (
                    :ccuser_id, :name, :address, :address_desc, :lng, :lat,
                    :contact_phone, 1, 'grab_order', 'grab_order',
                    NOW(), NOW()
                )
            """)

            await db.execute(insert_address_sql, {
                "ccuser_id": str(customer_id),
                "name": demand_data.get("customer_name", ""),
                "address": demand_data.get("service_address", ""),
                "address_desc": demand_data.get("service_requirements", ""),
                "lng": demand_data.get("lng", ""),
                "lat": demand_data.get("lat", ""),
                "contact_phone": demand_data.get("customer_phone", "")
            })

            # 获取新插入的地址ID
            address_id_query = text("""
                SELECT id FROM ccuser_extend
                WHERE ccuser_id = :ccuser_id AND contact_phone = :contact_phone
                ORDER BY id DESC LIMIT 1
            """)
            address_id_result = await db.execute(address_id_query, {
                "ccuser_id": str(customer_id),
                "contact_phone": demand_data.get("customer_phone", "")
            })
            address_id_row = address_id_result.fetchone()
            address_id = address_id_row._mapping.get("id") if address_id_row else None

            if not address_id:
                raise BusinessException(message="获取地址ID失败")

            logger.info(f"客户地址创建成功: customer_id={customer_id}, address_id={address_id}")
            return int(address_id)

        except Exception as e:
            logger.error(f"创建客户地址失败: {str(e)}")
            raise BusinessException(message=f"创建客户地址失败: {str(e)}")

    @staticmethod
    async def _create_payment_record_from_demand(
        db: AsyncSession,
        order_number: str,
        commission_amount: float
    ):
        """
        创建支付记录
        """
        try:
            current_time = datetime.now()

            # 创建支付记录
            create_payment_sql = text("""
                INSERT INTO order_payments (
                    order_number, pay_money, pay_actual, pay_type, pay_type_name,
                    pay_status, create_time, update_time
                ) VALUES (
                    :order_number, :pay_money, :pay_actual, :pay_type, :pay_type_name,
                    1, :create_time, :update_time
                )
            """)

            # 执行插入
            await db.execute(create_payment_sql, {
                "order_number": order_number,
                "pay_money": commission_amount,
                "pay_actual": commission_amount,
                "pay_type": "106",  # 现金支付
                "pay_type_name": "现金支付",
                "create_time": current_time,
                "update_time": current_time
            })

            logger.info(f"支付记录创建成功: {order_number}")

        except Exception as e:
            logger.error(f"创建支付记录失败: {str(e)}")
            raise BusinessException(message=f"创建支付记录失败: {str(e)}")

    @staticmethod
    async def _create_customer_lead_from_demand(
        db: AsyncSession,
        demand: DemandSquare,
        store_info,
        user_id: int,
        user_uuid: str,
        user_name: str
    ) -> str:
        """
        从需求创建customer线索记录（到店订单）
        """
        try:
            import uuid

            # 生成线索UUID
            customer_uuid = str(uuid.uuid4()).replace('-', '')
            current_time = datetime.now()

            # 解析服务地址获取城市信息
            service_address = demand.service_address or ""
            city_name = "未知城市"

            # 简单的城市提取逻辑（可以根据实际需求优化）
            for city in ["厦门", "福州", "泉州", "漳州", "龙岩", "三明", "莆田", "南平", "宁德"]:
                if city in service_address:
                    city_name = city
                    break

            # 查询城市ID
            city_query = text("""
                SELECT id FROM city_information
                WHERE name = :city_name OR name = :city_name_with_suffix
                LIMIT 1
            """)
            city_result = await db.execute(city_query, {
                "city_name": city_name,
                "city_name_with_suffix": f"{city_name}市"
            })
            city_info = city_result.fetchone()
            city_id = city_info.id if city_info else None

            # 🔥 修复：处理服务项目映射，将代码转换为中文名称
            aunt_type_code = demand.service_project
            aunt_names = []

            if ',' in aunt_type_code:
                # 多个服务项目
                type_codes = aunt_type_code.split(',')
                for code in type_codes:
                    code = code.strip()
                    aunt_name_query = text("""
                        SELECT dict_label FROM sys_dict_data
                        WHERE dict_type = 'aunt_type' AND dict_value = :aunt_type_code
                    """)
                    aunt_name_result = await db.execute(aunt_name_query, {"aunt_type_code": code})
                    aunt_name_row = aunt_name_result.fetchone()
                    if aunt_name_row:
                        aunt_names.append(aunt_name_row[0])
                    else:
                        # 如果没找到，使用默认映射
                        default_mapping = {
                            "1001": "保姆",
                            "1002": "月嫂",
                            "1003": "小儿推拿师",
                            "1004": "育婴师",
                            "1005": "保安",
                            "1006": "中医师"
                        }
                        aunt_names.append(default_mapping.get(code, code))
            else:
                # 单个服务项目
                aunt_name_query = text("""
                    SELECT dict_label FROM sys_dict_data
                    WHERE dict_type = 'aunt_type' AND dict_value = :aunt_type_code
                """)
                aunt_name_result = await db.execute(aunt_name_query, {"aunt_type_code": aunt_type_code})
                aunt_name_row = aunt_name_result.fetchone()
                if aunt_name_row:
                    aunt_names.append(aunt_name_row[0])
                else:
                    # 如果没找到，使用默认映射
                    default_mapping = {
                        "1001": "保姆",
                        "1002": "月嫂",
                        "1003": "小儿推拿师",
                        "1004": "育婴师",
                        "1005": "保安",
                        "1006": "中医师"
                    }
                    aunt_names.append(default_mapping.get(aunt_type_code, aunt_type_code))

            aunt_name = ','.join(aunt_names)
            logger.info(f"抢单服务项目映射: {aunt_type_code} -> {aunt_name}")

            # 创建customer线索记录
            create_customer_sql = text("""
                INSERT INTO customer (
                    uuid, name, mobile, address, city, city_id,
                    aunt_type, aunt_name, source, status, status_name,
                    user_uuid, user_name, store_uuid, remark,
                    create_time, update_time, is_delete, common_status,
                    demand_id
                ) VALUES (
                    :uuid, :name, :mobile, :address, :city, :city_id,
                    :aunt_type, :aunt_name, '9', '1', '待跟进',
                    :user_uuid, :user_name, :store_uuid, :remark,
                    :create_time, :update_time, '0', '0',
                    :demand_id
                )
            """)

            # 执行插入
            await db.execute(create_customer_sql, {
                "uuid": customer_uuid,
                "name": demand.customer_name,
                "mobile": demand.customer_phone,
                "address": service_address,
                "city": city_name,
                "city_id": city_id,
                "aunt_type": aunt_type_code,  # 服务项目代码作为阿姨类型
                "aunt_name": aunt_name,  # 🔥 修复：使用转换后的中文名称
                "user_uuid": user_uuid,  # 使用用户的UUID
                "user_name": user_name,
                "store_uuid": store_info._mapping.get("store_uuid"),
                "remark": f"抢单生成线索，平台订单号：{demand.platform_order_number}",
                "create_time": current_time,
                "update_time": current_time,
                "demand_id": demand.uuid
            })

            logger.info(f"线索创建成功: {customer_uuid}")
            return customer_uuid

        except Exception as e:
            logger.error(f"创建线索失败: {str(e)}")
            raise BusinessException(message=f"创建线索失败: {str(e)}")

    @classmethod
    async def _convert_aunt_type_to_name(cls, db: AsyncSession, aunt_type: str) -> str:
        """
        将aunt_type代码转换为中文名称

        Args:
            db: 数据库会话
            aunt_type: aunt_type代码（如：1001,1002 或 1001）

        Returns:
            中文服务名称（如：保姆,产后修复师 或 保姆）
        """
        try:
            if not aunt_type:
                return "家政服务"

            aunt_names = []

            if ',' in aunt_type:
                # 多个服务项目
                type_codes = aunt_type.split(',')
                for code in type_codes:
                    code = code.strip()
                    aunt_name_query = text("""
                        SELECT dict_label FROM sys_dict_data
                        WHERE dict_type = 'aunt_type' AND dict_value = :aunt_type_code AND status = '0'
                    """)
                    aunt_name_result = await db.execute(aunt_name_query, {"aunt_type_code": code})
                    aunt_name_row = aunt_name_result.fetchone()
                    if aunt_name_row:
                        aunt_names.append(aunt_name_row[0])
                    else:
                        # 如果没找到，使用默认映射
                        default_mapping = {
                            "1001": "保姆", "1002": "产后修复师", "1003": "小儿推拿师",
                            "1004": "催乳师", "1005": "保安", "1006": "中医师",
                            "1007": "家教", "1008": "心理理疗师", "1009": "指导师",
                            "1010": "家政员", "1011": "管家", "1012": "厨师",
                            "1013": "月嫂", "1014": "育儿嫂", "1015": "老年护理",
                            "1016": "小时工", "1017": "白班阿姨", "1018": "别墅家务",
                            "1019": "病人护理", "1020": "育婴师", "1021": "早教",
                            "1022": "其他", "1023": "单餐保姆"
                        }
                        aunt_names.append(default_mapping.get(code, code))
            else:
                # 单个服务项目
                aunt_name_query = text("""
                    SELECT dict_label FROM sys_dict_data
                    WHERE dict_type = 'aunt_type' AND dict_value = :aunt_type_code AND status = '0'
                """)
                aunt_name_result = await db.execute(aunt_name_query, {"aunt_type_code": aunt_type})
                aunt_name_row = aunt_name_result.fetchone()
                if aunt_name_row:
                    aunt_names.append(aunt_name_row[0])
                else:
                    # 使用默认映射
                    default_mapping = {
                        "1001": "保姆", "1002": "产后修复师", "1003": "小儿推拿师",
                        "1004": "催乳师", "1005": "保安", "1006": "中医师",
                        "1007": "家教", "1008": "心理理疗师", "1009": "指导师",
                        "1010": "家政员", "1011": "管家", "1012": "厨师",
                        "1013": "月嫂", "1014": "育儿嫂", "1015": "老年护理",
                        "1016": "小时工", "1017": "白班阿姨", "1018": "别墅家务",
                        "1019": "病人护理", "1020": "育婴师", "1021": "早教",
                        "1022": "其他", "1023": "单餐保姆"
                    }
                    aunt_names.append(default_mapping.get(aunt_type, aunt_type))

            result = ",".join(aunt_names) if aunt_names else "家政服务"
            logger.info(f"aunt_type转换: {aunt_type} -> {result}")
            return result

        except Exception as e:
            logger.error(f"aunt_type转换失败: {aunt_type}, 错误: {str(e)}")
            return aunt_type or "家政服务"

    @classmethod
    async def share_store_lead_to_square(
        cls,
        db: AsyncSession,
        customer_uuid: str,
        commission_amount: float,
        expire_hours: int,
        current_user
    ) -> dict:
        """
        共享到店线索到家政广场

        Args:
            db: 数据库会话
            customer_uuid: 线索UUID
            commission_amount: 佣金金额
            expire_hours: 过期小时数
            current_user: 当前用户

        Returns:
            共享结果
        """
        try:
            from sqlalchemy import text
            from datetime import datetime, timedelta
            import uuid

            # 1. 获取当前用户信息
            user_uuid = getattr(current_user.user, 'uuid', None)
            user_name = getattr(current_user.user, 'name', None)
            store_uuid = getattr(current_user.user, 'store_uuid', None)

            if not all([user_uuid, user_name, store_uuid]):
                raise ValidationException(message="用户信息获取失败")

            # 2. 验证线索是否存在且属于当前用户
            customer_query = text("""
                SELECT *, lng, lat FROM customer
                WHERE uuid = :customer_uuid AND store_uuid = :store_uuid AND is_delete = 0
            """)
            customer_result = await db.execute(customer_query, {
                "customer_uuid": customer_uuid,
                "store_uuid": store_uuid
            })
            customer = customer_result.fetchone()

            if not customer:
                raise BusinessException(message="线索不存在或无权限操作")

            # 3. 检查是否已经共享过（修复：使用正确的字段检查）
            existing_query = text("""
                SELECT uuid FROM demand_square
                WHERE customer_name = :customer_name
                AND customer_phone = :customer_phone
                AND business_type = 2
                AND original_seller_store_uuid = :store_uuid
                AND is_delete = 0
            """)
            existing_result = await db.execute(existing_query, {
                "customer_name": customer.name,
                "customer_phone": customer.mobile,
                "store_uuid": store_uuid
            })
            existing_demand = existing_result.fetchone()

            if existing_demand:
                raise BusinessException(message="该线索已经共享过，不能重复共享")

            # 4. 创建需求记录
            demand_uuid = str(uuid.uuid4()).replace('-', '')
            expire_time = datetime.now() + timedelta(hours=expire_hours)

            # 构建服务地址
            service_address = customer.address or "客户地址待确认"

            insert_sql = text("""
                INSERT INTO demand_square (
                    uuid, platform_order_number, business_type, business_status,
                    commission_amount, service_project, service_address, service_time,
                    demand_status, expire_time, customer_name, customer_phone,
                    customer_address, lng, lat, source_platform, remark, original_seller_store_uuid,
                    create_time, update_time, is_delete
                ) VALUES (
                    :uuid, :platform_order_number, 2, 1,
                    :commission_amount, :service_project, :service_address, :service_time,
                    1, :expire_time, :customer_name, :customer_phone,
                    :customer_address, :lng, :lat, 'store_lead_share', :remark, :original_seller_store_uuid,
                    NOW(), NOW(), 0
                )
            """)

            # 转换aunt_type为中文服务名称
            service_project_name = await cls._convert_aunt_type_to_name(db, customer.aunt_type or "1022")

            await db.execute(insert_sql, {
                "uuid": demand_uuid,
                "platform_order_number": demand_uuid,  # 使用UUID作为平台订单号
                "commission_amount": commission_amount,
                "service_project": service_project_name,  # 使用转换后的中文名称
                "service_address": service_address,
                "service_time": datetime.now() + timedelta(days=1),  # 默认明天服务
                "expire_time": expire_time,
                "customer_name": customer.name,
                "customer_phone": customer.mobile,
                "customer_address": customer.address,
                "lng": customer.lng,  # 从customer表获取经度
                "lat": customer.lat,  # 从customer表获取纬度
                "original_seller_store_uuid": store_uuid,  # 原始卖方门店UUID
                "remark": f"到店线索共享，原线索ID：{customer_uuid}"
            })

            await db.commit()

            logger.info(f"线索共享成功: customer_uuid={customer_uuid}, demand_uuid={demand_uuid}")
            return {
                "success": True,
                "message": "线索共享成功",
                "demand_uuid": demand_uuid,
                "expire_time": expire_time.isoformat()
            }

        except ValidationException as e:
            logger.error(f"线索共享参数验证失败: {e.message}")
            raise e
        except BusinessException as e:
            logger.error(f"线索共享业务异常: {e.message}")
            raise e
        except Exception as e:
            await db.rollback()
            logger.error(f"线索共享服务异常: {str(e)}")
            raise BusinessException(message=f"线索共享失败: {str(e)}")

    @staticmethod
    async def share_order_to_square(
        db: AsyncSession,
        share_request,
        current_user
    ) -> dict:
        """
        共享订单到家政广场
        """
        try:
            from module_admin.entity.do.store import Store
            from datetime import datetime, timedelta
            import uuid

            # 1. 验证订单是否存在且属于当前用户门店，同时获取客户信息
            from module_admin.entity.do.ccuser_extend import CCUserExtend

            # 使用原生SQL查询避免ORM字段映射问题
            order_query = text("""
                SELECT
                    o.id,
                    o.order_number,
                    o.user_id,
                    o.store_id,
                    o.store_uuid,
                    o.product_id,
                    o.product_name,
                    o.service_type,
                    o.service_address,
                    o.service_date,
                    o.service_phone,
                    o.order_status,
                    o.pay_actual,
                    o.remark,
                    o.service_remark,
                    o.address_id,
                    ce.name as customer_name_extend,
                    ce.contact_phone as customer_phone_extend,
                    ce.lng,
                    ce.lat,
                    c.name as customer_name_ccuser
                FROM `order` o
                LEFT JOIN ccuser_extend ce ON o.address_id = ce.id
                LEFT JOIN ccuser c ON o.user_id = c.id
                WHERE o.id = :order_id
                LIMIT 1
            """)

            order_result = await db.execute(order_query, {"order_id": share_request.order_id})
            order = order_result.fetchone()

            if not order:
                return {"success": False, "message": "订单不存在"}

            # 获取用户地址信息（通过 user_id 关联）
            user_address_query = select(
                CCUserExtend.address
            ).where(
                CCUserExtend.ccuser_id == order.user_id
            ).order_by(CCUserExtend.id.desc()).limit(1)  # 取最新的地址记录

            user_address_result = await db.execute(user_address_query)
            user_address_row = user_address_result.first()
            user_address = user_address_row.address if user_address_row else None

            # 2. 验证订单归属权（只能共享本门店的订单）
            user_store_uuid = current_user.user.store_uuid
            if order.store_uuid != user_store_uuid:
                return {"success": False, "message": "只能共享本门店的订单"}

            # 3. 验证订单状态（只有已支付的到家订单可以共享）
            if order.service_type != 1:
                return {"success": False, "message": "只有到家订单可以共享"}

            if order.order_status != 10:
                return {"success": False, "message": "只有已支付的订单可以共享"}

            # 4. 检查订单是否已经共享过
            existing_share_query = select(DemandSquare).where(
                DemandSquare.source_order_id == share_request.order_id
            )
            existing_share_result = await db.execute(existing_share_query)
            existing_share = existing_share_result.scalar_one_or_none()

            if existing_share:
                return {"success": False, "message": "该订单已经共享过了"}

            # 5. 获取门店信息
            store_query = select(Store).where(Store.id == order.store_id)
            store_result = await db.execute(store_query)
            store = store_result.scalar_one_or_none()

            if not store:
                return {"success": False, "message": "门店信息不存在"}

            # 6. 创建demand_square记录，正确映射所有字段
            demand_uuid = str(uuid.uuid4())
            expire_time = datetime.now() + timedelta(hours=share_request.expire_hours)

            # 确定客户姓名：优先使用 ccuser_extend.name，其次使用 ccuser.name
            customer_name = order.customer_name_extend or order.customer_name_ccuser

            # 确定客户电话：优先使用 ccuser_extend.contact_phone，其次使用 order.service_phone
            customer_phone = order.customer_phone_extend or order.service_phone

            # 确定客户地址：优先使用用户地址，其次使用订单服务地址
            customer_address = user_address or order.service_address

            demand_data = DemandSquare(
                uuid=demand_uuid,
                platform_order_number=demand_uuid,  # 使用需求UUID作为平台订单号（满足非空约束）
                jingang_order_number=order.order_number,  # 订单号填写到jingang_order_number字段
                jingang_product_id=str(order.product_id) if order.product_id else None,  # 产品ID映射
                source_order_id=order.id,  # 标记为共享订单
                business_type=1,  # 到家服务
                business_status=1,  # 实单
                commission_amount=share_request.commission_amount,  # 使用用户设置的佣金金额
                service_project=order.product_name,
                service_address=order.service_address,
                service_time=order.service_date,
                lng=order.lng,  # 地理位置坐标
                lat=order.lat,  # 地理位置坐标
                demand_status=1,  # 待抢单
                expire_time=expire_time,
                customer_name=customer_name,  # 通过关联查询获取的客户姓名
                customer_phone=customer_phone,  # 优先使用联系电话
                customer_address=customer_address,  # 客户地址
                service_requirements=order.service_remark,  # 服务要求字段
                remark=order.remark,  # 备注信息
                source_platform="order_share",  # 标记来源为订单共享
                original_seller_store_uuid=order.store_uuid,  # 🔥 修复：设置原始门店UUID，防止自抢单
                create_time=datetime.now(),
                update_time=datetime.now()
            )

            db.add(demand_data)
            await db.commit()

            logger.info(f"订单共享成功: order_id={share_request.order_id}, demand_uuid={demand_uuid}")
            return {"success": True, "message": "订单共享成功", "demand_uuid": demand_uuid}

        except Exception as e:
            await db.rollback()
            logger.error(f"订单共享失败: {str(e)}")
            return {"success": False, "message": f"订单共享失败: {str(e)}"}

    @staticmethod
    async def get_available_staff_list(
        db: AsyncSession,
        store_uuid: str
    ) -> dict:
        """
        获取门店可用员工列表
        """
        try:
            from module_admin.entity.do.service_staff import ServiceStaff
            from module_admin.entity.vo.demand_square_vo import AvailableStaffVO

            # 查询门店的可用员工
            staff_query = select(ServiceStaff).where(
                and_(
                    ServiceStaff.store_uuid == store_uuid,
                    ServiceStaff.status == 1,  # 启用状态
                    ServiceStaff.is_delete == 0  # 未删除
                )
            ).order_by(ServiceStaff.star_level.desc(), ServiceStaff.id.asc())

            staff_result = await db.execute(staff_query)
            staff_list = staff_result.scalars().all()

            # 转换为VO对象
            available_staff = []
            for staff in staff_list:
                staff_vo = AvailableStaffVO(
                    id=staff.id,
                    uuid=staff.uuid,
                    real_name=staff.real_name or staff.user_name,
                    user_name=staff.user_name,
                    star_level=str(staff.star_level or 0),
                    service_cnt=staff.service_cnt or 0,
                    avatar=staff.avatar,
                    status=staff.status
                )
                available_staff.append(staff_vo.model_dump())

            logger.info(f"获取员工列表成功: store_uuid={store_uuid}, 数量={len(available_staff)}")
            return {"list": available_staff}

        except Exception as e:
            logger.error(f"获取员工列表失败: {str(e)}")
            return {"list": []}

    @staticmethod
    async def select_service_staff(
        db: AsyncSession,
        staff_request,
        current_user
    ) -> dict:
        """
        选择服务员工并复制到目标门店
        """
        try:

            # 1. 获取需求信息
            demand_query = select(DemandSquare).where(DemandSquare.uuid == staff_request.demand_uuid)
            demand_result = await db.execute(demand_query)
            demand = demand_result.scalar_one_or_none()

            if not demand:
                return {"success": False, "message": "需求信息不存在"}

            if not demand.source_order_id:
                return {"success": False, "message": "非共享订单，无需选择员工"}

            # 2. 获取原始订单信息
            order_query = text("""
                SELECT id, order_number, store_id, store_uuid, product_id, product_name
                FROM `order`
                WHERE id = :order_id
                LIMIT 1
            """)
            order_result = await db.execute(order_query, {"order_id": demand.source_order_id})
            original_order = order_result.fetchone()

            if not original_order:
                return {"success": False, "message": "原始订单不存在"}

            # 3. 获取目标门店信息（原始订单所属门店）
            target_store_query = select(Store).where(Store.id == original_order.store_id)
            target_store_result = await db.execute(target_store_query)
            target_store = target_store_result.scalar_one_or_none()

            if not target_store:
                return {"success": False, "message": "目标门店信息不存在"}

            # 4. 获取选中的员工信息
            selected_staff_query = select(ServiceStaff).where(ServiceStaff.id == staff_request.selected_staff_id)
            selected_staff_result = await db.execute(selected_staff_query)
            selected_staff = selected_staff_result.scalar_one_or_none()

            if not selected_staff:
                return {"success": False, "message": "选中的员工不存在"}

            # 验证员工归属权（只能选择当前用户门店的员工）
            user_store_uuid = current_user.user.store_uuid
            if selected_staff.store_uuid != user_store_uuid:
                return {"success": False, "message": "只能选择本门店的员工"}

            # === 开始事务操作 ===
            logger.info(f"开始员工处理操作: demand_uuid={staff_request.demand_uuid}, staff_id={staff_request.selected_staff_id}")

            # 5. 检查该手机号是否已在目标门店存在账号
            existing_staff_query = text("""
                SELECT id, uuid FROM service_staff
                WHERE mobile = :mobile AND store_id = :store_id AND status = '1' AND is_delete = '0'
                LIMIT 1
            """)
            existing_result = await db.execute(existing_staff_query, {
                "mobile": selected_staff.mobile,
                "store_id": original_order.store_id
            })
            existing_staff_row = existing_result.fetchone()

            if existing_staff_row:
                # 情况1：员工在目标门店已存在，直接使用现有账号
                target_staff_id = existing_staff_row.id
                target_staff_uuid = existing_staff_row.uuid
                logger.info(f"员工在目标门店已存在，直接使用: 手机号={selected_staff.mobile}, 目标门店={original_order.store_id}, 员工ID={target_staff_id}")

                # 获取现有员工的完整信息
                existing_staff_detail_query = text("""
                    SELECT * FROM service_staff
                    WHERE id = :staff_id AND status = '1' AND is_delete = '0'
                    LIMIT 1
                """)
                existing_detail_result = await db.execute(existing_staff_detail_query, {"staff_id": target_staff_id})
                existing_staff_detail = existing_detail_result.fetchone()

                if not existing_staff_detail:
                    return {"success": False, "message": "目标门店员工信息获取失败"}

                # 使用现有员工信息
                copied_staff = type('MockStaff', (), {
                    'id': target_staff_id,
                    'uuid': target_staff_uuid,
                    'real_name': existing_staff_detail.real_name,
                    'mobile': existing_staff_detail.mobile
                })()

                logger.info(f"使用现有员工账号: ID={target_staff_id}, 姓名={existing_staff_detail.real_name}")

            else:
                # 情况2：员工在目标门店不存在，需要复制创建新账号
                logger.info(f"员工在目标门店不存在，需要复制创建: 手机号={selected_staff.mobile}, 目标门店={original_order.store_id}")

                # 6. 复制员工到目标门店（完整复制除公司绑定外的所有信息）
                new_staff_uuid = str(uuid.uuid4())
                copied_staff = ServiceStaff(
                    uuid=new_staff_uuid,
                    user_id=selected_staff.user_id,
                    # 公司绑定相关字段 - 使用目标门店信息
                    company_id=target_store.company_id,
                    store_id=str(target_store.id),  # 转换为字符串
                    store_uuid=target_store.store_uuid,
                    store_name=target_store.name,

                    # 完整复制原员工的所有其他信息
                    job_type=selected_staff.job_type,
                    user_name=selected_staff.user_name,
                    nick_name=selected_staff.nick_name,
                    real_name=selected_staff.real_name,
                    mobile=selected_staff.mobile,
                    password=selected_staff.password,

                    # 微信相关信息
                    wx_openid=selected_staff.wx_openid,
                    wx_unionid=selected_staff.wx_unionid,
                    wx_bind_time=selected_staff.wx_bind_time,
                    wx_official_openid=selected_staff.wx_official_openid,
                    wx_official_unionid=selected_staff.wx_official_unionid,
                    wx_official_nickname=selected_staff.wx_official_nickname,
                    wx_official_avatar=selected_staff.wx_official_avatar,
                    wx_official_bind_time=selected_staff.wx_official_bind_time,
                    is_bind_wx=selected_staff.is_bind_wx,

                    # 员工属性信息
                    star_level=selected_staff.star_level,
                    status=selected_staff.status,
                    work_type=selected_staff.work_type,
                    is_delete=selected_staff.is_delete,
                    is_part_time_job=selected_staff.is_part_time_job,
                    is_old=selected_staff.is_old,
                    is_allow_rob=selected_staff.is_allow_rob,

                    # 个人信息
                    sex=selected_staff.sex,
                    age=selected_staff.age,
                    avatar=selected_staff.avatar,
                    city_id=selected_staff.city_id,
                    city=selected_staff.city,
                    address=selected_staff.address,

                    # 服务统计信息
                    service_cnt=selected_staff.service_cnt,
                    service_uv=selected_staff.service_uv,
                    service_commission=selected_staff.service_commission,
                    sale_commission=selected_staff.sale_commission,

                    # 归属信息
                    own_user_id=selected_staff.own_user_id,
                    own_user_name=selected_staff.own_user_name,

                    # 保险信息
                    insurance_start_time=selected_staff.insurance_start_time,
                    insurance_end_time=selected_staff.insurance_end_time,

                    # 邀请码
                    invitation_code=selected_staff.invitation_code,

                    # 创建信息
                    created_by='system_copy',
                    created_by_name='系统复制',
                    create_time=datetime.now(),
                    update_time=datetime.now()
                )

                db.add(copied_staff)
                logger.info("员工对象已添加到会话，准备flush获取ID")
                await db.flush()  # 获取新员工的ID
                logger.info(f"员工复制完成，新员工ID: {copied_staff.id}")

                # 6.1. 复制员工扩展信息
                await DemandSquareService._copy_staff_ext_info(db, selected_staff.id, copied_staff.id)

            # 7. 为员工分配产品权限（无论是现有员工还是新创建的员工）
            # 核心逻辑：分配什么服务项目的订单，就自动给服务人员加什么服务项目权限
            if original_order.product_id:
                # 检查员工是否已有该产品权限
                existing_product_query = text("""
                    SELECT COUNT(*) FROM service_product
                    WHERE staff_id = :staff_id AND productid = :product_id
                """)
                existing_product_result = await db.execute(existing_product_query, {
                    "staff_id": copied_staff.id,
                    "product_id": original_order.product_id
                })
                existing_product_count = existing_product_result.scalar() or 0

                if existing_product_count == 0:
                    # 员工还没有该产品权限，自动添加
                    service_product = ServiceProduct(
                        staff_id=copied_staff.id,
                        productid=original_order.product_id,
                        store_uuid=target_store.store_uuid,
                        company_uuid=target_store.company_id,  # company_id实际上就是company_uuid
                        create_time=datetime.now(),
                        update_time=datetime.now()
                    )
                    db.add(service_product)
                    logger.info(f"自动为员工添加产品权限: staff_id={copied_staff.id}, product_id={original_order.product_id}, product_name={original_order.product_name}")
                else:
                    logger.info(f"员工已有产品权限，无需添加: staff_id={copied_staff.id}, product_id={original_order.product_id}, product_name={original_order.product_name}")
            else:
                logger.warning(f"订单没有产品ID，跳过产品权限分配: order_id={original_order.id}")

            # 7. 将原始订单分配给复制的员工（通过order_waiter表）
            logger.info(f"开始订单分配: order_number={original_order.order_number}")

            # 首先删除可能存在的旧分配记录
            delete_old_assignment = text("""
                DELETE FROM order_waiter
                WHERE order_number = :order_number
            """)
            await db.execute(delete_old_assignment, {"order_number": original_order.order_number})
            logger.info("旧的订单分配记录已删除")

            # 插入新的员工分配记录
            insert_assignment = text("""
                INSERT INTO order_waiter (
                    order_number,
                    service_id,
                    service_name,
                    service_personal_commission,
                    service_personal,
                    create_time
                ) VALUES (
                    :order_number,
                    :service_id,
                    :service_name,
                    :service_personal_commission,
                    :service_personal,
                    NOW()
                )
            """)
            await db.execute(insert_assignment, {
                "order_number": original_order.order_number,
                "service_id": str(copied_staff.id),
                "service_name": copied_staff.real_name,
                "service_personal_commission": 0,  # 固定为0
                "service_personal": f"{float(demand.commission_amount):.2f}"  # 佣金金额，保留2位小数
            })
            logger.info(f"新的订单分配记录已插入: staff_id={copied_staff.id}")

            # 8. 更新原订单状态为已派单
            logger.info("开始更新原订单状态")
            update_order_query = text("""
                UPDATE `order`
                SET order_status = 40, order_status_name = '已派单', update_time = NOW()
                WHERE id = :order_id
            """)
            await db.execute(update_order_query, {"order_id": original_order.id})
            logger.info(f"原订单状态已更新为已派单: order_id={original_order.id}")

            # 9. 更新demand_square记录，标记为已完成
            logger.info("开始更新需求广场状态")
            update_demand_query = update(DemandSquare).where(
                DemandSquare.uuid == staff_request.demand_uuid
            ).values(
                created_order_number=original_order.order_number,
                update_time=datetime.now()
            )
            await db.execute(update_demand_query)
            logger.info("需求广场状态更新完成")

            # === 准备响应数据（在提交前） ===
            response_data = {
                "success": True,
                "message": "员工选择成功，订单已分配",
                "order_number": original_order.order_number,
                "order_status": 20
            }

            # 准备日志信息（在提交前）
            success_log = f"员工选择成功: demand_uuid={staff_request.demand_uuid}, staff_id={staff_request.selected_staff_id}, new_staff_id={copied_staff.id}"

            # === 提交事务 ===
            logger.info("准备提交事务")
            await db.commit()
            logger.info("事务提交成功")

            # 记录成功日志（在事务提交后）
            logger.info(success_log)
            return response_data

        except Exception as e:
            # === 回滚事务 ===
            try:
                await db.rollback()
                logger.info("事务已回滚")
            except Exception as rollback_error:
                logger.error(f"事务回滚失败: {str(rollback_error)}")

            error_message = f"员工选择失败: {str(e)}"
            logger.error(error_message)

            return {
                "success": False,
                "message": error_message
            }

    @staticmethod
    async def cancel_demand_grab(
        db: AsyncSession,
        demand_uuid: str,
        current_user
    ) -> dict:
        """
        取消抢单，回滚需求状态
        """
        try:
            # 1. 获取需求信息
            demand_query = select(DemandSquare).where(DemandSquare.uuid == demand_uuid)
            demand_result = await db.execute(demand_query)
            demand = demand_result.scalar_one_or_none()

            if not demand:
                return {"success": False, "message": "需求不存在"}

            # 2. 检查是否为当前用户抢的单
            user_store_uuid = current_user.user.store_uuid
            if demand.grab_store_uuid != user_store_uuid:
                return {"success": False, "message": "只能取消自己抢的单"}

            # 3. 检查需求状态
            if demand.demand_status != 2:
                return {"success": False, "message": "该需求不是已抢单状态，无法取消"}

            # 4. 检查是否已经分配了员工（如果已分配员工，不允许取消）
            if demand.source_order_id:
                # 共享订单：检查是否已经有员工分配
                order_waiter_query = text("""
                    SELECT COUNT(*) as count
                    FROM order_waiter ow
                    LEFT JOIN `order` o ON ow.order_number = o.order_number
                    WHERE o.id = :source_order_id
                      AND ow.create_time > :grab_time
                """)
                result = await db.execute(order_waiter_query, {
                    "source_order_id": demand.source_order_id,
                    "grab_time": demand.grab_time
                })
                count = result.scalar()

                if count > 0:
                    return {"success": False, "message": "该订单已分配员工，无法取消抢单"}

            # 5. 回滚需求状态
            update_demand_query = update(DemandSquare).where(
                DemandSquare.uuid == demand_uuid
            ).values(
                demand_status=1,  # 回滚为待抢单
                grab_store_uuid=None,
                grab_store_name=None,
                grab_user_uuid=None,
                grab_user_name=None,
                grab_time=None,
                update_time=datetime.now()
            )

            await db.execute(update_demand_query)
            await db.commit()

            logger.info(f"取消抢单成功: demand_uuid={demand_uuid}, user_store={user_store_uuid}")
            return {
                "success": True,
                "message": "取消抢单成功",
                "demand_uuid": demand_uuid
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"取消抢单失败: {str(e)}")
            return {"success": False, "message": f"取消抢单失败: {str(e)}"}

    @staticmethod
    async def adjust_shared_order_commission(
        db: AsyncSession,
        order_id: int,
        new_commission_amount: float,
        current_user
    ) -> dict:
        """
        调整已共享订单的佣金金额
        """
        try:
            # 参数验证
            if not order_id or order_id <= 0:
                return {"success": False, "message": "订单ID无效"}

            if not new_commission_amount or new_commission_amount <= 0:
                return {"success": False, "message": "佣金金额必须大于0"}

            if new_commission_amount > 99999.99:  # 防止过大的金额
                return {"success": False, "message": "佣金金额过大"}

            logger.info(f"开始调整佣金: order_id={order_id}, new_commission={new_commission_amount}, user_store={current_user.user.store_uuid}")

            # 1. 验证订单是否存在且属于当前用户门店
            order_number = None
            order_store_uuid = None
            order_pay_actual = None

            try:
                order_query = text("""
                    SELECT id, order_number, store_uuid, pay_actual
                    FROM `order`
                    WHERE id = :order_id
                    LIMIT 1
                """)
                order_result = await db.execute(order_query, {"order_id": order_id})
                order_row = order_result.fetchone()

                if not order_row:
                    logger.warning(f"订单不存在: order_id={order_id}")
                    return {"success": False, "message": "订单不存在"}

                # 提取订单信息到局部变量
                order_number = order_row.order_number
                order_store_uuid = order_row.store_uuid
                order_pay_actual = order_row.pay_actual

                logger.info(f"订单查询成功: order_number={order_number}, store_uuid={order_store_uuid}")

            except Exception as e:
                logger.error(f"查询订单信息失败: order_id={order_id}, error={str(e)}")
                return {"success": False, "message": f"查询订单信息失败: {str(e)}"}

            # 验证订单归属权
            user_store_uuid = current_user.user.store_uuid
            if order_store_uuid != user_store_uuid:
                logger.warning(f"订单归属权验证失败: order_store={order_store_uuid}, user_store={user_store_uuid}")
                return {"success": False, "message": "只能调整本门店的订单佣金"}

            # 2. 查询订单的共享记录
            try:
                # 使用索引优化查询：先按 source_order_id 查询，再过滤状态
                demand_query = select(DemandSquare).where(
                    and_(
                        DemandSquare.source_order_id == order_id,
                        DemandSquare.demand_status == 1,  # 只有待抢单状态才能调整佣金
                        DemandSquare.is_delete == 0  # 确保记录未删除
                    )
                ).limit(1)  # 限制结果数量，提高性能
                demand_result = await db.execute(demand_query)
                demand = demand_result.scalar_one_or_none()

                if not demand:
                    logger.warning(f"订单未共享或已被抢单: order_id={order_id}")
                    return {"success": False, "message": "订单未共享或已被抢单，无法调整佣金"}

                logger.info(f"共享记录查询成功: demand_uuid={demand.uuid}, current_commission={demand.commission_amount}")

            except Exception as e:
                logger.error(f"查询共享记录失败: order_id={order_id}, error={str(e)}")
                return {"success": False, "message": f"查询共享记录失败: {str(e)}"}

            # 3. 验证新佣金金额
            if new_commission_amount <= 0:
                return {"success": False, "message": "佣金金额必须大于0"}

            if new_commission_amount > float(order_pay_actual or 0):
                return {"success": False, "message": "佣金金额不能超过订单总金额"}

            # 4. 验证只能增加佣金，不能减少
            current_commission = float(demand.commission_amount or 0)
            if new_commission_amount <= current_commission:
                return {"success": False, "message": f"新佣金金额必须大于当前佣金金额¥{current_commission}"}

            # 验证佣金增幅是否合理（防止恶意操作）
            max_order_amount = float(order_pay_actual or 0)
            if current_commission > 0:
                increase_ratio = (new_commission_amount - current_commission) / current_commission
                if increase_ratio > 10:  # 增幅超过1000%
                    logger.warning(f"佣金增幅过大: order_id={order_id}, increase_ratio={increase_ratio:.2f}")
                    return {"success": False, "message": "单次佣金调整幅度过大，请分次调整"}

            # 验证佣金占订单金额的比例是否合理
            if max_order_amount > 0:
                commission_ratio = new_commission_amount / max_order_amount
                if commission_ratio > 1.0:  # 佣金超过订单金额的100%
                    logger.warning(f"佣金比例过高: order_id={order_id}, commission_ratio={commission_ratio:.2f}")
                    return {"success": False, "message": "佣金金额不能超过订单总金额"}

            # 5. 更新佣金金额
            old_commission = demand.commission_amount
            demand_id = demand.id
            demand_uuid = demand.uuid

            try:
                logger.info(f"准备更新佣金: demand_id={demand_id}, old_commission={old_commission}, new_commission={new_commission_amount}")

                update_query = update(DemandSquare).where(
                    DemandSquare.id == demand_id
                ).values(
                    commission_amount=new_commission_amount,
                    update_time=datetime.now()
                )

                result = await db.execute(update_query)
                logger.info(f"数据库更新执行完成: affected_rows={result.rowcount}")

                await db.commit()
                logger.info(f"事务提交成功")

                logger.info(f"佣金调整成功: order_id={order_id}, order_number={order_number}, old_commission={old_commission}, new_commission={new_commission_amount}")

                return {
                    "success": True,
                    "message": "佣金调整成功",
                    "old_commission": float(old_commission),
                    "new_commission": new_commission_amount,
                    "order_id": order_id,
                    "demand_uuid": demand_uuid
                }

            except Exception as e:
                await db.rollback()
                logger.error(f"更新佣金失败: demand_id={demand_id}, error={str(e)}")
                return {"success": False, "message": f"更新佣金失败: {str(e)}"}

        except Exception as e:
            try:
                await db.rollback()
                logger.info("事务回滚成功")
            except Exception as rollback_error:
                logger.error(f"事务回滚失败: {str(rollback_error)}")

            logger.error(f"调整佣金失败: order_id={order_id}, user_id={current_user.user.id}, error={str(e)}", exc_info=True)

            # 根据错误类型返回更具体的错误信息
            if "Unknown column" in str(e):
                return {"success": False, "message": "数据库字段错误，请联系技术支持"}
            elif "doesn't exist" in str(e):
                return {"success": False, "message": "数据表不存在，请联系技术支持"}
            elif "Connection" in str(e):
                return {"success": False, "message": "数据库连接异常，请稍后重试"}
            else:
                return {"success": False, "message": f"调整佣金失败: {str(e)}"}

    @staticmethod
    async def check_order_shared_status(
        db: AsyncSession,
        order_id: int
    ) -> dict:
        """
        检查订单是否已共享到家政人广场
        """
        try:
            # 查询订单是否在需求广场中
            demand_query = select(DemandSquare).where(
                and_(
                    DemandSquare.source_order_id == order_id,
                    DemandSquare.demand_status.in_([1, 2])  # 待抢单或已抢单
                )
            )
            demand_result = await db.execute(demand_query)
            demand = demand_result.scalar_one_or_none()

            if demand:
                return {
                    "success": True,
                    "is_shared": True,
                    "demand_uuid": demand.uuid,
                    "demand_status": demand.demand_status,
                    "demand_status_name": "待抢单" if demand.demand_status == 1 else "已抢单",
                    "commission_amount": float(demand.commission_amount or 0),
                    "grab_store_name": demand.grab_store_name,
                    "grab_time": demand.grab_time.isoformat() if demand.grab_time else None
                }
            else:
                return {
                    "success": True,
                    "is_shared": False
                }

        except Exception as e:
            logger.error(f"检查订单共享状态失败: {str(e)}")
            return {"success": False, "message": f"检查订单共享状态失败: {str(e)}"}

    @staticmethod
    async def expire_shared_order(
        db: AsyncSession,
        order_id: int,
        current_user
    ) -> dict:
        """
        将共享订单设置为过期状态
        """
        try:
            # 1. 查询订单的共享记录 - 使用原生SQL避免ORM延迟加载问题
            demand_query = text("""
                SELECT id, uuid, demand_status, grab_time
                FROM demand_square
                WHERE source_order_id = :order_id
                  AND demand_status IN (1, 2)
                  AND is_delete = 0
                LIMIT 1
            """)
            demand_result = await db.execute(demand_query, {"order_id": order_id})
            demand_row = demand_result.fetchone()

            if not demand_row:
                return {"success": False, "message": "订单未共享或已处理"}

            # 提取需求信息到局部变量
            demand_id = demand_row.id
            demand_uuid = demand_row.uuid
            demand_status = demand_row.demand_status
            grab_time = demand_row.grab_time

            # 2. 检查是否为订单所属门店的用户
            order_query = text("""
                SELECT id, order_number, store_uuid
                FROM `order`
                WHERE id = :order_id
                LIMIT 1
            """)
            order_result = await db.execute(order_query, {"order_id": order_id})
            order_row = order_result.fetchone()

            if not order_row:
                return {"success": False, "message": "订单不存在"}

            user_store_uuid = current_user.user.store_uuid
            if order_row.store_uuid != user_store_uuid:
                return {"success": False, "message": "只能操作本门店的订单"}

            # 3. 如果已被抢单，需要先检查是否已分配员工
            if demand_status == 2:
                # 检查是否已分配员工
                order_waiter_query = text("""
                    SELECT COUNT(*) as count
                    FROM order_waiter ow
                    WHERE ow.order_number = :order_number
                      AND ow.create_time > :grab_time
                """)
                result = await db.execute(order_waiter_query, {
                    "order_number": order_row.order_number,
                    "grab_time": grab_time
                })
                count = result.scalar()

                if count > 0:
                    return {"success": False, "message": "订单已被接单并分配员工，无法取消共享"}

            # 4. 更新需求状态为过期 - 使用原生SQL避免ORM问题
            update_demand_query = text("""
                UPDATE demand_square
                SET demand_status = 3, update_time = NOW()
                WHERE id = :demand_id
            """)

            await db.execute(update_demand_query, {"demand_id": demand_id})
            await db.commit()

            logger.info(f"共享订单已设置为过期: order_id={order_id}, demand_uuid={demand_uuid}")
            return {
                "success": True,
                "message": "共享订单已取消，可以进行派单",
                "demand_uuid": demand_uuid
            }

        except Exception as e:
            # 安全地处理回滚
            try:
                await db.rollback()
            except Exception as rollback_error:
                logger.error(f"回滚事务失败: {str(rollback_error)}")
            
            logger.error(f"设置共享订单过期失败: {str(e)}", exc_info=True)
            return {"success": False, "message": f"操作失败: {str(e)}"}

    @staticmethod
    async def _copy_staff_ext_info(
        db: AsyncSession,
        original_staff_id: int,
        new_staff_id: int
    ):
        """
        复制员工扩展信息
        """
        try:
            # 查询原始员工的扩展信息
            original_ext_query = text("""
                SELECT
                    id_number, birthday, lng, lat, live_pos, native_place, nation,
                    marriage_status, education_background, height, weight, family_address,
                    source, province_id, province_name, area_id, area_name, address_desc,
                    emergency_contact_name, emergency_contact_relation, emergency_contact_phone,
                    emergency_contact_address, start_time, end_time, rest_days
                FROM service_staff_ext
                WHERE staff_id = :staff_id
                LIMIT 1
            """)

            original_ext_result = await db.execute(original_ext_query, {"staff_id": original_staff_id})
            original_ext = original_ext_result.fetchone()

            if original_ext:
                # 创建新的员工扩展信息
                insert_ext_sql = text("""
                    INSERT INTO service_staff_ext (
                        staff_id, id_number, birthday, lng, lat, live_pos, native_place, nation,
                        marriage_status, education_background, height, weight, family_address,
                        source, province_id, province_name, area_id, area_name, address_desc,
                        emergency_contact_name, emergency_contact_relation, emergency_contact_phone,
                        emergency_contact_address, start_time, end_time, rest_days,
                        created_by, created_at, updated_at
                    ) VALUES (
                        :staff_id, :id_number, :birthday, :lng, :lat, :live_pos, :native_place, :nation,
                        :marriage_status, :education_background, :height, :weight, :family_address,
                        :source, :province_id, :province_name, :area_id, :area_name, :address_desc,
                        :emergency_contact_name, :emergency_contact_relation, :emergency_contact_phone,
                        :emergency_contact_address, :start_time, :end_time, :rest_days,
                        'system_copy', NOW(), NOW()
                    )
                """)

                await db.execute(insert_ext_sql, {
                    "staff_id": new_staff_id,
                    "id_number": original_ext.id_number,
                    "birthday": original_ext.birthday,
                    "lng": original_ext.lng,
                    "lat": original_ext.lat,
                    "live_pos": original_ext.live_pos,
                    "native_place": original_ext.native_place,
                    "nation": original_ext.nation,
                    "marriage_status": original_ext.marriage_status,
                    "education_background": original_ext.education_background,
                    "height": original_ext.height,
                    "weight": original_ext.weight,
                    "family_address": original_ext.family_address,
                    "source": original_ext.source,
                    "province_id": original_ext.province_id,
                    "province_name": original_ext.province_name,
                    "area_id": original_ext.area_id,
                    "area_name": original_ext.area_name,
                    "address_desc": original_ext.address_desc,
                    "emergency_contact_name": original_ext.emergency_contact_name,
                    "emergency_contact_relation": original_ext.emergency_contact_relation,
                    "emergency_contact_phone": original_ext.emergency_contact_phone,
                    "emergency_contact_address": original_ext.emergency_contact_address,
                    "start_time": original_ext.start_time,
                    "end_time": original_ext.end_time,
                    "rest_days": original_ext.rest_days
                })

                logger.info(f"员工扩展信息复制成功: original_staff_id={original_staff_id}, new_staff_id={new_staff_id}")
            else:
                # 如果原始员工没有扩展信息，创建默认的扩展信息
                insert_default_ext_sql = text("""
                    INSERT INTO service_staff_ext (
                        staff_id, marriage_status, education_background, height, weight,
                        emergency_contact_relation, area_id, start_time, end_time, rest_days,
                        created_by, created_at, updated_at
                    ) VALUES (
                        :staff_id, '0', '0', '0', '0', '0', '0', 8, 18, '',
                        'system_copy', NOW(), NOW()
                    )
                """)

                await db.execute(insert_default_ext_sql, {"staff_id": new_staff_id})
                logger.info(f"创建默认员工扩展信息: new_staff_id={new_staff_id}")

        except Exception as e:
            logger.error(f"复制员工扩展信息失败: {str(e)}")
            # 不抛出异常，避免影响主流程
            pass
