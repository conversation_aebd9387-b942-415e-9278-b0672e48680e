from datetime import datetime
from sqlalchemy import Column, String, Integer, DateTime, BigInteger, DECIMAL, Text
from config.database import Base


class CCUser(Base):
    """CC用户表"""
    __tablename__ = 'ccuser'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='用户ID')
    uuid = Column(String(64), nullable=True, comment='用户UUID')
    name = Column(String(50), nullable=True, comment='用户姓名')
    mobile = Column(String(20), nullable=True, comment='手机号')
    password = Column(String(100), nullable=True, comment='密码')
    avatar = Column(String(255), nullable=True, comment='头像')
    sex = Column(String(2), nullable=True, comment='性别')
    age = Column(Integer, nullable=True, comment='年龄')
    amount = Column(DECIMAL(10, 2), nullable=True, default=0.00, comment='余额')
    city_id = Column(String(20), nullable=True, comment='城市ID')
    city = Column(String(50), nullable=True, comment='城市')
    address = Column(String(255), nullable=True, comment='地址')
    lng = Column(String(20), nullable=True, comment='经度')
    lat = Column(String(20), nullable=True, comment='纬度')
    
    # 微信相关字段
    wx_openid = Column(String(64), nullable=True, comment='微信OpenID')
    wx_unionid = Column(String(64), nullable=True, comment='微信UnionID')
    wx_nickname = Column(String(100), nullable=True, comment='微信昵称')
    wx_avatar = Column(String(255), nullable=True, comment='微信头像')
    
    # 状态字段
    status = Column(String(2), nullable=True, default='1', comment='状态')
    is_delete = Column(String(2), nullable=True, default='0', comment='是否删除')
    
    # 时间字段
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, nullable=True, default=datetime.now, onupdate=datetime.now, comment='更新时间')
