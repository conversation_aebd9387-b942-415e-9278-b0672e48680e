"""
插入需求广场测试数据脚本
基于数据表.txt中的真实数据示例
"""
import asyncio
import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal
import uuid as uuid_lib

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import AsyncSessionLocal
from module_admin.entity.do.demand_square import DemandSquare

async def insert_test_data():
    """插入测试数据"""
    async with AsyncSessionLocal() as db:
        try:
            # 基于数据表.txt中的真实数据示例
            test_data = [
                {
                    'uuid': 'uuid_001',
                    'platform_order_number': 'platform_order_001',
                    'jingang_product_id': 'jingang_prod_001',
                    'jingang_order_number': 'jingang_order_001',
                    'business_type': 1,  # 到家
                    'business_status': 1,
                    'commission_amount': Decimal('100.00'),
                    'service_project': '空调安装',
                    'service_address': '厦门集美区福建省厦门市集美区灌口镇',
                    'service_time': datetime(2025, 7, 15, 13, 0, 0),
                    'lng': '118.1100',
                    'lat': '24.4700',
                    'demand_status': 1,  # 待抢单
                    'expire_time': datetime(2025, 7, 20, 18, 0, 0),
                    'customer_name': '李四',
                    'customer_phone': '***********',
                    'customer_address': '厦门集美区福建省厦门市集美区灌口镇',
                    'service_requirements': '需要安装空调并提供清洁服务',
                    'source_platform': '平台A',
                    'platform_fee': Decimal('20.00'),
                    'remark': '安装服务',
                    'is_delete': 0
                },
                {
                    'uuid': 'uuid_002',
                    'platform_order_number': 'platform_order_002',
                    'jingang_product_id': 'jingang_prod_002',
                    'jingang_order_number': 'jingang_order_002',
                    'business_type': 2,  # 到店
                    'business_status': 1,
                    'commission_amount': Decimal('150.00'),
                    'service_project': '智能家居安装',
                    'service_address': '上海市浦东新区某大厦',
                    'service_time': datetime(2025, 7, 17, 14, 0, 0),
                    'lng': '121.5200',
                    'lat': '31.2300',
                    'demand_status': 1,  # 待抢单
                    'expire_time': datetime(2025, 7, 22, 18, 0, 0),
                    'customer_name': '王五',
                    'customer_phone': '***********',
                    'customer_address': '上海市浦东新区某大厦',
                    'service_requirements': '需要智能家居的安装与调试',
                    'source_platform': '平台B',
                    'platform_fee': Decimal('30.00'),
                    'remark': '安装调试',
                    'is_delete': 0
                },
                {
                    'uuid': 'uuid_003',
                    'platform_order_number': 'platform_order_003',
                    'jingang_product_id': 'jingang_prod_003',
                    'jingang_order_number': 'jingang_order_003',
                    'business_type': 1,  # 到家
                    'business_status': 1,
                    'commission_amount': Decimal('200.00'),
                    'service_project': '家电维修',
                    'service_address': '北京市朝阳区某小区',
                    'service_time': datetime(2025, 7, 18, 15, 0, 0),
                    'lng': '116.4100',
                    'lat': '39.9100',
                    'demand_status': 1,  # 待抢单
                    'expire_time': datetime(2025, 7, 23, 18, 0, 0),
                    'customer_name': '赵六',
                    'customer_phone': '***********',
                    'customer_address': '北京市朝阳区某小区',
                    'service_requirements': '需要维修电视机和空调',
                    'source_platform': '平台C',
                    'platform_fee': Decimal('25.00'),
                    'remark': '维修服务',
                    'is_delete': 0
                }
            ]
            
            # 插入数据
            for data in test_data:
                demand = DemandSquare(**data)
                db.add(demand)
            
            await db.commit()
            print(f"成功插入 {len(test_data)} 条测试数据")
            
        except Exception as e:
            await db.rollback()
            print(f"插入数据失败: {str(e)}")
            raise

if __name__ == "__main__":
    asyncio.run(insert_test_data())
