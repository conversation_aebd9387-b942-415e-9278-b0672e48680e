<template>
  <view class="payment-modal-overlay" v-if="visible" @click="handleOverlayClick">
    <view class="payment-modal" @click.stop>
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <text class="modal-title">选择支付方式</text>
        <view class="close-btn" @click="handleClose">
          <u-icon name="close" size="20" color="#666"></u-icon>
        </view>
      </view>

      <!-- 支付方式列表 -->
      <view class="payment-methods">
        <view 
          class="payment-method-item"
          v-for="(method, index) in paymentMethods"
          :key="index"
          @click="handleMethodSelect(method)"
        >
          <view class="method-info">
            <view class="method-icon" :class="method.iconClass">
              <u-icon :name="method.icon" size="20" color="#fff"></u-icon>
            </view>
            <view class="method-details">
              <text class="method-name">{{ method.name }}</text>
              <text class="method-desc" v-if="method.desc">{{ method.desc }}</text>
            </view>
          </view>
          <view class="method-balance" v-if="method.showBalance">
            <text class="balance-amount">¥{{ customerBalance }}</text>
          </view>
          <view class="method-arrow">
            <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
          </view>
        </view>
      </view>

      <!-- 取消按钮 -->
      <view class="cancel-section">
        <view class="cancel-btn" @click="handleClose">
          <text>取消</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PaymentMethodModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      customerBalance: '0.00',
      paymentMethods: [
        {
          key: 'store_deduct',
          name: '门店代扣',
          desc: '从门店账户余额中扣费',
          icon: 'home-fill',
          iconClass: 'store-icon'
        },
        {
          key: 'balance_pay',
          name: '余额支付',
          desc: '从客户余额中扣费',
          icon: 'account-fill',
          iconClass: 'balance-icon',
          showBalance: true
        },
        {
          key: 'cash_pay',
          name: '现金支付',
          desc: '线下现金交易',
          icon: 'rmb-circle-fill',
          iconClass: 'cash-icon'
        },

        {
          key: 'yibao_pay',
          name: '易宝支付',
          desc: '功能开发中',
          icon: 'rmb-circle',
          iconClass: 'yibao-icon'
        },
        {
          key: 'qrcode_pay',
          name: '扫码支付',
          desc: '微信扫码支付',
          icon: 'scan',
          iconClass: 'qrcode-icon'
        }
      ]
    };
  },
  watch: {
    visible(newVal) {
      if (newVal && this.orderInfo && this.orderInfo.user_id) {
        this.getCustomerBalance();
      }
    },
    orderInfo: {
      handler(newVal) {
        if (newVal && newVal.user_id && this.visible) {
          this.getCustomerBalance();
        }
      },
      deep: true
    }
  },
  methods: {
    handleOverlayClick() {
      this.handleClose();
    },
    
    handleClose() {
      this.$emit('close');
    },
    
    handleMethodSelect(method) {
      if (method.key === 'store_deduct' || method.key === 'cash_pay' || method.key === 'balance_pay' || method.key === 'qrcode_pay') {
        // 门店代扣、现金支付、余额支付和扫码支付：执行实际的支付逻辑
        this.$emit('select', method);
      } else {
        // 其他支付方式：显示开发中提示
        uni.showToast({
          title: '功能开发中',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 获取客户余额
    async getCustomerBalance() {
      if (!this.orderInfo || !this.orderInfo.user_id) {
        this.customerBalance = '0.00';
        return;
      }

      try {
        const response = await this.$get('/api/v1/cc/getCcUserBalance', {
          user_id: this.orderInfo.user_id
        });

        if (response && response.amount !== undefined) {
          // API直接返回包含amount字段的数据
          this.customerBalance = (response.amount || 0).toFixed(2);
        } else {
          this.customerBalance = '0.00';
        }
      } catch (error) {
        console.error('获取客户余额失败:', error);
        this.customerBalance = '0.00';
        // 可以选择显示错误提示
        // uni.showToast({
        //   title: '获取余额失败',
        //   icon: 'none'
        // });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.payment-modal {
  width: 100%;
  max-height: 70vh;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  padding: 10rpx;
}

.payment-methods {
  padding: 20rpx 0;
}

.payment-method-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  
  &:active {
    background-color: #f8f8f8;
  }
}

.method-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.method-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  
  &.store-icon {
    background-color: #007AFF;
  }
  
  &.balance-icon {
    background-color: #34C759;
  }
  
  &.cash-icon {
    background-color: #FF9500;
  }
  

  
  &.yibao-icon {
    background-color: #FF3B30;
  }
  
  &.qrcode-icon {
    background-color: #5856D6;
  }
}

.method-details {
  display: flex;
  flex-direction: column;
}

.method-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.method-desc {
  font-size: 24rpx;
  color: #999;
}

.method-balance {
  margin-left: auto;
  margin-right: 20rpx;
}

.balance-amount {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6b35;
}

.method-arrow {
  margin-left: 20rpx;
}

.cancel-section {
  padding: 20rpx 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn {
  width: 100%;
  height: 88rpx;
  background-color: #f8f8f8;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  text {
    font-size: 32rpx;
    color: #666;
  }
  
  &:active {
    background-color: #e8e8e8;
  }
}
</style>
