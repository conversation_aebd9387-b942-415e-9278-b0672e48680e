<template>
  <view class="staff-detail-page" :style="{ '--status-bar-height': StatusBar + 'px' }">
    <!-- 现代化头部 -->
    <view class="detail-header">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航 -->
        <view class="top-nav">
          <view class="nav-left" @click="goBack">
            <view class="back-btn">
              <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
            </view>
          </view>
          <view class="nav-center">
            <text class="nav-title">员工详情</text>
          </view>
          <view class="nav-right"></view>
        </view>

        <!-- 员工状态卡片 -->
        <view class="staff-status-card" v-if="staffDetail">
          <view class="status-main">
            <view class="staff-avatar">
              <image
                v-if="staffDetail.basic_info.avatar"
                :src="staffDetail.basic_info.avatar"
                class="avatar-img"
                mode="aspectFill"
              />
              <view v-else class="avatar-placeholder">
                <u-icon name="account" size="32" color="#fff"></u-icon>
              </view>
            </view>
            <view class="staff-info">
              <text class="staff-name">{{ staffDetail.basic_info.real_name || '未知员工' }}</text>
              <text class="staff-mobile">{{ staffDetail.basic_info.mobile || '无联系方式' }}</text>
            </view>
            <view class="staff-actions">
              <view class="credit-query-btn" @click="handleCreditQuery">
                <view class="btn-icon">
                  <u-icon name="search" size="18" color="#fff"></u-icon>
                </view>
                <text class="btn-text">信用查询</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="spinner" size="40"></u-loading-icon>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 员工详情内容 -->
    <view v-else-if="staffDetail" class="detail-content">

      <!-- 基本信息卡片 -->
      <view class="info-card basic-card">
        <view class="card-header">
          <view class="header-icon basic-icon">
            <u-icon name="account" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">基本信息</text>
        </view>
        <view class="basic-content">
          <view class="basic-row">
            <text class="basic-label">姓名</text>
            <text class="basic-value">{{ staffDetail.basic_info.real_name || '未设置' }}</text>
          </view>
          <view class="basic-row">
            <text class="basic-label">手机号</text>
            <view class="basic-value-container">
              <text class="basic-value">{{ staffDetail.basic_info.mobile || '未设置' }}</text>
              <u-icon
                name="edit-pen"
                size="16"
                color="#1890ff"
                @click="showEditPopup('mobile')"
                class="edit-icon"
              />
            </view>
          </view>
          <view class="basic-row">
            <text class="basic-label">性别</text>
            <text class="basic-value">{{ getSexText(staffDetail.basic_info.sex) }}</text>
          </view>
          <view class="basic-row">
            <text class="basic-label">年龄</text>
            <text class="basic-value">{{ staffDetail.basic_info.age || '未设置' }}岁</text>
          </view>
          <view class="basic-row">
            <text class="basic-label">星级评分</text>
            <text class="basic-value">{{ staffDetail.basic_info.star_level || '0' }}星</text>
          </view>
        </view>
      </view>

      <!-- 公司门店信息卡片 -->
      <view class="info-card company-card">
        <view class="card-header">
          <view class="header-icon company-icon">
            <u-icon name="home" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">公司门店</text>
        </view>
        <view class="company-content">
          <view class="company-row">
            <text class="company-label">门店名称</text>
            <text class="company-value">{{ staffDetail.company_info.store_name || '未设置' }}</text>
          </view>

          <view class="company-row">
            <text class="company-label">默认公司</text>
            <text class="company-value">{{ staffDetail.company_info.is_default_company === '1' ? '是' : '否' }}</text>
          </view>
        </view>
      </view>

      <!-- 工作统计卡片 -->
      <view class="info-card stats-card">
        <view class="card-header">
          <view class="header-icon stats-icon">
            <u-icon name="list" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">工作统计</text>
        </view>
        <view class="stats-content">
          <view class="stats-row">
            <text class="stats-label">服务次数</text>
            <text class="stats-value">{{ staffDetail.work_stats.service_cnt || '0' }}次</text>
          </view>
          <view class="stats-row">
            <text class="stats-label">服务用户数</text>
            <text class="stats-value">{{ staffDetail.work_stats.service_uv || '0' }}人</text>
          </view>
          <view class="stats-row">
            <text class="stats-label">服务提成</text>
            <text class="stats-value">¥{{ staffDetail.work_stats.service_commission || '0' }}</text>
          </view>
          <view class="stats-row">
            <text class="stats-label">销售提成</text>
            <text class="stats-value">¥{{ staffDetail.work_stats.sale_commission || '0' }}</text>
          </view>
          <view class="stats-row">
            <text class="stats-label">允许抢单</text>
            <text class="stats-value">{{ staffDetail.work_stats.is_allow_rob === '1' ? '是' : '否' }}</text>
          </view>
        </view>
      </view>

      <!-- 扩展信息卡片 -->
      <view class="info-card ext-card" v-if="staffDetail.ext_info">
        <view class="card-header">
          <view class="header-icon ext-icon">
            <u-icon name="info-circle" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">扩展信息</text>
        </view>
        <view class="ext-content">
          <view class="ext-row" v-if="staffDetail.ext_info.id_card">
            <text class="ext-label">身份证号</text>
            <text class="ext-value">{{ staffDetail.ext_info.id_card }}</text>
          </view>
          <view class="ext-row" v-if="staffDetail.ext_info.birthday">
            <text class="ext-label">生日</text>
            <text class="ext-value">{{ staffDetail.ext_info.birthday }}</text>
          </view>
          <view class="ext-row" v-if="staffDetail.ext_info.education">
            <text class="ext-label">学历</text>
            <text class="ext-value">{{ staffDetail.ext_info.education }}</text>
          </view>
          <view class="ext-row" v-if="staffDetail.ext_info.marital_status">
            <text class="ext-label">婚姻状况</text>
            <text class="ext-value">{{ getMaritalStatusText(staffDetail.ext_info.marital_status) }}</text>
          </view>
          <view class="ext-row" v-if="staffDetail.ext_info.native_place">
            <text class="ext-label">籍贯</text>
            <text class="ext-value">{{ staffDetail.ext_info.native_place }}</text>
          </view>
          <view class="ext-row" v-if="staffDetail.ext_info.current_address">
            <text class="ext-label">现住址</text>
            <view class="ext-value-container">
              <text class="ext-value">{{ staffDetail.ext_info.current_address }}</text>
              <u-icon
                name="edit-pen"
                size="16"
                color="#1890ff"
                @click="showEditPopup('address')"
                class="edit-icon"
              />
            </view>
          </view>
          <view class="ext-row" v-if="!staffDetail.ext_info || !staffDetail.ext_info.current_address">
            <text class="ext-label">现住址</text>
            <view class="ext-value-container">
              <text class="ext-value">未设置</text>
              <u-icon
                name="edit-pen"
                size="16"
                color="#1890ff"
                @click="showEditPopup('address')"
                class="edit-icon"
              />
            </view>
          </view>
          <view class="ext-row" v-if="staffDetail.ext_info.emergency_contact">
            <text class="ext-label">紧急联系人</text>
            <text class="ext-value">{{ staffDetail.ext_info.emergency_contact }}</text>
          </view>
          <view class="ext-row" v-if="staffDetail.ext_info.emergency_phone">
            <text class="ext-label">紧急联系电话</text>
            <text class="ext-value">{{ staffDetail.ext_info.emergency_phone }}</text>
          </view>
          <view class="ext-row" v-if="staffDetail.ext_info.skills">
            <text class="ext-label">技能</text>
            <text class="ext-value">{{ staffDetail.ext_info.skills }}</text>
          </view>
          <view class="ext-row" v-if="staffDetail.ext_info.work_experience">
            <text class="ext-label">工作经验</text>
            <text class="ext-value">{{ staffDetail.ext_info.work_experience }}</text>
          </view>
        </view>
      </view>

      <!-- 服务产品卡片 -->
      <view class="info-card products-card">
        <view class="card-header">
          <view class="header-icon products-icon">
            <u-icon name="grid" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">服务产品</text>
          <view class="edit-btn" @click="showProductEditor">
            <u-icon name="edit-pen" size="16" color="#1890ff"></u-icon>
            <text class="edit-text">编辑</text>
          </view>
        </view>
        <view class="products-content">
          <view v-if="staffProducts.length > 0" class="products-list">
            <view v-for="(product, index) in staffProducts" :key="index" class="product-tag">
              <text class="product-name">{{ product.name }}</text>
            </view>
          </view>
          <view v-else class="no-products">
            <text class="no-products-text">暂无绑定产品</text>
            <text class="add-products-hint">点击编辑按钮添加服务产品</text>
          </view>
        </view>
      </view>

      <!-- 微信信息卡片 -->
      <view class="info-card wechat-card" v-if="staffDetail.wechat_info">
        <view class="card-header">
          <view class="header-icon wechat-icon">
            <u-icon name="weixin-fill" size="20" color="#fff"></u-icon>
          </view>
          <text class="card-title">微信信息</text>
        </view>
        <view class="wechat-content">
          <view class="wechat-row">
            <text class="wechat-label">微信绑定</text>
            <text class="wechat-value">{{ staffDetail.wechat_info.is_bind_wx === '1' ? '已绑定' : '未绑定' }}</text>
          </view>
          <view class="wechat-row" v-if="staffDetail.wechat_info.is_bind_wx === '1' && staffDetail.wechat_info.wx_bind_time">
            <text class="wechat-label">绑定时间</text>
            <text class="wechat-value">{{ staffDetail.wechat_info.wx_bind_time }}</text>
          </view>
        </view>
      </view>

    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <u-icon name="error-circle" size="60" color="#ff6b6b"></u-icon>
      <text class="error-text">{{ error }}</text>
      <view class="retry-btn" @click="loadStaffDetail">
        <text>重新加载</text>
      </view>
    </view>

    <!-- 固定底部操作栏 -->
    <view class="fixed-bottom-actions" v-if="staffDetail">
      <view class="action-item" @click="editStaffInfo">
        <text class="action-text">编辑简历</text>
      </view>
      <view class="action-item" @click="handleCreditQuery">
        <text class="action-text">信用查询</text>
      </view>
      <view class="action-item primary-action" @click="makePhoneCall">
        <text class="action-text">联系电话</text>
      </view>
    </view>



    <!-- 底部空白区域 -->
    <view style="height: 120rpx"></view>

    <!-- 编辑基本信息弹窗 -->
    <u-popup
      :show="showEditInfoPopup"
      mode="center"
      @close="hideEditPopup"
      :round="16"
      :closeOnClickOverlay="true"
    >
      <view class="edit-info-popup">
        <view class="popup-header">
          <text class="popup-title">{{ editType === 'mobile' ? '编辑手机号' : '编辑现住址' }}</text>
          <u-icon name="close" size="20" color="#999" @click="hideEditPopup" class="close-icon" />
        </view>

        <view class="popup-content">
          <view class="form-item" v-if="editType === 'mobile'">
            <text class="form-label">手机号</text>
            <u-input
              v-model="editForm.mobile"
              placeholder="请输入手机号"
              maxlength="11"
              type="number"
              class="form-input"
            />
          </view>

          <view class="form-item" v-if="editType === 'address'">
            <text class="form-label">现住址</text>
            <u-input
              v-model="editForm.current_address"
              placeholder="请输入现住址"
              type="textarea"
              :autoHeight="true"
              class="form-input"
            />
          </view>
        </view>

        <view class="popup-footer">
          <view class="footer-btn cancel-btn" @click="hideEditPopup">
            <text class="btn-text">取消</text>
          </view>
          <view class="footer-btn confirm-btn" @click="saveEditInfo">
            <text class="btn-text">保存</text>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- 服务产品编辑组件 -->
    <StaffProductEditor
      :show.sync="productEditorShow"
      :staff-id="staffId"
      @save-success="onProductSaveSuccess"
      @close="productEditorShow = false"
    />

  </view>
</template>

<script>
import { getStaffDetail, getStaffProducts, updateStaffInfo } from '@/api/staff.js'
import StaffProductEditor from './components/StaffProductEditor.vue'

export default {
  name: 'StaffDetail',
  components: {
    StaffProductEditor
  },
  data() {
    return {
      // 状态栏高度
      StatusBar: 0,
      // 员工ID
      staffId: null,
      // 加载状态
      loading: false,
      // 错误信息
      error: null,
      // 员工详情数据
      staffDetail: null,
      // 员工产品数据
      staffProducts: [],
      // 产品编辑器显示状态
      productEditorShow: false,
      // 编辑弹窗显示状态
      showEditInfoPopup: false,
      // 编辑类型 ('mobile' | 'address')
      editType: '',
      // 编辑表单数据
      editForm: {
        mobile: '',
        current_address: ''
      }
    };
  },

  onLoad(options) {
    // 获取状态栏高度
    this.StatusBar = uni.getSystemInfoSync().statusBarHeight || 0;

    console.log('员工详情页面加载，接收到的参数:', JSON.stringify(options, null, 2));

    // 获取传递的员工ID
    if (options && options.id) {
      this.staffId = options.id;
      console.log('加载员工ID:', this.staffId, '类型:', typeof this.staffId);

      // 检查是否是问题ID
      if (this.staffId === '1' || this.staffId === 1) {
        console.error('检测到问题ID 1，页面来源参数:', options);
        console.error('当前页面栈:', getCurrentPages().map(page => page.route));
      }

      this.loadStaffDetail();
    } else {
      this.error = '缺少员工ID参数';
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },

    // 处理信用查询
    handleCreditQuery() {
      if (!this.staffDetail || !this.staffDetail.basic_info) {
        uni.showToast({
          title: '员工信息不完整',
          icon: 'none'
        });
        return;
      }

      const staffName = this.staffDetail.basic_info.real_name;
      const staffMobile = this.staffDetail.basic_info.mobile;
      const idCard = this.staffDetail.ext_info?.id_card;

      if (!staffName || !staffMobile || !idCard) {
        uni.showToast({
          title: '员工基本信息不完整，无法进行信用查询',
          icon: 'none'
        });
        return;
      }

      // 跳转到信用查询页面，传递员工信息
      const staffUuid = this.staffDetail.basic_info.uuid;
      const avatarUrl = this.staffDetail.basic_info.avatar || '';
      const staffType = 2; // service_staff表类型为2

      uni.navigateTo({
        url: `/pages-staff/credit-check?staff_uuid=${encodeURIComponent(staffUuid)}&staff_type=${staffType}&name=${encodeURIComponent(staffName)}&phone=${encodeURIComponent(staffMobile)}&idCard=${encodeURIComponent(idCard)}&avatar=${encodeURIComponent(avatarUrl)}`
      });
    },





    // 执行信用查询
    async performCreditQuery() {
      try {
        // 显示3秒加载进度
        uni.showLoading({
          title: '查询中...',
          mask: true
        });

        // 模拟3秒查询延迟
        await new Promise(resolve => setTimeout(resolve, 3000));

        uni.hideLoading();

        // 跳转到信用查询结果页面
        const staffName = this.staffDetail.basic_info.real_name || '未知员工';
        const staffMobile = this.staffDetail.basic_info.mobile || '';
        const idCard = this.staffDetail.ext_info?.id_card || this.generateRandomIdCard();
        const avatarUrl = this.staffDetail.basic_info.avatar || ''; // 获取头像URL
        const staffUuid = this.staffDetail.basic_info.uuid; // 获取员工UUID
        const staffType = 2; // service_staff表类型为2

        // 验证必要参数
        if (!staffUuid) {
          uni.showToast({
            title: '缺少员工UUID，无法进行信用查询',
            icon: 'none'
          });
          return;
        }

        console.log('跳转信用查询页面，参数:', {
          staff_uuid: staffUuid,
          staff_type: staffType,
          name: staffName,
          phone: staffMobile,
          idCard: idCard
        });

        uni.navigateTo({
          url: `/pages-staff/credit-check?staff_uuid=${encodeURIComponent(staffUuid)}&staff_type=${staffType}&name=${encodeURIComponent(staffName)}&phone=${encodeURIComponent(staffMobile)}&idCard=${encodeURIComponent(idCard)}&avatar=${encodeURIComponent(avatarUrl)}`,
          success: () => {
            console.log('跳转到信用查询页面成功');
          },
          fail: (err) => {
            console.error('跳转到信用查询页面失败:', err);
            uni.showToast({
              title: '跳转失败，请稍后重试',
              icon: 'none'
            });
          }
        });

      } catch (error) {
        console.error('信用查询失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '查询失败，请稍后重试',
          icon: 'none'
        });
      }
    },

    // 生成随机身份证号（用于演示）
    generateRandomIdCard() {
      // 生成18位身份证号格式：前6位地区码 + 8位生日 + 3位顺序码 + 1位校验码
      const areaCodes = ['110101', '310101', '440101', '500101', '120101']; // 一些常见地区码
      const areaCode = areaCodes[Math.floor(Math.random() * areaCodes.length)];

      // 生成生日（1970-2000年）
      const year = 1970 + Math.floor(Math.random() * 30);
      const month = String(1 + Math.floor(Math.random() * 12)).padStart(2, '0');
      const day = String(1 + Math.floor(Math.random() * 28)).padStart(2, '0');
      const birthday = `${year}${month}${day}`;

      // 生成顺序码（3位）
      const sequence = String(Math.floor(Math.random() * 1000)).padStart(3, '0');

      // 生成校验码（简化处理，随机生成）
      const checkCodes = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'X'];
      const checkCode = checkCodes[Math.floor(Math.random() * checkCodes.length)];

      return `${areaCode}${birthday}${sequence}${checkCode}`;
    },

    // 生成随机身份证号（用于演示）
    generateRandomIdCard() {
      // 生成18位身份证号格式：前6位地区码 + 8位生日 + 3位顺序码 + 1位校验码
      const areaCodes = ['110101', '310101', '440101', '500101', '120101']; // 一些常见地区码
      const areaCode = areaCodes[Math.floor(Math.random() * areaCodes.length)];

      // 生成生日（1970-2000年）
      const year = 1970 + Math.floor(Math.random() * 30);
      const month = String(1 + Math.floor(Math.random() * 12)).padStart(2, '0');
      const day = String(1 + Math.floor(Math.random() * 28)).padStart(2, '0');
      const birthday = `${year}${month}${day}`;

      // 生成顺序码（3位）
      const sequence = String(Math.floor(Math.random() * 1000)).padStart(3, '0');

      // 生成校验码（简化处理，随机生成）
      const checkCodes = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'X'];
      const checkCode = checkCodes[Math.floor(Math.random() * checkCodes.length)];

      return `${areaCode}${birthday}${sequence}${checkCode}`;
    },

    // 编辑员工信息
    editStaffInfo() {
      if (!this.staffDetail) {
        uni.showToast({
          title: '员工信息不完整',
          icon: 'none'
        });
        return;
      }

      uni.navigateTo({
        url: `/pages-staff/edit?id=${this.staffId}`,
        fail: (err) => {
          console.error('跳转到编辑页面失败:', err);
          uni.showToast({
            title: '跳转失败，请稍后重试',
            icon: 'none'
          });
        }
      });
    },

    // 拨打电话
    makePhoneCall() {
      if (!this.staffDetail || !this.staffDetail.basic_info.mobile) {
        uni.showToast({
          title: '员工手机号不存在',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: this.staffDetail.basic_info.mobile,
        fail: (err) => {
          console.error('拨打电话失败:', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },

    // 加载员工详情
    async loadStaffDetail() {
      if (!this.staffId) {
        this.error = '缺少员工ID参数';
        return;
      }

      try {
        this.loading = true;
        this.error = null;

        console.log('开始加载员工详情，ID:', this.staffId);

        // 调用API获取员工详情
        const response = await getStaffDetail(this.staffId);

        this.staffDetail = response;
        console.log('员工详情加载成功:', this.staffDetail);

        // 加载员工产品信息
        await this.loadStaffProducts();
 
      } catch (error) {
        console.error('加载员工详情失败:', error);
        this.error = error.message || '加载员工详情失败，请重试';

        uni.showToast({
          title: this.error,
          icon: 'none',
          duration: 2000
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取员工状态文本
    getStatusText(status) {
      const statusMap = {
        '1': '在职',
        '2': '离职',
        '3': '休假',
        '4': '培训',
        '5': '试用',
        '0': '禁用'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取工作类型文本
    getWorkTypeText(workType) {
      const workTypeMap = {
        '1': '保姆',
        '2': '保洁',
        '3': '月嫂',
        '4': '育儿嫂',
        '5': '护工'
      };
      return workTypeMap[workType] || '未知类型';
    },

    // 获取性别文本
    getSexText(sex) {
      const sexMap = {
        '1': '男',
        '2': '女'
      };
      return sexMap[sex] || '未设置';
    },

    // 获取婚姻状况文本
    getMaritalStatusText(status) {
      const statusMap = {
        '1': '未婚',
        '2': '已婚',
        '3': '离异',
        '4': '丧偶'
      };
      return statusMap[status] || '未设置';
    },

    // 加载员工产品信息
    async loadStaffProducts() {
      if (!this.staffId) return;

      try {
        const response = await getStaffProducts(this.staffId);
        this.staffProducts = response.products || [];
        console.log('员工产品加载成功:', this.staffProducts);
      } catch (error) {
        console.error('加载员工产品失败:', error);
        // 不显示错误提示，静默失败
        this.staffProducts = [];
      }
    },

    // 显示产品编辑器
    showProductEditor() {
      this.productEditorShow = true;
    },

    // 产品保存成功回调
    async onProductSaveSuccess(data) {
      console.log('产品保存成功:', data);

      // 重新加载员工产品信息
      await this.loadStaffProducts();

      uni.showToast({
        title: '产品更新成功',
        icon: 'success'
      });
    },

    // 显示编辑弹窗
    showEditPopup(type) {
      this.editType = type;

      // 根据编辑类型初始化表单数据
      if (type === 'mobile') {
        this.editForm.mobile = this.staffDetail.basic_info.mobile || '';
        this.editForm.current_address = ''; // 清空另一个字段
      } else if (type === 'address') {
        this.editForm.current_address = this.staffDetail.ext_info?.current_address || '';
        this.editForm.mobile = ''; // 清空另一个字段
      }

      this.showEditInfoPopup = true;
    },

    // 隐藏编辑弹窗
    hideEditPopup() {
      this.showEditInfoPopup = false;
      // 清空表单数据
      this.editForm.mobile = '';
      this.editForm.current_address = '';
    },

    // 保存编辑信息
    async saveEditInfo() {
      let updateData = {};
      let successMessage = '';

      // 根据编辑类型进行验证和数据准备
      if (this.editType === 'mobile') {
        // 验证手机号
        if (!this.editForm.mobile) {
          uni.showToast({
            title: '请输入手机号',
            icon: 'none'
          });
          return;
        }

        if (!/^1[3-9]\d{9}$/.test(this.editForm.mobile)) {
          uni.showToast({
            title: '请输入正确的手机号格式',
            icon: 'none'
          });
          return;
        }

        updateData.mobile = this.editForm.mobile;
        successMessage = '手机号更新成功';

      } else if (this.editType === 'address') {
        // 验证现住址
        if (!this.editForm.current_address.trim()) {
          uni.showToast({
            title: '请输入现住址',
            icon: 'none'
          });
          return;
        }

        updateData.current_address = this.editForm.current_address.trim();
        successMessage = '现住址更新成功';
      }

      try {
        uni.showLoading({
          title: '保存中...'
        });

        // 调用API更新员工信息
        await updateStaffInfo(this.staffId, updateData);

        // 更新本地数据
        if (this.editType === 'mobile') {
          this.staffDetail.basic_info.mobile = this.editForm.mobile;
        } else if (this.editType === 'address') {
          if (!this.staffDetail.ext_info) {
            this.staffDetail.ext_info = {};
          }
          this.staffDetail.ext_info.current_address = this.editForm.current_address.trim();
        }

        // 隐藏弹窗
        this.hideEditPopup();

        uni.hideLoading();
        uni.showToast({
          title: successMessage,
          icon: 'success'
        });

      } catch (error) {
        uni.hideLoading();
        console.error('更新员工信息失败:', error);

        // 详细分析错误信息
        let errorMessage = '更新失败，请重试';
        if (error.data && error.data.detail) {
          console.error('422错误详情:', error.data.detail);
          if (Array.isArray(error.data.detail)) {
            errorMessage = error.data.detail.map(item => item.msg || item.message || '未知错误').join(', ');
          } else if (typeof error.data.detail === 'string') {
            errorMessage = error.data.detail;
          }
        } else if (error.message) {
          errorMessage = error.message;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.staff-detail-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f7fb 0%, #ffffff 100%);

  // 现代化头部样式
  .detail-header {
    position: relative;
    padding-top: var(--status-bar-height);

    .header-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      z-index: 0;
    }

    .header-content {
      position: relative;
      z-index: 1;

      // 顶部导航
      .top-nav {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 30rpx;
        height: 88rpx;

        .nav-left, .nav-right {
          width: 80rpx;
          display: flex;
          align-items: center;
        }

        .back-btn {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(10rpx);
        }

        .nav-center {
          flex: 1;
          text-align: center;

          .nav-title {
            font-size: 36rpx;
            font-weight: 600;
            color: #fff;
          }
        }
      }

      // 员工状态卡片
      .staff-status-card {
        margin: 30rpx;
        padding: 30rpx;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20rpx;
        backdrop-filter: blur(20rpx);
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

        .status-main {
          display: flex;
          align-items: center;

          .staff-avatar {
            margin-right: 30rpx;

            .avatar-img {
              width: 120rpx;
              height: 120rpx;
              border-radius: 50%;
              border: 4rpx solid rgba(255, 255, 255, 0.8);
            }

            .avatar-placeholder {
              width: 120rpx;
              height: 120rpx;
              border-radius: 50%;
              background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
              display: flex;
              align-items: center;
              justify-content: center;
              border: 4rpx solid rgba(255, 255, 255, 0.8);
            }
          }

          .staff-info {
            flex: 1;

            .staff-name {
              font-size: 36rpx;
              font-weight: 600;
              color: #2c3e50;
              margin-bottom: 8rpx;
              display: block;
            }

            .staff-mobile {
              font-size: 28rpx;
              color: #7f8c8d;
              margin-bottom: 16rpx;
              display: block;
            }
          }

          .staff-actions {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: center;

            .credit-query-btn {
              display: flex;
              align-items: center;
              gap: 8rpx;
              padding: 16rpx 20rpx;
              background: linear-gradient(135deg, #007aff 0%, #0066cc 100%);
              border-radius: 50rpx;
              box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
              transition: all 0.3s ease;

              &:active {
                transform: translateY(2rpx);
                box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.4);
              }

              .btn-icon {
                width: 32rpx;
                height: 32rpx;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.2);
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .btn-text {
                font-size: 26rpx;
                color: #fff;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }

  // 加载状态
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;

    .loading-text {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #7f8c8d;
    }
  }

  // 错误状态
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 30rpx;

    .error-text {
      margin: 30rpx 0;
      font-size: 28rpx;
      color: #e74c3c;
      text-align: center;
      line-height: 1.5;
    }

    .retry-btn {
      padding: 20rpx 40rpx;
      background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
      color: #fff;
      border-radius: 50rpx;
      font-size: 28rpx;
      font-weight: 500;
    }
  }

  // 详情内容区域
  .detail-content {
    padding: 0 30rpx 30rpx;
    margin-top: -40rpx;
    position: relative;
    z-index: 2;
  }

  // 信息卡片通用样式
  .info-card {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .card-header {
      display: flex;
      align-items: center;
      padding: 30rpx;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1rpx solid #e9ecef;

      .header-icon {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;

        &.basic-icon {
          background: linear-gradient(135deg, #09be89 0%, #00a862 100%);
        }

        &.company-icon {
          background: linear-gradient(135deg, #007aff 0%, #0066cc 100%);
        }

        &.stats-icon {
          background: linear-gradient(135deg, #ff801b 0%, #ff403f 100%);
        }

        &.ext-icon {
          background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
        }

        &.wechat-icon {
          background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
        }

        &.products-icon {
          background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        }
      }

      .card-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #2c3e50;
        flex: 1;
      }

      .edit-btn {
        display: flex;
        align-items: center;
        gap: 8rpx;
        padding: 8rpx 16rpx;
        background: rgba(24, 144, 255, 0.1);
        border-radius: 20rpx;
        transition: all 0.3s ease;

        &:active {
          background: rgba(24, 144, 255, 0.2);
        }

        .edit-text {
          font-size: 24rpx;
          color: #1890ff;
          font-weight: 500;
        }
      }
    }
  }

  // 基本信息卡片
  .basic-card {
    .basic-content {
      padding: 30rpx;

      .basic-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f8f9fa;

        &:last-child {
          border-bottom: none;
        }

        .basic-label {
          font-size: 28rpx;
          color: #7f8c8d;
          font-weight: 500;
        }

        .basic-value-container {
          display: flex;
          align-items: center;
          gap: 16rpx;
          flex: 1;
          justify-content: flex-end;

          .basic-value {
            font-size: 28rpx;
            color: #2c3e50;
            font-weight: 600;
          }

          .edit-icon {
            padding: 8rpx;
            border-radius: 50%;
            background: rgba(24, 144, 255, 0.1);
            transition: all 0.3s ease;

            &:active {
              background: rgba(24, 144, 255, 0.2);
            }
          }
        }

        .basic-value {
          font-size: 28rpx;
          color: #2c3e50;
          font-weight: 600;
        }
      }
    }
  }

  // 公司门店卡片
  .company-card {
    .company-content {
      padding: 30rpx;

      .company-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f8f9fa;

        &:last-child {
          border-bottom: none;
        }

        .company-label {
          font-size: 28rpx;
          color: #7f8c8d;
          font-weight: 500;
        }

        .company-value {
          font-size: 28rpx;
          color: #2c3e50;
          font-weight: 600;
        }
      }
    }
  }

  // 工作统计卡片
  .stats-card {
    .stats-content {
      padding: 30rpx;

      .stats-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f8f9fa;

        &:last-child {
          border-bottom: none;
        }

        .stats-label {
          font-size: 28rpx;
          color: #7f8c8d;
          font-weight: 500;
        }

        .stats-value {
          font-size: 28rpx;
          color: #2c3e50;
          font-weight: 600;
        }
      }
    }
  }

  // 扩展信息卡片
  .ext-card {
    .ext-content {
      padding: 30rpx;

      .ext-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f8f9fa;

        &:last-child {
          border-bottom: none;
        }

        .ext-label {
          font-size: 28rpx;
          color: #7f8c8d;
          font-weight: 500;
        }

        .ext-value-container {
          display: flex;
          align-items: center;
          gap: 16rpx;
          flex: 1;
          justify-content: flex-end;

          .ext-value {
            font-size: 28rpx;
            color: #2c3e50;
            font-weight: 600;
            text-align: right;
            flex: 1;
          }

          .edit-icon {
            padding: 8rpx;
            border-radius: 50%;
            background: rgba(24, 144, 255, 0.1);
            transition: all 0.3s ease;

            &:active {
              background: rgba(24, 144, 255, 0.2);
            }
          }
        }

        .ext-value {
          font-size: 28rpx;
          color: #2c3e50;
          font-weight: 600;
          text-align: right;
          flex: 1;
          margin-left: 20rpx;
        }
      }
    }
  }

  // 微信信息卡片
  .wechat-card {
    .wechat-content {
      padding: 30rpx;

      .wechat-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f8f9fa;

        &:last-child {
          border-bottom: none;
        }

        .wechat-label {
          font-size: 28rpx;
          color: #7f8c8d;
          font-weight: 500;
        }

        .wechat-value {
          font-size: 28rpx;
          color: #2c3e50;
          font-weight: 600;
        }
      }
    }
  }

  // 服务产品卡片
  .products-card {
    .products-content {
      padding: 30rpx;

      .products-list {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .product-tag {
          padding: 12rpx 20rpx;
          background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
          border-radius: 20rpx;
          box-shadow: 0 2rpx 8rpx rgba(142, 68, 173, 0.2);

          .product-name {
            font-size: 24rpx;
            color: #fff;
            font-weight: 500;
          }
        }
      }

      .no-products {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 60rpx 0;

        .no-products-text {
          font-size: 28rpx;
          color: #7f8c8d;
          margin-bottom: 12rpx;
        }

        .add-products-hint {
          font-size: 24rpx;
          color: #bdc3c7;
        }
      }
    }
  }

  // 编辑弹窗样式
  .edit-info-popup {
    width: 600rpx;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;

    .popup-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1rpx solid #e9ecef;

      .popup-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #2c3e50;
      }

      .close-icon {
        padding: 8rpx;
        border-radius: 50%;
        background: rgba(153, 153, 153, 0.1);
        transition: all 0.3s ease;

        &:active {
          background: rgba(153, 153, 153, 0.2);
        }
      }
    }

    .popup-content {
      padding: 30rpx;

      .form-item {
        margin-bottom: 30rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .form-label {
          display: block;
          font-size: 28rpx;
          color: #7f8c8d;
          font-weight: 500;
          margin-bottom: 16rpx;
        }

        .form-input {
          width: 100%;
          padding: 20rpx;
          border: 1rpx solid #e9ecef;
          border-radius: 12rpx;
          font-size: 28rpx;
          color: #2c3e50;
          background: #f8f9fa;
          transition: all 0.3s ease;

          &:focus {
            border-color: #1890ff;
            background: #fff;
          }
        }
      }
    }

    .popup-footer {
      display: flex;
      border-top: 1rpx solid #e9ecef;

      .footer-btn {
        flex: 1;
        padding: 30rpx;
        text-align: center;
        transition: all 0.3s ease;

        .btn-text {
          font-size: 28rpx;
          font-weight: 500;
        }

        &.cancel-btn {
          background: #f8f9fa;
          border-right: 1rpx solid #e9ecef;

          .btn-text {
            color: #7f8c8d;
          }

          &:active {
            background: #e9ecef;
          }
        }

        &.confirm-btn {
          background: #1890ff;

          .btn-text {
            color: #fff;
          }

          &:active {
            background: #0066cc;
          }
        }
      }
    }
  }

  // 固定底部操作栏
  .fixed-bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 20rpx 30rpx;
    padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
    border-top: 1rpx solid #e0e0e0;
    display: flex;
    gap: 20rpx;
    z-index: 100;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

    .action-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 24rpx 16rpx;
      border-radius: 12rpx;
      background: #f8f9fa;
      transition: all 0.3s ease;

      &:active {
        transform: translateY(2rpx);
        background: #e9ecef;
      }

      &.primary-action {
        background: #fdd118;

        &:active {
          background: #e6c015;
        }

        .action-text {
          color: #333;
          font-weight: 600;
        }
      }

      .action-text {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }
    }
  }


}
</style>

