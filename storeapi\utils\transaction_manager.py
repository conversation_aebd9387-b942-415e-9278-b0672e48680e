"""
事务管理工具类
用于确保财务操作的数据完整性和审计追踪
"""
import functools
from typing import Callable, Any, Dict, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from datetime import datetime
from utils.log_util import logger
from exceptions.exception import BusinessException, DatabaseException
import uuid


class TransactionManager:
    """事务管理器"""
    
    @staticmethod
    async def execute_financial_transaction(
        db: AsyncSession,
        operation_func: Callable,
        operation_name: str,
        *args,
        **kwargs
    ) -> Any:
        """
        执行财务事务操作
        
        Args:
            db: 数据库会话
            operation_func: 要执行的操作函数
            operation_name: 操作名称（用于日志）
            *args: 操作函数的位置参数
            **kwargs: 操作函数的关键字参数
            
        Returns:
            操作结果
        """
        transaction_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            logger.info(f"🔄 开始执行财务事务: {operation_name} (ID: {transaction_id})")
            
            # 执行业务操作
            result = await operation_func(db, *args, **kwargs)
            
            # 提交事务
            await db.commit()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"✅ 财务事务执行成功: {operation_name} (ID: {transaction_id}, 耗时: {duration:.2f}s)")
            
            return result
            
        except BusinessException as e:
            await db.rollback()
            logger.error(f"❌ 财务事务业务异常: {operation_name} (ID: {transaction_id}) - {str(e)}")
            raise e
        except Exception as e:
            await db.rollback()
            logger.error(f"❌ 财务事务系统异常: {operation_name} (ID: {transaction_id}) - {str(e)}")
            raise BusinessException(message=f"{operation_name}失败: {str(e)}")
    
    @staticmethod
    async def validate_balance_consistency(
        db: AsyncSession,
        company_uuid: str,
        expected_available_balance: Optional[float] = None,
        expected_frozen: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        验证余额一致性

        Args:
            db: 数据库会话
            company_uuid: 公司UUID
            expected_available_balance: 期望的可用余额（可选）
            expected_frozen: 期望的冻结金额（可选）

        Returns:
            验证结果
        """
        try:
            # 查询当前余额状态
            balance_query = text("""
                SELECT balance, frozen_amount
                FROM company
                WHERE id = :company_uuid AND is_delete = '0'
            """)
            result = await db.execute(balance_query, {"company_uuid": company_uuid})
            row = result.fetchone()

            if not row:
                raise BusinessException(message="公司信息不存在")

            available_balance = float(row.balance or 0)      # balance就是可用余额
            frozen_amount = float(row.frozen_amount or 0)    # 冻结金额
            total_balance = available_balance + frozen_amount # 账户总余额

            # 验证可用余额不能为负数
            if available_balance < 0:
                raise BusinessException(message=f"可用余额异常: {available_balance}")

            # 验证冻结金额不能为负数
            if frozen_amount < 0:
                raise BusinessException(message=f"冻结金额异常: {frozen_amount}")

            # 验证期望值（如果提供）
            validation_result = {
                "company_uuid": company_uuid,
                "available_balance": available_balance,  # 可用余额（balance字段）
                "frozen_amount": frozen_amount,          # 冻结金额
                "total_balance": total_balance,          # 账户总余额
                "validation_time": datetime.now().isoformat(),
                "is_valid": True,
                "messages": []
            }

            if expected_available_balance is not None and abs(available_balance - expected_available_balance) > 0.01:
                validation_result["is_valid"] = False
                validation_result["messages"].append(
                    f"可用余额不匹配: 期望 {expected_available_balance}, 实际 {available_balance}"
                )

            if expected_frozen is not None and abs(frozen_amount - expected_frozen) > 0.01:
                validation_result["is_valid"] = False
                validation_result["messages"].append(
                    f"冻结金额不匹配: 期望 {expected_frozen}, 实际 {frozen_amount}"
                )

            if not validation_result["is_valid"]:
                logger.warning(f"余额一致性验证失败: {validation_result['messages']}")

            return validation_result

        except Exception as e:
            logger.error(f"余额一致性验证失败: {str(e)}")
            raise DatabaseException(message=f"余额验证失败: {str(e)}")
    
    @staticmethod
    async def create_audit_log(
        db: AsyncSession,
        operation_type: str,
        operation_name: str,
        company_uuid: str,
        operator_id: str,
        operator_name: str,
        details: Dict[str, Any],
        result: str = "SUCCESS"
    ) -> None:
        """
        创建审计日志
        
        Args:
            db: 数据库会话
            operation_type: 操作类型
            operation_name: 操作名称
            company_uuid: 公司UUID
            operator_id: 操作人ID
            operator_name: 操作人姓名
            details: 操作详情
            result: 操作结果
        """
        try:
            audit_log_sql = text("""
                INSERT INTO financial_audit_log (
                    id, operation_type, operation_name, company_uuid,
                    operator_id, operator_name, operation_details,
                    operation_result, operation_time, created_at
                ) VALUES (
                    :id, :operation_type, :operation_name, :company_uuid,
                    :operator_id, :operator_name, :operation_details,
                    :operation_result, NOW(), NOW()
                )
            """)
            
            await db.execute(audit_log_sql, {
                "id": str(uuid.uuid4()),
                "operation_type": operation_type,
                "operation_name": operation_name,
                "company_uuid": company_uuid,
                "operator_id": operator_id,
                "operator_name": operator_name,
                "operation_details": str(details),
                "operation_result": result
            })
            
        except Exception as e:
            # 审计日志失败不应该影响主业务流程
            logger.error(f"创建审计日志失败: {str(e)}")


def financial_transaction(operation_name: str):
    """
    财务事务装饰器
    
    Args:
        operation_name: 操作名称
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 查找AsyncSession参数
            db_session = None
            for arg in args:
                if isinstance(arg, AsyncSession):
                    db_session = arg
                    break
            
            if not db_session:
                for value in kwargs.values():
                    if isinstance(value, AsyncSession):
                        db_session = value
                        break
            
            if not db_session:
                raise BusinessException(message="未找到数据库会话")
            
            return await TransactionManager.execute_financial_transaction(
                db_session, func, operation_name, *args, **kwargs
            )
        
        return wrapper
    return decorator


class FinancialOperationValidator:
    """财务操作验证器"""
    
    @staticmethod
    def validate_amount(amount: float, operation_name: str) -> None:
        """验证金额有效性"""
        if not isinstance(amount, (int, float)):
            raise BusinessException(message=f"{operation_name}: 金额必须为数字")
        
        if amount < 0:
            raise BusinessException(message=f"{operation_name}: 金额不能为负数")
        
        if amount > 999999999.99:
            raise BusinessException(message=f"{operation_name}: 金额超出限制")
    
    @staticmethod
    def validate_withdrawal_amount(amount: float, available_balance: float) -> None:
        """验证提现金额"""
        FinancialOperationValidator.validate_amount(amount, "提现申请")

        if amount <= 0:
            raise BusinessException(message="提现金额必须大于0")

        if amount < 100:
            raise BusinessException(message="最低提现金额为100元")

        if amount > available_balance:
            raise BusinessException(
                message=f"提现金额超出可用余额，当前可用余额: {available_balance:.2f}元，申请金额: {amount:.2f}元"
            )
    
    @staticmethod
    def validate_company_status(company_status: str) -> None:
        """验证公司状态"""
        if company_status != '1':
            raise BusinessException(message="公司状态异常，无法进行财务操作")


# 使用示例：
# 
# 1. 装饰器使用：
# @financial_transaction("创建提现申请")
# async def create_withdrawal_application(db: AsyncSession, ...):
#     # 业务逻辑
#     pass
#
# 2. 手动事务管理：
# result = await TransactionManager.execute_financial_transaction(
#     db, some_operation, "操作名称", arg1, arg2
# )
#
# 3. 余额验证：
# validation_result = await TransactionManager.validate_balance_consistency(
#     db, company_uuid, expected_balance=1000.0
# )
