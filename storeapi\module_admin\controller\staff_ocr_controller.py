"""
员工端OCR识别控制器
"""
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from config.get_db import get_db
from utils.response_util import ResponseUtil
from utils.log_util import logger
from utils.baidu_ocr_util import baidu_ocr_util
from exceptions.exception import ValidationException
from module_admin.service.service_staff_login_service import ServiceStaffLoginService

# 请求模型
class ImageUrlRequest(BaseModel):
    imageUrl: str

class TempFileRequest(BaseModel):
    temp_file_path: str

# 创建路由器
staff_ocr_controller = APIRouter(prefix='/api/v1/staff/ocr', tags=['员工端OCR识别'])


@staff_ocr_controller.post('/idcard/front', summary="员工身份证正面OCR识别")
async def recognize_id_card_front(
    request: ImageUrlRequest,
    current_staff=Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工身份证正面OCR识别接口

    通过图片URL进行身份证正面OCR识别

    Args:
        request: 包含图片URL的请求对象
        current_staff: 当前登录员工信息
        query_db: 数据库会话

    Returns:
        识别结果
    """
    try:
        logger.info(f"员工 {getattr(current_staff, 'mobile', 'unknown')} 请求身份证正面OCR识别")

        # 验证图片URL
        if not request.imageUrl:
            raise ValidationException(message="请提供图片URL")

        # 调用百度OCR识别（通过URL）
        result = await baidu_ocr_util.recognize_id_card_front_by_url(request.imageUrl)

        logger.info("员工身份证正面OCR识别成功")
        return ResponseUtil.success(data=result, msg="身份证正面识别成功")

    except ValidationException as e:
        logger.warning(f"员工身份证正面OCR识别参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"员工身份证正面OCR识别失败: {str(e)}")
        return ResponseUtil.error(msg="身份证识别失败，请手动填写")


@staff_ocr_controller.post('/idcard/back', summary="员工身份证背面OCR识别")
async def recognize_id_card_back(
    request: ImageUrlRequest,
    current_staff=Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工身份证背面OCR识别接口

    通过图片URL进行身份证背面OCR识别

    Args:
        request: 包含图片URL的请求对象
        current_staff: 当前登录员工信息
        query_db: 数据库会话

    Returns:
        识别结果
    """
    try:
        logger.info(f"员工 {getattr(current_staff, 'mobile', 'unknown')} 请求身份证背面OCR识别")

        # 验证图片URL
        if not request.imageUrl:
            raise ValidationException(message="请提供图片URL")

        # 调用百度OCR识别（通过URL）
        result = await baidu_ocr_util.recognize_id_card_back_by_url(request.imageUrl)

        logger.info("员工身份证背面OCR识别成功")
        return ResponseUtil.success(data=result, msg="身份证背面识别成功")

    except ValidationException as e:
        logger.warning(f"员工身份证背面OCR识别参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"员工身份证背面OCR识别失败: {str(e)}")
        return ResponseUtil.error(msg="身份证识别失败，请手动填写")


@staff_ocr_controller.post('/bankcard', summary="员工银行卡OCR识别")
async def recognize_bank_card(
    request: ImageUrlRequest,
    current_staff=Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工银行卡OCR识别接口

    通过图片URL进行银行卡OCR识别

    Args:
        request: 包含图片URL的请求对象
        current_staff: 当前登录员工信息
        query_db: 数据库会话

    Returns:
        识别结果
    """
    try:
        logger.info(f"员工 {getattr(current_staff, 'mobile', 'unknown')} 请求银行卡OCR识别")

        # 验证图片URL
        if not request.imageUrl:
            raise ValidationException(message="请提供图片URL")

        # 调用百度OCR识别（通过URL）
        result = await baidu_ocr_util.recognize_bank_card_by_url(request.imageUrl)

        logger.info("员工银行卡OCR识别成功")
        return ResponseUtil.success(data=result, msg="银行卡识别成功")

    except ValidationException as e:
        logger.warning(f"员工银行卡OCR识别参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"员工银行卡OCR识别失败: {str(e)}")
        return ResponseUtil.error(msg="银行卡识别失败，请手动填写")


# 临时文件识别接口（用于开户申请页面）
@staff_ocr_controller.post('/idcard/front-temp', summary="员工身份证正面OCR识别（临时文件）")
async def recognize_id_card_front_from_temp(
    request: TempFileRequest,
    current_staff=Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工身份证正面OCR识别接口（从临时文件）

    通过临时文件路径进行身份证正面OCR识别

    Args:
        request: 包含临时文件路径的请求对象
        current_staff: 当前登录员工信息
        query_db: 数据库会话

    Returns:
        识别结果
    """
    try:
        logger.info(f"员工 {getattr(current_staff, 'mobile', 'unknown')} 请求身份证正面OCR识别（临时文件）")

        # 验证临时文件路径
        if not request.temp_file_path:
            raise ValidationException(message="请提供临时文件路径")

        # 调用百度OCR识别（通过临时文件）
        result = await baidu_ocr_util.recognize_id_card_front_by_temp_file(request.temp_file_path)

        logger.info("员工身份证正面OCR识别成功（临时文件）")
        return ResponseUtil.success(data=result, msg="身份证正面识别成功")

    except ValidationException as e:
        logger.warning(f"员工身份证正面OCR识别参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"员工身份证正面OCR识别失败: {str(e)}")
        return ResponseUtil.error(msg="身份证识别失败，请手动填写")


@staff_ocr_controller.post('/idcard/back-temp', summary="员工身份证背面OCR识别（临时文件）")
async def recognize_id_card_back_from_temp(
    request: TempFileRequest,
    current_staff=Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工身份证背面OCR识别接口（从临时文件）

    通过临时文件路径进行身份证背面OCR识别

    Args:
        request: 包含临时文件路径的请求对象
        current_staff: 当前登录员工信息
        query_db: 数据库会话

    Returns:
        识别结果
    """
    try:
        logger.info(f"员工 {getattr(current_staff, 'mobile', 'unknown')} 请求身份证背面OCR识别（临时文件）")

        # 验证临时文件路径
        if not request.temp_file_path:
            raise ValidationException(message="请提供临时文件路径")

        # 调用百度OCR识别（通过临时文件）
        result = await baidu_ocr_util.recognize_id_card_back_by_temp_file(request.temp_file_path)

        logger.info("员工身份证背面OCR识别成功（临时文件）")
        return ResponseUtil.success(data=result, msg="身份证背面识别成功")

    except ValidationException as e:
        logger.warning(f"员工身份证背面OCR识别参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"员工身份证背面OCR识别失败: {str(e)}")
        return ResponseUtil.error(msg="身份证识别失败，请手动填写")


@staff_ocr_controller.post('/bankcard-temp', summary="员工银行卡OCR识别（临时文件）")
async def recognize_bank_card_from_temp(
    request: TempFileRequest,
    current_staff=Depends(ServiceStaffLoginService.get_current_staff),
    query_db: AsyncSession = Depends(get_db)
):
    """员工银行卡OCR识别接口（从临时文件）

    通过临时文件路径进行银行卡OCR识别

    Args:
        request: 包含临时文件路径的请求对象
        current_staff: 当前登录员工信息
        query_db: 数据库会话

    Returns:
        识别结果
    """
    try:
        logger.info(f"员工 {getattr(current_staff, 'mobile', 'unknown')} 请求银行卡OCR识别（临时文件）")

        # 验证临时文件路径
        if not request.temp_file_path:
            raise ValidationException(message="请提供临时文件路径")

        # 调用百度OCR识别（通过临时文件）
        result = await baidu_ocr_util.recognize_bank_card_by_temp_file(request.temp_file_path)

        logger.info("员工银行卡OCR识别成功（临时文件）")
        return ResponseUtil.success(data=result, msg="银行卡识别成功")

    except ValidationException as e:
        logger.warning(f"员工银行卡OCR识别参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"员工银行卡OCR识别失败: {str(e)}")
        return ResponseUtil.error(msg="银行卡识别失败，请手动填写")
