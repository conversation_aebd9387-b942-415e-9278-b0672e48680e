#!/usr/bin/env python3
"""
数据一致性检查脚本

检查service_product表中的数据一致性：
1. 员工、产品、公司UUID的一致性
2. 跨公司产品关联问题
3. 无效产品关联问题
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy import text
from config.database import DATABASE_URL
from utils.log_util import logger


class DataConsistencyChecker:
    """数据一致性检查器"""
    
    def __init__(self):
        self.engine = create_async_engine(DATABASE_URL, echo=False)
    
    async def check_cross_company_products(self) -> dict:
        """检查跨公司产品关联问题"""
        async with self.engine.begin() as conn:
            sql = """
                SELECT 
                    sp.id,
                    sp.staff_id,
                    sp.productid,
                    sp.store_uuid,
                    sp.company_uuid as sp_company,
                    ss.real_name,
                    ss.company_id as staff_company,
                    p.product_name,
                    p.company_uuid as product_company
                FROM service_product sp
                LEFT JOIN service_staff ss ON sp.staff_id = ss.id
                LEFT JOIN product p ON sp.productid = p.id
                WHERE sp.company_uuid != p.company_uuid
                OR sp.company_uuid != ss.company_id
                ORDER BY sp.id
                LIMIT 100
            """
            result = await conn.execute(text(sql))
            rows = result.fetchall()
            
            issues = []
            for row in rows:
                issue = {
                    "service_product_id": row.id,
                    "staff_id": row.staff_id,
                    "staff_name": row.real_name,
                    "product_id": row.productid,
                    "product_name": row.product_name,
                    "sp_company": row.sp_company,
                    "staff_company": row.staff_company,
                    "product_company": row.product_company,
                    "issue_type": []
                }
                
                if row.sp_company != row.product_company:
                    issue["issue_type"].append("产品公司不匹配")
                if row.sp_company != row.staff_company:
                    issue["issue_type"].append("员工公司不匹配")
                
                issues.append(issue)
            
            return {
                "total_issues": len(issues),
                "issues": issues
            }
    
    async def check_invalid_products(self) -> dict:
        """检查无效产品关联"""
        async with self.engine.begin() as conn:
            sql = """
                SELECT 
                    sp.id,
                    sp.staff_id,
                    sp.productid,
                    sp.store_uuid,
                    sp.company_uuid,
                    ss.real_name,
                    p.product_name,
                    p.product_status,
                    p.is_delete
                FROM service_product sp
                LEFT JOIN service_staff ss ON sp.staff_id = ss.id
                LEFT JOIN product p ON sp.productid = p.id
                WHERE p.id IS NULL 
                OR p.product_status != 1 
                OR p.is_delete = 1
                ORDER BY sp.id
                LIMIT 100
            """
            result = await conn.execute(text(sql))
            rows = result.fetchall()
            
            issues = []
            for row in rows:
                issue = {
                    "service_product_id": row.id,
                    "staff_id": row.staff_id,
                    "staff_name": row.real_name,
                    "product_id": row.productid,
                    "product_name": row.product_name,
                    "product_status": row.product_status,
                    "is_delete": row.is_delete,
                    "issue_type": []
                }
                
                if row.product_name is None:
                    issue["issue_type"].append("产品不存在")
                elif row.product_status != 1:
                    issue["issue_type"].append("产品已禁用")
                elif row.is_delete == 1:
                    issue["issue_type"].append("产品已删除")
                
                issues.append(issue)
            
            return {
                "total_issues": len(issues),
                "issues": issues
            }
    
    async def get_summary_stats(self) -> dict:
        """获取汇总统计"""
        async with self.engine.begin() as conn:
            # 总记录数
            total_sql = "SELECT COUNT(*) as total FROM service_product"
            total_result = await conn.execute(text(total_sql))
            total_count = total_result.fetchone().total
            
            # 有效记录数
            valid_sql = """
                SELECT COUNT(*) as valid_count
                FROM service_product sp
                INNER JOIN service_staff ss ON sp.staff_id = ss.id
                INNER JOIN product p ON sp.productid = p.id
                WHERE sp.company_uuid = ss.company_id
                AND sp.company_uuid = p.company_uuid
                AND p.product_status = 1
                AND p.is_delete = 0
                AND ss.is_delete = '0'
            """
            valid_result = await conn.execute(text(valid_sql))
            valid_count = valid_result.fetchone().valid_count
            
            return {
                "total_records": total_count,
                "valid_records": valid_count,
                "invalid_records": total_count - valid_count,
                "consistency_rate": round(valid_count / total_count * 100, 2) if total_count > 0 else 0
            }
    
    async def run_full_check(self) -> dict:
        """运行完整检查"""
        logger.info("开始数据一致性检查...")
        
        # 获取汇总统计
        stats = await self.get_summary_stats()
        logger.info(f"数据统计: {stats}")
        
        # 检查跨公司产品关联
        cross_company_issues = await self.check_cross_company_products()
        logger.info(f"跨公司产品关联问题: {cross_company_issues['total_issues']}个")
        
        # 检查无效产品关联
        invalid_product_issues = await self.check_invalid_products()
        logger.info(f"无效产品关联问题: {invalid_product_issues['total_issues']}个")
        
        return {
            "summary": stats,
            "cross_company_issues": cross_company_issues,
            "invalid_product_issues": invalid_product_issues
        }
    
    async def close(self):
        """关闭数据库连接"""
        await self.engine.dispose()


async def main():
    """主函数"""
    checker = DataConsistencyChecker()
    
    try:
        result = await checker.run_full_check()
        
        print("\n" + "="*60)
        print("数据一致性检查报告")
        print("="*60)
        
        # 汇总统计
        stats = result["summary"]
        print(f"\n📊 汇总统计:")
        print(f"  总记录数: {stats['total_records']}")
        print(f"  有效记录数: {stats['valid_records']}")
        print(f"  无效记录数: {stats['invalid_records']}")
        print(f"  一致性率: {stats['consistency_rate']}%")
        
        # 跨公司问题
        cross_issues = result["cross_company_issues"]
        print(f"\n🚨 跨公司产品关联问题: {cross_issues['total_issues']}个")
        if cross_issues['total_issues'] > 0:
            print("  前5个问题:")
            for i, issue in enumerate(cross_issues['issues'][:5]):
                print(f"    {i+1}. 员工{issue['staff_name']}(ID:{issue['staff_id']}) "
                      f"关联了产品{issue['product_name']}(ID:{issue['product_id']}) "
                      f"- {', '.join(issue['issue_type'])}")
        
        # 无效产品问题
        invalid_issues = result["invalid_product_issues"]
        print(f"\n❌ 无效产品关联问题: {invalid_issues['total_issues']}个")
        if invalid_issues['total_issues'] > 0:
            print("  前5个问题:")
            for i, issue in enumerate(invalid_issues['issues'][:5]):
                print(f"    {i+1}. 员工{issue['staff_name']}(ID:{issue['staff_id']}) "
                      f"关联了产品ID:{issue['product_id']} "
                      f"- {', '.join(issue['issue_type'])}")
        
        print("\n" + "="*60)
        
        # 如果有问题，返回非零退出码
        if stats['invalid_records'] > 0:
            sys.exit(1)
        else:
            print("✅ 数据一致性检查通过！")
            sys.exit(0)
            
    except Exception as e:
        logger.error(f"数据一致性检查失败: {str(e)}")
        print(f"❌ 检查失败: {str(e)}")
        sys.exit(1)
    finally:
        await checker.close()


if __name__ == "__main__":
    asyncio.run(main())
