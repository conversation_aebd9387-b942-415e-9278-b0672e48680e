"""
OCR识别控制器
"""
from fastapi import APIRouter, Depends, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from config.get_db import get_db
from utils.response_util import ResponseUtil
from utils.log_util import logger
from utils.baidu_ocr_util import baidu_ocr_util
from exceptions.exception import ValidationException
from module_admin.service.internal_user_login_service import InternalUserLoginService

# 请求模型
class ImageUrlRequest(BaseModel):
    imageUrl: str

# 创建路由器
ocr_controller = APIRouter(prefix='/api/v1/ocr', tags=['OCR识别'])


@ocr_controller.post('/idcard/front', summary="身份证正面OCR识别")
async def recognize_id_card_front(
    request: ImageUrlRequest,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """身份证正面OCR识别接口

    通过图片URL进行身份证正面OCR识别

    Args:
        request: 包含图片URL的请求对象
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        识别结果
    """
    try:
        logger.info(f"用户 {getattr(current_user.user, 'mobile', 'unknown')} 请求身份证正面OCR识别")

        # 验证图片URL
        if not request.imageUrl:
            raise ValidationException(message="请提供图片URL")

        # 调用百度OCR识别（通过URL）
        result = await baidu_ocr_util.recognize_id_card_front_by_url(request.imageUrl)

        logger.info("身份证正面OCR识别成功")
        return ResponseUtil.success(data=result, msg="身份证正面识别成功")

    except ValidationException as e:
        logger.warning(f"身份证正面OCR识别参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"身份证正面OCR识别失败: {str(e)}")
        return ResponseUtil.error(msg="身份证识别失败，请手动填写")


@ocr_controller.post('/idcard/back', summary="身份证背面OCR识别")
async def recognize_id_card_back(
    request: ImageUrlRequest,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """身份证背面OCR识别接口

    通过图片URL进行身份证背面OCR识别

    Args:
        request: 包含图片URL的请求对象
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        识别结果
    """
    try:
        logger.info(f"用户 {getattr(current_user.user, 'mobile', 'unknown')} 请求身份证背面OCR识别")

        # 验证图片URL
        if not request.imageUrl:
            raise ValidationException(message="请提供图片URL")

        # 调用百度OCR识别（通过URL）
        result = await baidu_ocr_util.recognize_id_card_back_by_url(request.imageUrl)

        logger.info("身份证背面OCR识别成功")
        return ResponseUtil.success(data=result, msg="身份证背面识别成功")

    except ValidationException as e:
        logger.warning(f"身份证背面OCR识别参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"身份证背面OCR识别失败: {str(e)}")
        return ResponseUtil.error(msg="身份证识别失败，请手动填写")


@ocr_controller.post('/bankcard', summary="银行卡OCR识别")
async def recognize_bank_card(
    request: ImageUrlRequest,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """银行卡OCR识别接口

    通过图片URL进行银行卡OCR识别

    Args:
        request: 包含图片URL的请求对象
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        识别结果
    """
    try:
        logger.info(f"用户 {getattr(current_user.user, 'mobile', 'unknown')} 请求银行卡OCR识别")

        # 验证图片URL
        if not request.imageUrl:
            raise ValidationException(message="请提供图片URL")

        # 调用百度OCR识别（通过URL）
        result = await baidu_ocr_util.recognize_bank_card_by_url(request.imageUrl)

        logger.info("银行卡OCR识别成功")
        return ResponseUtil.success(data=result, msg="银行卡识别成功")

    except ValidationException as e:
        logger.warning(f"银行卡OCR识别参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"银行卡OCR识别失败: {str(e)}")
        return ResponseUtil.error(msg="银行卡识别失败，请手动填写")


@ocr_controller.post('/business-license', summary="营业执照OCR识别")
async def recognize_business_license(
    request: ImageUrlRequest,
    current_user=Depends(InternalUserLoginService.get_current_user),
    query_db: AsyncSession = Depends(get_db)
):
    """营业执照OCR识别接口

    通过图片URL进行营业执照OCR识别

    Args:
        request: 包含图片URL的请求对象
        current_user: 当前登录用户信息
        query_db: 数据库会话

    Returns:
        识别结果
    """
    try:
        logger.info(f"用户 {getattr(current_user.user, 'mobile', 'unknown')} 请求营业执照OCR识别")

        # 验证图片URL
        if not request.imageUrl:
            raise ValidationException(message="请提供图片URL")

        # 调用百度OCR识别（通过URL）
        result = await baidu_ocr_util.recognize_business_license_by_url(request.imageUrl)

        logger.info("营业执照OCR识别成功")
        return ResponseUtil.success(data=result, msg="营业执照识别成功")

    except ValidationException as e:
        logger.warning(f"营业执照OCR识别参数验证失败: {e.message}")
        return ResponseUtil.error(msg=e.message)
    except Exception as e:
        logger.error(f"营业执照OCR识别失败: {str(e)}")
        return ResponseUtil.error(msg="营业执照识别失败，请手动填写")
