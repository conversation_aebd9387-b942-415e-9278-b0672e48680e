<template>
  <view class="page">
    <!-- 顶部导航 -->
    <appHead left fixed title="客户资料修改记录"></appHead>
    
    <!-- 修改记录列表 -->
    <view class="record-list">
      <view class="record-item">
        <view class="record-header">
          <text class="record-time">2025-03-04 14:45:53</text>
          <text class="record-operator">郑思敏</text>
        </view>
        <view class="record-content">
          <text class="record-text">郑思敏操作，把客户从"郑思敏"转移到"阿刚刚"名下</text>
        </view>
      </view>
      
      <!-- 可以添加更多记录项 -->
      
      <!-- 底部提示 -->
      <view class="end-tip">
        <text>已经到头了~</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      leadId: '',
      records: [
        {
          time: '2025-03-04 14:45:53',
          operator: '郑思敏',
          content: '郑思敏操作，把客户从"郑思敏"转移到"阿刚刚"名下'
        }
        // 可以添加更多记录
      ]
    };
  },
  onLoad(options) {
    if (options.id) {
      this.leadId = options.id;
      this.getModificationRecords();
    }
  },
  methods: {
    // 获取修改记录
    getModificationRecords() {
      // 实际项目中应该调用API获取修改记录
      console.log('获取客户资料修改记录，ID:', this.leadId);
      // 这里可以添加实际的API调用
      // 目前使用模拟数据
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
  padding-top: 90rpx;
}

.record-list {
  padding: 20rpx;
}

.record-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.record-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.record-time {
  font-size: 28rpx;
  color: #666;
}

.record-operator {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.record-content {
  margin-bottom: 10rpx;
}

.record-text {
  font-size: 30rpx;
  color: #ff9900;
  line-height: 1.5;
}

.end-tip {
  text-align: center;
  padding: 40rpx 0;
  
  text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
