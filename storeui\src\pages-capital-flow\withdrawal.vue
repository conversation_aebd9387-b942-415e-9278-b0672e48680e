<template>
  <view class="animated fadeIn withdrawal-page">
    <appHead left fixed title="申请提现"></appHead>
    <view class="form">
      <view class="form-item">
        <text class="label">提现类型</text>
        <view class="type-options">
          <view
            class="type-option"
            :class="{ selected: withdrawalForm.type === item.value }"
            v-for="item in withdrawalTypes"
            :key="item.value"
            @click="selectWithdrawalType(item.value)"
          >
            {{ item.label }}
          </view>
        </view>
      </view>
      <view class="form-item">
        <text class="label">提现金额</text>
        <view class="amount-input">
            <text>¥</text>
            <u-input v-model="withdrawalForm.amount" placeholder="请输入金额" type="number" @input="onAmountInput"></u-input>
          </view>
        <view class="balance">
          <view class="balance-text">
            <text>可用余额 </text>
            <text v-if="loading">加载中...</text>
            <text v-else>¥{{ balance }}</text>
          </view>
          <text class="all" @click="withdrawAll">全部提现</text>
        </view>
      </view>
      <view class="form-item">
        <text class="label">收款账户</text>
        <!-- 账户选择区域 -->
        <view class="account-select" v-if="filteredAccounts.length > 0">
          <view class="select-header">
            <view class="selected-account" @click="toggleAccountSelector">
              <view class="selected-info">
                <text v-if="selectedAccount" class="selected-name">{{ selectedAccount.accountName }}</text>
                <text v-if="selectedAccount" class="selected-detail">{{ selectedAccount.bankName }} {{ maskBankAccount(selectedAccount.bankAccount) }}</text>
                <text v-else class="placeholder">请选择提现账户</text>
              </view>
              <u-icon name="arrow-down" size="24" color="#666" :class="{ 'rotate': showAccountSelector }"></u-icon>
            </view>
            <view class="add-account-btn" @click="navigateToAccountManage">管理账户</view>
          </view>

          <!-- 账户下拉选择器 -->
          <view class="account-dropdown" v-if="showAccountSelector">
            <view
              class="account-option"
              v-for="(item, index) in filteredAccounts"
              :key="index"
              @click="selectAccount(item)"
              :class="{ 'selected': selectedAccountId === item.id }"
            >
              <view class="account-option-info">
                <text class="account-name">{{ item.accountName }}</text>
                <view class="account-tags">
                  <text class="account-type">{{ item.accountType === 1 ? '企业对公' : '个人' }}</text>
                  <text class="default-tag" v-if="item.isDefault">默认</text>
                </view>
                <text class="account-detail">{{ item.bankName }} {{ maskBankAccount(item.bankAccount) }}</text>
              </view>
              <u-icon name="checkmark" size="20" color="#007aff" v-if="selectedAccountId === item.id"></u-icon>
            </view>
          </view>
        </view>
        
        <!-- 无匹配账户时显示提示 -->
        <view class="no-account" v-else>
          <text v-if="savedAccounts.length === 0">暂无提现账户</text>
          <text v-else>暂无{{ withdrawalForm.type === 1 ? '企业对公' : '个人' }}提现账户</text>
          <view class="add-account-btn" @click="navigateToAccountManage">
            {{ savedAccounts.length === 0 ? '添加账户' : '管理账户' }}
          </view>
        </view>
        
        <!-- 选中账户后显示账户信息 -->
        <view class="selected-account-info" v-if="selectedAccount">
          <view class="account-detail-item">
            <text class="detail-label">银行名称：</text>
            <text class="detail-value">{{ selectedAccount.bankName }}</text>
          </view>
          <view class="account-detail-item">
            <text class="detail-label">开户行：</text>
            <text class="detail-value">{{ selectedAccount.bankBranch }}</text>
          </view>
          <view class="account-detail-item">
            <text class="detail-label">银行账号：</text>
            <text class="detail-value">{{ maskBankAccount(selectedAccount.bankAccount) }}</text>
          </view>
          <view class="account-detail-item">
            <text class="detail-label">开户人：</text>
            <text class="detail-value">{{ selectedAccount.accountHolder }}</text>
          </view>
        </view>
        
        <!-- 手动输入表单（无账户或选择手动输入时显示） -->
        <view class="manual-input" v-if="savedAccounts.length === 0 || manualInputMode">
          <view class="manual-input-header" v-if="savedAccounts.length > 0">
            <text>手动输入账户信息</text>
            <text class="switch-btn" @click="manualInputMode = false">使用已保存账户</text>
          </view>
          <u-input v-model="withdrawalForm.bankName" placeholder="银行名称"></u-input>
          <u-input v-model="withdrawalForm.bankBranch" placeholder="开户行"></u-input>
          <u-input v-model="withdrawalForm.bankAccount" placeholder="银行账号"></u-input>
          <u-input v-model="withdrawalForm.accountHolder" placeholder="开户人姓名"></u-input>
        </view>
      </view>
      
      <!-- 企业对公提现时显示开票图片上传 -->
      <view class="form-item" v-if="withdrawalForm.type === 1">
        <text class="label">开票图片上传</text>
        <view class="upload-container">
          <view class="upload-box" @tap="chooseImage" v-if="!withdrawalForm.invoiceImage">
            <u-icon name="camera" size="60" color="#ddd"></u-icon>
            <text class="upload-text">点击上传开票图片</text>
          </view>
          <view class="image-preview" v-else>
            <image :src="withdrawalForm.invoiceImage" mode="aspectFill"></image>
            <view class="image-actions">
              <view class="action-btn" @tap="previewImage">查看</view>
              <view class="action-btn delete" @tap="deleteImage">删除</view>
            </view>
          </view>
        </view>
        <text class="upload-tip">请上传清晰的开票信息图片，包含公司名称、税号等信息</text>
      </view>
      <view class="form-item">
        <text class="label">费用说明</text>
        <view class="fee-info">
          <!-- 根据提现类型显示不同的手续费规则 -->
          <text v-if="withdrawalForm.type === 1">• 企业对公提现：无手续费</text>
          <text v-if="withdrawalForm.type === 2">• 个人提现：{{ withdrawalConfig.withdrawal_fee_rate_percent }} 代扣个税</text>
          <text v-if="withdrawalForm.amount && withdrawalForm.type === 2">• 本次代扣个税 ¥{{ calculateFee() }}</text>
          <text v-if="withdrawalForm.amount" class="actual-amount">• 实际到账金额 ¥{{ calculateActualAmount() }}</text>
          <text>• 预计到账时间 1-3个工作日</text>
          <text>• 最低提现金额 ¥100</text>
        </view>
      </view>
      <view class="buttons">
        <u-button type="primary" @click="submit">
          <text v-if="!withdrawalForm.amount">确认提现</text>
          <text v-else>确认提现 ¥{{ calculateActualAmount() }}</text>
        </u-button>
        <u-button @click="cancel">取消</u-button>
      </view>
      
      <!-- 温馨提示 -->
      <view class="tips">
        <view class="tips-title">温馨提示：</view>
        <view class="tips-content">
          <text>• 提现申请提交后将在1-3个工作日内处理完成</text>
          <!-- 根据提现类型动态显示手续费说明 -->
          <text v-if="withdrawalForm.type === 1">• 企业对公提现无手续费，申请金额即为到账金额</text>
          <text v-if="withdrawalForm.type === 2">• 个人提现将代扣{{ withdrawalConfig.withdrawal_fee_rate_percent }}的个税</text>
          <text>• 请确保填写的银行账户信息准确无误</text>
          <text>• 如有疑问，请联系客服</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import appHead from '@/common/appHead.vue';

export default {
  components: {
    appHead
  },
  data() {
    return {
      withdrawalForm: {
        type: 1,
        amount: '',
        bankName: '',
        bankBranch: '',
        bankAccount: '',
        accountHolder: '',
        invoiceImage: ''
      },
      withdrawalTypes: [
        { value: 1, label: '企业对公提现' },
        { value: 2, label: '个人提现' }
      ],
      balance: '0.00',
      loading: false,
      // 账户选择相关数据
      savedAccounts: [],
      selectedAccountId: null,
      selectedAccount: null,
      showAccountSelector: false,
      manualInputMode: false,
      loadingAccounts: false,
      // 提现费率配置
      withdrawalConfig: {
        withdrawal_fee_rate: 0.2000,    // 个人提现代扣个税费率（默认20%）
        withdrawal_fee_rate_percent: '20.00%'
      },
      loadingConfig: false
    }
  },

  computed: {
    // 根据当前提现类型过滤账户
    filteredAccounts() {
      if (!this.savedAccounts || this.savedAccounts.length === 0) {
        return [];
      }

      // 过滤出与当前提现类型匹配的账户
      return this.savedAccounts.filter(account =>
        account.accountType === this.withdrawalForm.type
      );
    }
  },

  onLoad(options) {
    // 如果有传递余额参数，则直接使用
    if (options && options.balance) {
      this.balance = options.balance;
    } else {
      // 否则通过API获取余额数据
      this.getBalanceData();
    }

    // 获取提现账户列表
    this.getWithdrawalAccounts();

    // 获取提现配置
    this.getWithdrawalConfig();

    // 如果有传递账户ID参数，则直接选择该账户
    if (options && options.accountId) {
      this.selectedAccountId = options.accountId;
    }
  },
  methods: {
    // 获取余额数据
    async getBalanceData() {
      this.loading = true;

      try {
        // 使用项目标准API调用方式，require.js自动处理响应
        const balanceData = await this.$get('/api/v1/payment/balance-info');
        this.balance = balanceData?.available || '0.00';
        console.log('余额数据获取成功:', this.balance);
      } catch (error) {
        console.error('获取余额数据失败:', error);
        // require.js已经显示了错误提示，这里只需要处理UI状态
      } finally {
        this.loading = false;
      }
    },
    
    // 获取提现账户列表
    async getWithdrawalAccounts() {
      this.loadingAccounts = true;

      try {
        // 使用项目标准API调用方式，require.js自动处理响应
        const accountsData = await this.$get('/api/v1/payment/withdrawal/accounts');
        this.savedAccounts = accountsData || [];

        // 自动选择合适的账户
        this.autoSelectAccount();

        console.log('提现账户列表获取成功:', this.savedAccounts);
      } catch (error) {
        console.error('获取提现账户列表失败:', error);
        // require.js已经显示了错误提示，这里只需要处理UI状态
      } finally {
          this.loadingAccounts = false;
        }
    },

    // 获取提现配置
    async getWithdrawalConfig() {
      this.loadingConfig = true;

      try {
        // 使用项目标准API调用方式，require.js自动处理响应
        const configData = await this.$get('/api/v1/company/withdrawal-config');

        if (configData) {
          this.withdrawalConfig = {
            withdrawal_fee_rate: configData.withdrawal_fee_rate || 0.2000,
            withdrawal_fee_rate_percent: configData.withdrawal_fee_rate_percent || '20.00%'
          };
        }

        console.log('提现配置获取成功:', this.withdrawalConfig);
      } catch (error) {
        console.error('获取提现配置失败:', error);
        // 使用默认配置
        this.withdrawalConfig = {
          withdrawal_fee_rate: 0.2000,
          withdrawal_fee_rate_percent: '20.00%'
        };
        console.log('使用默认提现配置:', this.withdrawalConfig);
      } finally {
        this.loadingConfig = false;
      }
    },
    
    // 切换账户选择器显示状态
    toggleAccountSelector() {
      this.showAccountSelector = !this.showAccountSelector;
    },

    // 选择账户
    selectAccount(account) {
      this.selectedAccount = account;
      this.selectedAccountId = account.id;
      this.showAccountSelector = false;

      // 确保账户类型与当前提现类型匹配
      if (account.accountType !== this.withdrawalForm.type) {
        this.withdrawalForm.type = account.accountType;
      }

      // 填充表单数据
      this.withdrawalForm.bankName = account.bankName;
      this.withdrawalForm.bankBranch = account.bankBranch;
      this.withdrawalForm.bankAccount = account.bankAccount;
      this.withdrawalForm.accountHolder = account.accountHolder;

      // 如果是企业对公账户，填充开票图片
      if (account.accountType === 1 && account.invoiceImage) {
        this.withdrawalForm.invoiceImage = account.invoiceImage;
      }

      // 关闭手动输入模式
      this.manualInputMode = false;

      console.log('选择账户:', account.accountName, '类型:', account.accountType === 1 ? '企业对公' : '个人');
    },

    // 自动选择合适的账户
    autoSelectAccount() {
      // 如果有指定的账户ID，优先选择该账户
      if (this.selectedAccountId) {
        const account = this.savedAccounts.find(item =>
          item.id === this.selectedAccountId && item.accountType === this.withdrawalForm.type
        );
        if (account) {
          this.selectAccount(account);
          return;
        }
      }

      // 否则选择符合当前提现类型的默认账户
      const defaultAccount = this.savedAccounts.find(item =>
        item.isDefault && item.accountType === this.withdrawalForm.type
      );
      if (defaultAccount) {
        this.selectAccount(defaultAccount);
        return;
      }

      // 如果没有默认账户，选择第一个符合类型的账户
      const firstMatchingAccount = this.savedAccounts.find(item =>
        item.accountType === this.withdrawalForm.type
      );
      if (firstMatchingAccount) {
        this.selectAccount(firstMatchingAccount);
      }
    },
    
    // 切换到手动输入模式
    switchToManualInput() {
      this.manualInputMode = true;
      this.selectedAccount = null;
      this.selectedAccountId = null;
    },
    
    // 导航到账户管理页面
    navigateToAccountManage() {
      console.log('正在跳转到账户管理页面...');
      uni.navigateTo({
        url: '/pages-capital-flow/withdrawal-accounts',
        success: function() {
          console.log('跳转到账户管理页面成功');
        },
        fail: function(err) {
          console.error('跳转到账户管理页面失败:', err);
          uni.showToast({
            title: '页面跳转失败，请重试',
            icon: 'none'
          });
        }
      });
    },
    
    // 银行账号脱敏显示
    maskBankAccount(account) {
      if (!account) return '';
      if (account.length <= 8) return account;
      return account.substring(0, 4) + '****' + account.substring(account.length - 4);
    },
    
    selectWithdrawalType(value) {
      // 如果切换了提现类型，需要重新选择合适的账户
      if (this.withdrawalForm.type !== value) {
        this.withdrawalForm.type = value;

        // 清空当前选择的账户（如果类型不匹配）
        if (this.selectedAccount && this.selectedAccount.accountType !== value) {
          this.selectedAccount = null;
          this.selectedAccountId = null;
          this.manualInputMode = false;

          // 清空表单中的账户信息，但保留金额
          const amount = this.withdrawalForm.amount;
          this.withdrawalForm = {
            type: value,
            amount: amount,
            bankName: '',
            bankBranch: '',
            bankAccount: '',
            accountHolder: '',
            invoiceImage: ''
          };
        }

        // 重新自动选择合适的账户
        this.autoSelectAccount();

        // 关闭账户选择器
        this.showAccountSelector = false;
      }
    },
    
    withdrawAll() {
      this.withdrawalForm.amount = this.balance
    },
    async submit() {
      // 表单验证
      if (!this.withdrawalForm.amount || parseFloat(this.withdrawalForm.amount) <= 0) {
        uni.showToast({
          title: '请输入有效的提现金额',
          icon: 'none'
        });
        return;
      }
      
      if (parseFloat(this.withdrawalForm.amount) > parseFloat(this.balance)) {
        uni.showToast({
          title: '提现金额不能大于可用余额',
          icon: 'none'
        });
        return;
      }
      
      if (parseFloat(this.withdrawalForm.amount) < 100) {
        uni.showToast({
          title: '最低提现金额为100元',
          icon: 'none'
        });
        return;
      }
      
      if (!this.withdrawalForm.bankName) {
        uni.showToast({
          title: '请输入银行名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.withdrawalForm.bankAccount) {
        uni.showToast({
          title: '请输入银行账号',
          icon: 'none'
        });
        return;
      }
      
      if (!this.withdrawalForm.accountHolder) {
        uni.showToast({
          title: '请输入开户人姓名',
          icon: 'none'
        });
        return;
      }
      
      // 企业对公提现需要验证开票图片
      if (this.withdrawalForm.type === 1 && !this.withdrawalForm.invoiceImage) {
        uni.showToast({
          title: '请上传开票图片',
          icon: 'none'
        });
        return;
      }
      
      // 显示加载中
      uni.showLoading({
        title: '提交中...'
      });
      
      try {
        // 计算手续费和实际到账金额
        const applyAmount = parseFloat(this.withdrawalForm.amount);
        let feeRate = 0;
        let feeAmount = 0;
        let actualAmount = applyAmount;

        if (this.withdrawalForm.type === 1) {
          // 企业对公提现：无手续费
          feeRate = 0.0000;
          feeAmount = 0.0000;
          actualAmount = applyAmount;
        } else if (this.withdrawalForm.type === 2) {
          // 个人提现：代扣个税，使用配置的费率
          feeRate = this.withdrawalConfig.withdrawal_fee_rate;
          feeAmount = applyAmount * feeRate;
          actualAmount = applyAmount - feeAmount;
        }

        const submitData = {
          // 后端API期望的字段名称
          apply_amount: applyAmount,                    // 后端期望 apply_amount
          withdrawal_type: this.withdrawalForm.type,   // 后端期望 withdrawal_type
          bank_name: this.withdrawalForm.bankName,     // 后端期望 bank_name
          bank_account: this.withdrawalForm.bankAccount, // 后端期望 bank_account
          account_holder: this.withdrawalForm.accountHolder, // 后端期望 account_holder

          // 可选字段
          bank_branch: this.withdrawalForm.bankBranch || '', // 银行支行
          fee_rate: feeRate,                           // 手续费率
          fee_amount: feeAmount,                       // 手续费金额
          actual_amount: actualAmount,                 // 实际到账金额
          apply_reason: '用户申请提现',                 // 申请原因

          // 开票图片URL（统一使用invoice_info字段存储）
          invoice_info: this.withdrawalForm.invoiceImage || ''
        };
        
        // 如果使用的是已保存的账户，添加账户ID
        if (this.selectedAccount && !this.manualInputMode) {
          submitData.accountId = this.selectedAccount.id;
        }

        console.log('=== 提现申请提交数据详情 ===');
        console.log('提现类型:', submitData.withdrawal_type === 1 ? '企业对公' : '个人提现');
        console.log('申请金额:', submitData.apply_amount);
        console.log('手续费率:', submitData.fee_rate);
        console.log('手续费金额:', submitData.fee_amount);
        console.log('实际到账:', submitData.actual_amount);
        console.log('银行信息:', {
          bank_name: submitData.bank_name,
          bank_branch: submitData.bank_branch,
          bank_account: submitData.bank_account,
          account_holder: submitData.account_holder
        });
        console.log('开票图片URL:', submitData.invoice_info ? '已上传' : '未上传');
        console.log('完整提交数据:', submitData);

        // 使用项目标准API调用方式，require.js自动处理响应
        const result = await this.$post('/api/v1/payment/withdrawal/apply', submitData, {
          contentType: 'application/json'
        });

        console.log('提现申请接口响应:', result);

        uni.showToast({
          title: '提现申请已提交',
          icon: 'success'
        });

        // 如果是手动输入模式且用户没有保存过账户，询问是否保存当前账户
        if ((this.manualInputMode || this.savedAccounts.length === 0) &&
            !this.selectedAccount) {
          setTimeout(() => {
            uni.showModal({
              title: '保存账户信息',
              content: '是否保存此次提现的账户信息，方便下次使用？',
              confirmText: '保存',
              cancelText: '不保存',
              success: (res) => {
                if (res.confirm) {
                  // 跳转到账户编辑页面，并传递当前账户信息
                  uni.navigateTo({
                    url: '/pages-capital-flow/withdrawal-account-edit?type=' +
                         this.withdrawalForm.type +
                         '&bankName=' + encodeURIComponent(this.withdrawalForm.bankName) +
                         '&bankBranch=' + encodeURIComponent(this.withdrawalForm.bankBranch) +
                         '&bankAccount=' + encodeURIComponent(this.withdrawalForm.bankAccount) +
                         '&accountHolder=' + encodeURIComponent(this.withdrawalForm.accountHolder)
                  });
                } else {
                  // 直接返回上一页
                  uni.navigateBack();
                }
              }
            });
          }, 1500);
        } else {
          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      } catch (error) {
        console.error('提交提现申请失败:', error);
        // require.js已经显示了错误提示，这里只需要处理UI状态
      } finally {
        uni.hideLoading();
      }
    },
    cancel() {
      uni.navigateBack();
    },
    // 计算提现手续费（个人提现为代扣个税）
    calculateFee() {
      if (!this.withdrawalForm.amount || parseFloat(this.withdrawalForm.amount) <= 0) {
        return '0.00';
      }

      const amount = parseFloat(this.withdrawalForm.amount);

      // 根据提现类型计算手续费
      if (this.withdrawalForm.type === 1) {
        // 企业对公提现：无手续费
        return '0.00';
      } else if (this.withdrawalForm.type === 2) {
        // 个人提现：代扣个税，使用配置的费率
        const fee = amount * this.withdrawalConfig.withdrawal_fee_rate;
        return fee.toFixed(2);
      }

      return '0.00';
    },

    // 计算实际到账金额
    calculateActualAmount() {
      if (!this.withdrawalForm.amount || parseFloat(this.withdrawalForm.amount) <= 0) {
        return '0.00';
      }

      const amount = parseFloat(this.withdrawalForm.amount);

      // 根据提现类型计算实际到账金额
      if (this.withdrawalForm.type === 1) {
        // 企业对公提现：无手续费，申请金额即为到账金额
        return amount.toFixed(2);
      } else if (this.withdrawalForm.type === 2) {
        // 个人提现：实际到账金额 = 申请金额 - 代扣个税
        const fee = amount * this.withdrawalConfig.withdrawal_fee_rate;
        const actualAmount = amount - fee;
        return actualAmount.toFixed(2);
      }

      return amount.toFixed(2);
    },
    // 处理金额输入事件
    onAmountInput(value) {
      // 限制只能输入数字和小数点
      this.withdrawalForm.amount = value.replace(/[^\d.]/g, '');
      // 限制只能有一个小数点
      if (this.withdrawalForm.amount.split('.').length > 2) {
        this.withdrawalForm.amount = this.withdrawalForm.amount.slice(0, -1);
      }
      // 限制小数点后最多两位
      if (this.withdrawalForm.amount.indexOf('.') !== -1) {
        const parts = this.withdrawalForm.amount.split('.');
        if (parts[1].length > 2) {
          this.withdrawalForm.amount = parts[0] + '.' + parts[1].slice(0, 2);
        }
      }
    },
    
    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          // 显示加载中
          uni.showLoading({
            title: '上传中...'
          });
          
          // 上传图片到服务器
          uni.uploadFile({
            url: this.$baseUrl + '/api/v1/public/upload',
            filePath: res.tempFilePaths[0],
            name: 'file',
            success: (uploadRes) => {
              try {
                const result = JSON.parse(uploadRes.data);
                if (result.code === 200 && result.data && result.data.url) {
                  // 保存上传后的图片URL到表单数据
                  this.withdrawalForm.invoiceImage = result.data.url;
                  console.log('开票图片上传成功，URL:', result.data.url);
                } else {
                  uni.showToast({
                    title: '图片上传失败',
                    icon: 'none'
                  });
                  // 临时保存本地路径，以便显示预览
                  this.withdrawalForm.invoiceImage = res.tempFilePaths[0];
                }
              } catch (error) {
                console.error('上传响应解析失败:', error);
                uni.showToast({
                  title: '图片上传失败',
                  icon: 'none'
                });
                // 临时保存本地路径，以便显示预览
                this.withdrawalForm.invoiceImage = res.tempFilePaths[0];
              }
            },
            fail: (error) => {
              console.error('图片上传请求失败:', error);
              uni.showToast({
                title: '图片上传失败',
                icon: 'none'
              });
              // 临时保存本地路径，以便显示预览
              this.withdrawalForm.invoiceImage = res.tempFilePaths[0];
            },
            complete: () => {
              uni.hideLoading();
            }
          });
        }
      });
    },
    
    // 预览图片
    previewImage() {
      if (this.withdrawalForm.invoiceImage) {
        uni.previewImage({
          urls: [this.withdrawalForm.invoiceImage],
          current: this.withdrawalForm.invoiceImage
        });
      }
    },
    
    // 删除图片
    deleteImage() {
      uni.showModal({
        title: '提示',
        content: '确定要删除此图片吗？',
        success: (res) => {
          if (res.confirm) {
            this.withdrawalForm.invoiceImage = '';
          }
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.withdrawal-page { 
  padding: 20rpx; 
  background: #f8f8f8; 
  min-height: 100vh;
}

.form {
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-item { 
  margin-bottom: 40rpx; 
}

.label { 
  font-size: 30rpx; 
  margin-bottom: 20rpx; 
  display: block; 
  font-weight: 500;
  color: #333;
}

.type-options { 
  display: flex; 
  justify-content: space-between; 
}

.type-option { 
  padding: 24rpx; 
  border: 1px solid #ddd; 
  border-radius: 8rpx; 
  width: 48%; 
  text-align: center; 
  font-size: 28rpx;
  transition: all 0.3s;
}

.type-option.selected { 
  border-color: $xyj-theme; 
  background: rgba($xyj-theme, 0.1); 
  color: $xyj-theme;
}

.amount-input { 
  display: flex; 
  align-items: center; 
  border: 1px solid #ddd; 
  padding: 24rpx; 
  border-radius: 8rpx; 
  background: #f9f9f9;
}

.balance { 
  font-size: 26rpx; 
  color: #666; 
  margin-top: 16rpx; 
  display: flex; 
  justify-content: space-between; 
  align-items: center;
}

.balance-text {
  display: flex;
  align-items: center;
}

.all { 
  color: $xyj-theme; 
  font-weight: 500;
  background-color: rgba($xyj-theme, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

u-input { 
  margin-bottom: 24rpx; 
  border: 1px solid #eee; 
  padding: 24rpx; 
  border-radius: 8rpx; 
  background: #f9f9f9;
}

.fee-info {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  background: linear-gradient(135deg, #f9f9f9 0%, #f5f7fa 100%);
  padding: 24rpx;
  border-radius: 12rpx;
  border: 1rpx solid #e8e8e8;
}

.fee-info text {
  display: block;
  margin-bottom: 12rpx;
}

.fee-info text:last-child {
  margin-bottom: 0;
}

/* 实际到账金额突出显示 */
.fee-info .actual-amount {
  font-size: 30rpx !important;
  font-weight: 600 !important;
  color: $xyj-theme !important;
  background: rgba($xyj-theme, 0.1);
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  margin: 16rpx 0 !important;
  border-left: 4rpx solid $xyj-theme;
}

.buttons { 
  display: flex; 
  justify-content: space-between; 
  margin-top: 60rpx;
}

u-button { 
  width: 48%; 
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
}

.tips {
  margin-top: 40rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 8rpx;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
}

.tips-content text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
}

/* 账户选择相关样式 */
.account-select {
  margin-bottom: 30rpx;
}

.select-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.selected-account {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  padding: 24rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e8e8e8;
  flex: 1;
  margin-right: 20rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.selected-account:active {
  transform: scale(0.98);
  border-color: $xyj-theme;
}

.selected-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.selected-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}

.selected-detail {
  font-size: 24rpx;
  color: #666;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.placeholder {
  font-size: 28rpx;
  color: #999;
}

.selected-account .rotate {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

.add-account-btn {
  font-size: 26rpx;
  color: #fff;
  padding: 24rpx 32rpx;
  border: none;
  border-radius: 12rpx;
  background: linear-gradient(135deg, $xyj-theme, lighten($xyj-theme, 10%));
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba($xyj-theme, 0.3);
  transition: all 0.3s ease;
}

.add-account-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba($xyj-theme, 0.2);
}

.account-dropdown {
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.account-option {
  padding: 24rpx;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.account-option:last-child {
  border-bottom: none;
}

.account-option.selected {
  background-color: rgba($xyj-theme, 0.05);
}

.account-option-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.account-name {
  font-size: 30rpx;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  font-weight: 600;
}

.account-option.selected .account-name {
  color: $xyj-theme;
}

.account-tags {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.account-type {
  font-size: 22rpx;
  color: #666;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-weight: 500;
  border: 1rpx solid #e9ecef;
  margin-right: 12rpx;
}

.account-option.selected .account-type {
  background: linear-gradient(135deg, rgba($xyj-theme, 0.1), rgba($xyj-theme, 0.05));
  color: $xyj-theme;
  border-color: rgba($xyj-theme, 0.2);
}

.account-detail {
  font-size: 26rpx;
  color: #666;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  letter-spacing: 1rpx;
}

.account-option.selected .account-detail {
  color: #555;
}

.default-tag {
  font-size: 20rpx;
  color: #fff;
  background: linear-gradient(135deg, $xyj-theme, lighten($xyj-theme, 10%));
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba($xyj-theme, 0.3);
  text-transform: uppercase;
}

.no-account {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

.no-account .add-account-btn {
  margin-top: 20rpx;
  padding: 16rpx 40rpx;
}

.selected-account-info {
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.account-detail-item {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}

.account-detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #666;
  width: 160rpx;
}

.detail-value {
  color: #333;
  flex: 1;
}

.manual-input {
  margin-top: 20rpx;
}

.manual-input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.switch-btn {
  font-size: 26rpx;
  color: $xyj-theme;
}

/* 图片上传样式 */
.upload-container {
  margin-top: 20rpx;
}

.upload-box {
  width: 200rpx;
  height: 200rpx;
  border: 1px dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.image-preview {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.image-preview image {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: rgba(0, 0, 0, 0.5);
}

.action-btn {
  flex: 1;
  text-align: center;
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 0;
}

.action-btn.delete {
  background-color: rgba(255, 0, 0, 0.7);
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
  display: block;
}
</style>