<template>
  <view class="animated fadeIn withdrawal-accounts-page">
    <appHead left fixed title="提现账户管理"></appHead>
    <view class="container">
      <!-- 账户列表 -->
      <view class="account-list" v-if="accounts.length > 0">
        <view 
          class="account-item" 
          v-for="(item, index) in accounts" 
          :key="index"
          :class="{ 'is-default': item.isDefault }"
        >
          <view class="account-info">
            <view class="account-name">{{ item.accountName }}</view>
            <view class="account-type">{{ item.accountType === 1 ? '企业对公' : '个人' }}</view>
            <view class="account-details">
              <text>{{ item.bankName }} {{ item.bankBranch }}</text>
              <text>{{ maskBankAccount(item.bankAccount) }}</text>
              <text>{{ item.accountHolder }}</text>
            </view>
            <view class="default-tag" v-if="item.isDefault">默认</view>
          </view>
          <view class="account-actions">
            <view class="action-btn" @click="setDefault(item)" v-if="!item.isDefault">设为默认</view>
            <view class="action-btn" @click="editAccount(item)">编辑</view>
            <view class="action-btn delete" @click="deleteAccount(item)">删除</view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-else>
        <u-icon name="account" size="80" color="#ddd"></u-icon>
        <text>暂无提现账户</text>
        <text>添加账户后可快速提现</text>
      </view>
      
      <!-- 添加账户按钮 -->
      <view class="add-btn" @click="navigateToAddAccount">
        <u-icon name="plus" size="20" color="#fff"></u-icon>
        <text>添加提现账户</text>
      </view>
    </view>
  </view>
</template>

<script>
import appHead from '@/common/appHead.vue';
import {
  getWithdrawalAccounts,
  deleteWithdrawalAccount,
  setDefaultWithdrawalAccount
} from '@/api/withdrawal-account.js';

export default {
  components: {
    appHead
  },
  data() {
    return {
      accounts: [],
      loading: false,
      storeUuid: '' // 从store中获取或通过API获取
    }
  },
  onLoad() {
    this.getAccountList();
  },
  methods: {
    // 获取账户列表
    async getAccountList() {
      this.loading = true;

      try {
        // require.js已经处理了业务逻辑，直接使用返回的数据
        const accounts = await getWithdrawalAccounts();
        this.accounts = accounts || [];
        console.log('提现账户列表获取成功:', this.accounts);
      } catch (error) {
        console.error('获取提现账户列表失败:', error);
        // require.js已经显示了错误提示，这里只需要处理UI状态
      } finally {
        this.loading = false;
      }
    },
    
    // 银行账号脱敏显示
    maskBankAccount(account) {
      if (!account) return '';
      if (account.length <= 8) return account;
      return account.substring(0, 4) + '****' + account.substring(account.length - 4);
    },
    
    // 设置默认账户
    async setDefault(account) {
      uni.showLoading({
        title: '设置中...'
      });

      try {
        // require.js已经处理了业务逻辑，成功时直接返回数据
        await setDefaultWithdrawalAccount(account.id);
        uni.showToast({
          title: '设置成功',
          icon: 'success'
        });
        this.getAccountList(); // 重新获取列表
      } catch (error) {
        console.error('设置默认账户失败:', error);
        // require.js已经显示了错误提示，这里只需要处理UI状态
      } finally {
        uni.hideLoading();
      }
    },
    
    // 编辑账户
    editAccount(account) {
      uni.navigateTo({
        url: `/pages-capital-flow/withdrawal-account-edit?id=${account.id}`
      });
    },
    
    // 删除账户
    deleteAccount(account) {
      if (account.isDefault) {
        uni.showToast({
          title: '默认账户不能删除，请先设置其他账户为默认',
          icon: 'none'
        });
        return;
      }

      uni.showModal({
        title: '提示',
        content: '确定要删除此提现账户吗？',
        success: async (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '删除中...'
            });

            try {
              // require.js已经处理了业务逻辑，成功时直接返回数据
              await deleteWithdrawalAccount(account.id);
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.getAccountList(); // 重新获取列表
            } catch (error) {
               console.error('删除账户失败:', error);
               // require.js已经显示了错误提示，这里只需要处理UI状态
             } finally {
               uni.hideLoading();
             }
          }
        }
      });
    },
    
    // 跳转到添加账户页面
    navigateToAddAccount() {
      uni.navigateTo({
        url: '/pages-capital-flow/withdrawal-account-edit'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.withdrawal-accounts-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

.container {
  padding: 20rpx;
  margin-top: 20rpx;
}

.account-list {
  margin-bottom: 40rpx;
}

.account-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  
  &.is-default {
    border-left: 8rpx solid $xyj-theme;
  }
}

.account-info {
  margin-bottom: 20rpx;
}

.account-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.account-type {
  font-size: 24rpx;
  color: #666;
  background-color: #f2f2f2;
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
}

.account-details {
  display: flex;
  flex-direction: column;
  
  text {
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
  }
}

.default-tag {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: $xyj-theme;
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.account-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
  padding-top: 20rpx;
}

.action-btn {
  font-size: 26rpx;
  color: $xyj-theme;
  padding: 10rpx 20rpx;
  margin-left: 20rpx;
  background-color: rgba($xyj-theme, 0.1);
  border-radius: 30rpx;
  
  &.delete {
    color: #ff4d4f;
    background-color: rgba(#ff4d4f, 0.1);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  text {
    font-size: 28rpx;
    color: #999;
    margin-top: 20rpx;
  }
}

.add-btn {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: $xyj-theme;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba($xyj-theme, 0.3);
  
  text {
    font-size: 28rpx;
    margin-left: 10rpx;
  }
}
</style>