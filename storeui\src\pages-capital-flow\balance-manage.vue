<template>
  <!-- 余额管理 -->
  <view class="balance-manage">
    <appHead left fixed title="余额管理"></appHead>
    
    <!-- 余额显示卡片 -->
    <view class="balance-card">
      <view class="balance-info">
        <view class="balance-title">账户余额</view>
        <view class="balance-amount">￥{{ balanceInfo.available || '0.00' }}</view>
        <view class="balance-details">
          <view class="detail-item">
            <text class="detail-label">可用</text>
            <text class="detail-value">￥{{ balanceInfo.available || '0.00' }}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">冻结</text> 
            <text class="detail-value">￥{{ balanceInfo.frozen || '0.00' }}</text>
          </view>
        </view>
      </view>
      <view class="balance-actions">
        <!-- 开户状态提示 -->
        <view v-if="shouldShowStatusTip" :class="getStatusTipClass">
          <text class="status-text">{{ getStatusTipText }}</text>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <view class="action-btn" @click="showRechargeModal">充值</view>
          <view class="action-btn withdrawal-btn" @click="navigateToWithdrawal">提现</view>
          <!-- 根据开户状态动态显示开户按钮 -->
          <view v-if="shouldShowAccountButton" class="action-btn account-btn" @click="showAccountModal">
            {{ getAccountButtonText }}
          </view>
        </view>
      </view>
    </view>

    <!-- 记录列表标题和筛选 -->
    <view class="records-header">
      <view class="records-title">收支记录</view>

      <!-- 日期筛选 -->
      <view class="date-filter">
        <view class="filter-label">日期</view>
        <view class="date-range-selector">
          <view class="date-input" @click="showStartDatePicker">
            <text>{{ dateRange.startDate || '开始日期' }}</text>
            <u-icon name="calendar" size="16" color="#999"></u-icon>
          </view>
          <text class="date-separator">至</text>
          <view class="date-input" @click="showEndDatePicker">
            <text>{{ dateRange.endDate || '结束日期' }}</text>
            <u-icon name="calendar" size="16" color="#999"></u-icon>
          </view>
        </view>
      </view>

      <!-- 类型筛选 -->
      <view class="type-filter">
        <view class="filter-label">类型</view>
        <view class="filter-tabs">
          <view
            class="filter-tab"
            :class="{ active: form.IncExp === item.value }"
            v-for="(item, index) in accountIncExpAry"
            :key="index"
            @click="changeIncExpType(item.value)"
          >
            {{ item.name }}
          </view>
        </view>
      </view>
    </view>

    <!-- 收支记录列表 -->
    <view class="records-list">
      <view class="record-item" v-for="(item, index) in filteredFlowList" :key="index">
        <view class="record-content">
          <view class="record-info">
            <view class="record-desc">{{ item.description }}</view>
            <view class="record-time">{{ item.date }}</view>
          </view>
          <view class="record-amount" :class="{ income: item.type === 'income', expense: item.type === 'expense' }">
            <text>{{ item.type === 'income' ? '+' : '-' }}￥{{ item.amount }}</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredFlowList.length === 0">
        <text>暂无记录</text>
      </view>
    </view>

    <!-- 充值弹窗 -->
    <u-modal
      :show="rechargeModalShow"
      title="充值"
      :showCancelButton="true"
      @cancel="rechargeModalShow = false"
      @confirm="confirmRecharge"
    >
      <view class="recharge-modal-content">
        <!-- 充值金额输入 -->
        <view class="amount-section">
          <view class="amount-label">充值金额</view>
          <view class="amount-input-wrapper">
            <text class="currency-symbol">￥</text>
            <u-input
              v-model="rechargeAmount"
              placeholder="请输入充值金额"
              type="number"
              :border="false"
              :customStyle="{ fontSize: '32rpx', fontWeight: 'bold' }"
            ></u-input>
          </view>

          <!-- 快捷金额选择 -->
          <view class="quick-amounts">
            <view
              class="quick-amount-btn"
              :class="{ active: rechargeAmount === item }"
              v-for="item in quickAmounts"
              :key="item"
              @click="selectQuickAmount(item)"
            >
              {{ item }}
            </view>
          </view>
        </view>

        <!-- 支付方式选择 -->
        <view class="payment-section">
          <view class="payment-label">支付方式</view>
          <view class="payment-methods">
            <view class="payment-option" @click="selectPaymentType('miniapp')">
              <radio :checked="paymentType === 'miniapp'" color="#007AFF" />
              <text class="payment-text">小程序支付</text>
            </view>
            <view class="payment-option" @click="selectPaymentType('qrcode')">
              <radio :checked="paymentType === 'qrcode'" color="#007AFF" />
              <text class="payment-text">扫码支付</text>
            </view>
          </view>
        </view>

      </view>
    </u-modal>

    <!-- 二维码支付弹窗 -->
    <u-modal
      :show="qrcodeModalShow"
      title="扫码支付"
      :showCancelButton="true"
      @cancel="closeQrcodeModal"
      @confirm="handlePaymentComplete"
      confirmText="完成支付"
    >
      <view class="qrcode-modal-content" style="padding: 40rpx; text-align: center;">
        <view class="qrcode-title" style="font-size: 32rpx; color: #333; margin-bottom: 40rpx; font-weight: 500;">请使用微信扫描二维码完成支付</view>
        <view class="qrcode-container" style="display: flex; justify-content: center; align-items: center; margin-bottom: 30rpx; padding: 20rpx; background-color: #f8f8f8; border-radius: 12rpx;">
          <!-- 使用image组件显示二维码，如果失败则显示canvas -->
          <image
            v-if="qrcodeImageUrl && !useCanvasMode"
            :src="qrcodeImageUrl"
            class="qrcode-image"
            :style="{ width: qrcodeSize + 'px', height: qrcodeSize + 'px', border: '2rpx solid #e0e0e0', borderRadius: '8rpx', background: '#ffffff' }"
            mode="aspectFit"
            @error="handleQrcodeImageError"
          ></image>
          <canvas
            v-else
            canvas-id="qrcode-canvas"
            class="qrcode-canvas"
            :style="{ width: qrcodeSize + 'px', height: qrcodeSize + 'px', border: '2rpx solid #e0e0e0', borderRadius: '8rpx' }"
          ></canvas>
        </view>
        <view class="qrcode-amount" style="font-size: 36rpx; color: #ff6b35; font-weight: 600; margin-bottom: 30rpx;">支付金额：￥{{ rechargeAmount }}</view>
        <view class="qrcode-tips" style="margin-bottom: 40rpx;">
          <text style="display: block; font-size: 28rpx; color: #666; line-height: 1.6; margin-bottom: 8rpx;">• 请在30分钟内完成支付</text>
          <text style="display: block; font-size: 28rpx; color: #666; line-height: 1.6; margin-bottom: 8rpx;">• 支付完成后请点击"完成支付"按钮</text>
        </view>

        <!-- 复制链接按钮 -->
        <view class="copy-link-section">
          <button
            class="copy-link-btn"
            @click="copyPayUrl"
            style="width: 100%; height: 80rpx; background-color: #007AFF; color: #fff; border: none; border-radius: 12rpx; font-size: 32rpx; font-weight: 500;"
          >复制支付链接</button>
        </view>
      </view>
    </u-modal>

    <!-- 开户弹窗 -->
    <u-modal
      :show="accountModalShow"
      title="选择开户类型"
      :showCancelButton="true"
      @cancel="accountModalShow = false"
      :showConfirmButton="false"
    >
      <view class="account-modal-content">
        <view class="account-type-list">
          <!-- 企业开户选项暂时隐藏，保留代码以备后用 -->
          <!--
          <view class="account-type-item" @click="selectAccountType('enterprise')">
            <view class="type-icon">🏢</view>
            <view class="type-info">
              <view class="type-title">企业/个体户开户</view>
              <view class="type-desc">适用于有营业执照的企业和个体工商户</view>
            </view>
            <view class="type-arrow">
              <u-icon name="arrow-right" size="16" color="#999"></u-icon>
            </view>
          </view>
          -->

          <view class="account-type-item" @click="selectAccountType('micro')">
            <view class="type-icon">👤</view>
            <view class="type-info">
              <view class="type-title">个人开户</view>
              <view class="type-desc">适用于个人用户开户申请</view>
            </view>
            <view class="type-arrow">
              <u-icon name="arrow-right" size="16" color="#999"></u-icon>
            </view>
          </view>
        </view>

        <view class="account-tips">
          <view class="tips-title">开户说明</view>
          <view class="tips-content">
            <text>• 企业/个体户开户需要提供营业执照等资料</text>
            <text>• 个人开户只需要身份证即可，流程更简单</text>
            <text>• 开户成功后即可使用易宝支付服务</text>
          </view>
        </view>
      </view>
    </u-modal>

    

    <!-- 开始日期选择器 -->
    <u-datetime-picker
      :show="startDateShow"
      v-model="dateRange.startDate"
      mode="date"
      @confirm="confirmStartDate"
      @cancel="startDateShow = false"
    ></u-datetime-picker>

    <!-- 结束日期选择器 -->
    <u-datetime-picker
      :show="endDateShow"
      v-model="dateRange.endDate"
      mode="date"
      @confirm="confirmEndDate"
      @cancel="endDateShow = false"
    ></u-datetime-picker>
  </view>
</template>

<script>
import { getUserBalance, getBalanceFlow, createRecharge, createQrcodeRecharge, queryPaymentOrderStatus, createWithdrawalApplication, checkAccountStatus } from '@/api/payment.js'
import qrCodeGenerator from '@/utils/uqrcode.js'

export default {
  name: 'balance-manage',
  components: {
    // 这里可以导入需要的组件，如果项目中有的话
  },
  data() {
    return {
      // 余额信息
      balanceInfo: {
        available: '0.00', // 可用余额
        frozen: '0.00', // 冻结金额
      },

      // 开户状态信息
      accountStatus: {
        has_application: false,
        need_apply: true,
        status: null,
        status_name: '未开户',
        message: '请先完成开户申请'
      },
      
      // 筛选表单
      form: {
        date: '全部',
        IncExp: 'all',
      },
      
      // 收支类型选项
      accountIncExpAry: [
        { name: '全部', value: 'all' },
        { name: '收入', value: 'income' },
        { name: '支出', value: 'expense' },
      ],
      
      // 充值相关
      rechargeModalShow: false,
      rechargeAmount: '',
      quickAmounts: ['50', '100', '500', '1000'], // 快捷金额选项
      currentOrderNumber: '', // 当前充值订单号
      paymentType: 'miniapp', // 支付方式：miniapp-小程序支付，qrcode-扫码支付

      // 二维码相关
      qrcodeModalShow: false,
      qrcodeSize: 200, // 二维码尺寸
      payUrl: '', // 支付链接
      qrcodeImageUrl: '', // 二维码图片URL
      useCanvasMode: false, // 是否使用canvas模式

      // 开户相关
      accountModalShow: false,

      

      // 支付状态轮询相关
      pollingTimer: null, // 轮询定时器
      pollingCount: 0, // 轮询次数
      maxPollingCount: 40, // 最大轮询次数（40次，约2分钟）
      pollingInterval: 3000, // 轮询间隔（3秒）
      
      // 日期选择器
      startDateShow: false,
      endDateShow: false,
      dateRange: {
        startDate: '',
        endDate: ''
      },
      
      // 收支记录列表
      flowList: [
        
      ],
      dateRangeShow: false,
    };
  },

  computed: {
    // 是否显示开户按钮
    shouldShowAccountButton() {
     if (this.accountStatus.status === '2') {
      return this.accountStatus.need_apply === false;
      }
      return this.accountStatus.need_apply === true;
    },

    // 开户按钮文本
    getAccountButtonText() {
      // 只有在显示按钮时才会调用此方法
      if (this.accountStatus.status === '3') {
        return '重新开户';
      }
      return '开户';
    },

    // 是否显示状态提示
    shouldShowStatusTip() {
      // 未开户、开户失败、处理中时显示提示
      return this.accountStatus.status !== '2' && this.accountStatus.message;
    },

    // 状态提示样式类
    getStatusTipClass() {
      if (this.accountStatus.status === '3') {
        return 'account-status-error'; // 开户失败 - 红色
      } else if (this.accountStatus.status === '0' || this.accountStatus.status === '1') {
        return 'account-status-info'; // 待审核/审核中 - 蓝色
      } else {
        return 'account-status-tip'; // 需要开户 - 黄色
      }
    },

    // 状态提示文本
    getStatusTipText() {
      if (this.accountStatus.status === '2') {
        return ''; // 审核通过不显示提示
      } else if (this.accountStatus.status === '3') {
        return '开户申请被拒绝，请重新申请';
      } else if (this.accountStatus.status === '0' || this.accountStatus.status === '1') {
        return `开户状态：${this.accountStatus.status_name}`;
      } else {
        return this.accountStatus.message || '请先完成开户申请';
      }
    },

    // 根据筛选条件过滤记录
    filteredFlowList() {
      let filtered = this.flowList;

      // 按收支类型筛选
      if (this.form.IncExp !== 'all') {
        filtered = filtered.filter(item => item.type === this.form.IncExp);
      }

      // 按日期筛选
      if (this.dateRange.startDate && this.dateRange.endDate) {
        filtered = filtered.filter(item => {
          const itemDate = new Date(item.date);
          const startDate = new Date(this.dateRange.startDate);
          const endDate = new Date(this.dateRange.endDate);
          // 设置结束日期为当天的23:59:59
          endDate.setHours(23, 59, 59, 999);
          return itemDate >= startDate && itemDate <= endDate;
        });
      }

      return filtered;
    }
  },

  methods: {
    // 检查开户状态
    async checkAccountStatus() {
      try {
        // 获取当前用户手机号
        const userInfo = uni.getStorageSync('userInfo');
        if (!userInfo || !userInfo.mobile) {
          console.warn('未获取到用户手机号，跳过开户状态检查');
          return;
        }

        console.log('检查开户状态，手机号:', userInfo.mobile);

        // 显示加载提示
        uni.showLoading({
          title: '检查开户状态...',
          mask: false
        });

        // 调用检查开户状态API
        const result = await checkAccountStatus(userInfo.mobile);

        // 隐藏加载提示
        uni.hideLoading();

        console.log('开户状态检查结果:', result);

        // 更新开户状态信息
        this.accountStatus = {
          has_application: result.has_application || false,
          need_apply: result.need_apply || true,
          status: result.status,
          status_name: result.status_name || '未开户',
          message: result.message || '请先完成开户申请',
          application_uuid: result.application_uuid,
          yeepay_customer_code: result.yeepay_customer_code
        };

        // 根据状态显示不同提示
        if (this.accountStatus.status === '2') {
          console.log('用户开户已完成:', this.accountStatus.yeepay_customer_code);
        } else if (this.accountStatus.status === '3') {
          console.log('用户开户被拒绝，需要重新开户');
        } else if (this.accountStatus.need_apply) {
          console.log('用户需要开户:', this.accountStatus.message);
        } else {
          console.log('用户开户处理中:', this.accountStatus.message);
        }

      } catch (error) {
        console.error('检查开户状态失败:', error);
        // 隐藏加载提示
        uni.hideLoading();

        // 检查失败时设置默认状态
        this.accountStatus = {
          has_application: false,
          need_apply: true,
          status: null,
          status_name: '状态未知',
          message: '开户状态检查失败，请稍后重试'
        };

        // 显示错误提示
        uni.showToast({
          title: '开户状态检查失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 加载余额数据
    async loadBalanceData() {
      try {
        // 使用封装的API
        const result = await getUserBalance();

        this.balanceInfo = {
          available: result.available || '0.00',
          frozen: result.frozen || '0.00',
          total: result.total || '0.00'
        };
      } catch (error) {
        console.error('加载余额数据失败:', error);
        uni.showToast({
          title: '加载余额失败',
          icon: 'none'
        });
      }
    },

    // 加载流水数据
    async loadFlowData() {
      try {
        console.log('开始加载余额流水数据...');

        // 构建查询参数
        const params = {};

        if (this.form.IncExp && this.form.IncExp !== 'all') {
          params.flow_type = this.form.IncExp;
        }

        if (this.dateRange.startDate) {
          params.start_date = this.dateRange.startDate;
        }

        if (this.dateRange.endDate) {
          params.end_date = this.dateRange.endDate;
        }

        console.log('查询参数:', params);

        // 使用封装的API
        const result = await getBalanceFlow(params);

        console.log('流水API响应:', result);

 
          // 更新流水数据
          this.flowList = result || [];
          console.log('流水数据更新成功，共', this.flowList.length, '条记录');


      } catch (error) {
        console.error('加载流水数据失败:', error);
        this.flowList = [];
        uni.showToast({
          title: '加载流水失败',
          icon: 'none'
        });
      }
    },

    // 切换收支类型
    changeIncExpType(value) {
      this.form.IncExp = value;
      // 切换类型后重新加载数据
      this.loadFlowData();
    },
    
    // 显示充值弹窗
    showRechargeModal() {
      this.rechargeModalShow = true;
      this.rechargeAmount = '';
    },

    // 选择快捷金额
    selectQuickAmount(amount) {
      this.rechargeAmount = amount;
    },

    // 选择支付方式
    selectPaymentType(type) {
      this.paymentType = type;
    },

    // 显示二维码弹窗
    showQrcodeModal() {
      this.qrcodeModalShow = true;
      // 延迟生成二维码，确保DOM已渲染
      this.$nextTick(() => {
        this.generateQRCode();
      });
    },

    // 关闭二维码弹窗
    closeQrcodeModal() {
      this.qrcodeModalShow = false;
      this.payUrl = '';
      this.qrcodeImageUrl = '';
      this.useCanvasMode = false;
      this.stopPaymentStatusPolling();
    },

    // 处理完成支付按钮点击
    async handlePaymentComplete() {
      if (!this.currentOrderNumber) {
        this.closeQrcodeModal();
        return;
      }

      try {
        uni.showLoading({
          title: '查询支付结果...'
        });

        // 主动查询一次支付状态
        const result = await queryPaymentOrderStatus(this.currentOrderNumber);

        uni.hideLoading();

        if (result && result.status === 'success') {
          // 支付成功
          this.stopPaymentStatusPolling();
          this.qrcodeModalShow = false;

          uni.showToast({
            title: '充值成功！',
            icon: 'success',
            duration: 2000
          });

          // 刷新余额信息
          setTimeout(() => {
            this.loadBalanceInfo();
            this.loadBalanceFlow();
          }, 1000);

        } else if (result && result.status === 'failed') {
          // 支付失败
          uni.showModal({
            title: '支付失败',
            content: '支付未成功，请重新尝试或联系客服',
            showCancel: false
          });
        } else {
          // 支付状态未知或仍在处理中
          uni.showModal({
            title: '支付状态查询',
            content: '支付状态暂未确认，请稍后再试或联系客服确认',
            showCancel: false
          });
        }

      } catch (error) {
        uni.hideLoading();
        console.error('查询支付状态失败:', error);
        uni.showToast({
          title: '查询失败，请重试',
          icon: 'none'
        });
      }
    },

    // 生成二维码
    generateQRCode() {
      if (!this.payUrl) {
        console.error('支付链接为空，无法生成二维码');
        return;
      }

      try {
        // 首先尝试使用image模式显示二维码
        this.qrcodeImageUrl = qrCodeGenerator.getQRCodeUrl(this.payUrl, this.qrcodeSize);
        this.useCanvasMode = false;

        if (this.qrcodeImageUrl) {
          console.log('二维码URL生成成功:', this.qrcodeImageUrl);
        } else {
          // 如果URL生成失败，回退到canvas模式
          this.fallbackToCanvasMode();
        }

      } catch (error) {
        console.error('生成二维码失败:', error);
        this.fallbackToCanvasMode();
      }
    },

    // 回退到canvas模式
    fallbackToCanvasMode() {
      console.log('回退到canvas模式');
      this.useCanvasMode = true;
      this.qrcodeImageUrl = '';

      try {
        // 使用canvas模式生成二维码
        qrCodeGenerator.make({
          canvasId: 'qrcode-canvas',
          componentInstance: this,
          text: this.payUrl,
          size: this.qrcodeSize,
          margin: 10,
          backgroundColor: '#ffffff',
          foregroundColor: '#000000',
          success: () => {
            console.log('canvas二维码生成成功');
          },
          fail: (err) => {
            console.error('canvas二维码生成失败:', err);
            this.drawQRCodeFallback();
          }
        });
      } catch (error) {
        console.error('canvas模式失败:', error);
        this.drawQRCodeFallback();
      }
    },

    // 处理二维码图片加载错误
    handleQrcodeImageError() {
      console.log('二维码图片加载失败，切换到canvas模式');
      this.fallbackToCanvasMode();
    },

    // 二维码生成失败时的备用方案
    drawQRCodeFallback() {
      uni.showToast({
        title: '二维码生成失败，请手动复制链接',
        icon: 'none'
      });
    },

    // 复制支付链接
    copyPayUrl() {
      if (!this.payUrl) {
        uni.showToast({
          title: '支付链接为空',
          icon: 'none'
        });
        return;
      }

      // 复制到剪贴板
      uni.setClipboardData({
        data: this.payUrl,
        success: () => {
          uni.showToast({
            title: '支付链接已复制',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },

    // 显示开户弹窗
    showAccountModal() {
      this.accountModalShow = true;
    },

    // 选择开户类型
    selectAccountType(type) {
      this.accountModalShow = false;

      // 跳转到对应的开户页面
      // 企业开户暂时注释，保留代码以备后用
      /*
      if (type === 'enterprise') {
        // 企业/个体户开户
        uni.navigateTo({
          url: '/pages-other/yeepay-innet/index?type=enterprise',
          fail: (err) => {
            console.error('跳转企业开户页面失败:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } else
      */
      if (type === 'micro') {
        // 个人开户申请
        uni.navigateTo({
          url: '/pages-other/account-application/index',
          fail: (err) => {
            console.error('跳转个人开户页面失败:', err);
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    },

    // 导航到提现页面
    navigateToWithdrawal() {
      uni.navigateTo({
        url: '/pages-capital-flow/withdrawal?balance=' + this.balanceInfo.available
      });
    },

    
    // 确认充值
    confirmRecharge() {
      if (!this.rechargeAmount || parseFloat(this.rechargeAmount) <= 0) {
        uni.showToast({
          title: '请输入有效的充值金额',
          icon: 'none'
        });
        return;
      }
      
      // 这里调用充值API
      this.processRecharge();
    },
    
    // 处理充值
    async processRecharge() {
      try {
        // 验证充值金额
        const amount = parseFloat(this.rechargeAmount);
        if (!amount || amount <= 0) {
          uni.showToast({
            title: '请输入有效的充值金额',
            icon: 'none'
          });
          return;
        }

        if (amount < 0.01) {
          uni.showToast({
            title: '充值金额不能少于0.01元',
            icon: 'none'
          });
          return;
        }

        if (amount > 50000) {
          uni.showToast({
            title: '单次充值金额不能超过50000元',
            icon: 'none'
          });
          return;
        }

        uni.showLoading({
          title: '正在创建支付订单...',
          mask: true
        });

        // 根据支付方式调用不同的API
        if (this.paymentType === 'miniapp') {
          // 小程序支付
          await this.handleMiniappRecharge(amount);
        } else if (this.paymentType === 'qrcode') {
          // 扫码支付
          await this.handleQrcodeRecharge(amount);
        }

      } catch (error) {
        uni.hideLoading();

        let errorMsg = '充值失败，请重试';
        if (error.message) {
          errorMsg = error.message;
        } else if (typeof error === 'string') {
          errorMsg = error;
        }

        uni.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
        this.rechargeModalShow = false;
      }
    },

    // 小程序支付充值
    async handleMiniappRecharge(amount) {
      try {
        // 使用封装的API创建充值订单
        const result = await createRecharge({
          amount: amount
        });

        // 保存订单信息
        this.currentOrderNumber = result.order_number;

        // 后端返回支付参数，调用微信支付
        if (result.payToken) {
          uni.hideLoading();
          this.processWechatPayment(result.payToken, result.order_number);
        } else if (result.payUrl) {
          // H5支付，跳转到支付页面
          uni.hideLoading();
          // #ifdef H5
          window.location.href = result.payUrl;
          // #endif
          // #ifndef H5
          uni.showToast({
            title: '当前环境不支持H5支付',
            icon: 'none'
          });
          // #endif
        } else {
          uni.hideLoading();
          uni.showToast({
            title: '支付参数获取失败',
            icon: 'none'
          });
          this.rechargeModalShow = false;
        }
      } catch (error) {
        uni.hideLoading();
        throw error; // 重新抛出错误，让上层处理
      }
    },

    // 扫码支付充值
    async handleQrcodeRecharge(amount) {
      try {
        // 调用扫码支付API
        const result = await createQrcodeRecharge({
          amount: amount,
          paymentType: 'qrcode'
        });

        // 扫码支付API返回的数据结构
        uni.hideLoading();

        // 保存订单信息和支付链接
        this.currentOrderNumber = result.order_number;
        this.payUrl = result.payUrl;

        // 输出payUrl到控制台
        console.log('扫码支付URL:', result.payUrl);

        // 关闭充值弹窗，显示二维码弹窗
        this.rechargeModalShow = false;
        this.showQrcodeModal();

        // 开始轮询支付状态
        this.startPaymentStatusPolling(result.order_number);
      } catch (error) {
        uni.hideLoading();
        throw error; // 重新抛出错误，让上层处理
      }
    },

    // 处理微信小程序支付
    processWechatPayment(payToken, orderNumber) {
      // 解析payToken，调用微信支付
      try {
        // 解析易宝返回的payToken
        const paymentParams = JSON.parse(payToken);

        // 验证必要的支付参数
        const requiredParams = ['appId', 'timeStamp', 'nonceStr', 'package', 'signType', 'paySign'];
        const missingParams = requiredParams.filter(param => !paymentParams[param]);

        if (missingParams.length > 0) {
          throw new Error(`支付参数不完整，缺少: ${missingParams.join(', ')}`);
        }

        // 立即开始轮询支付状态（在调起支付前就开始）
        this.startPaymentStatusPolling(orderNumber);

        // 调用微信小程序支付
        uni.requestPayment({
          // 微信小程序支付参数
          timeStamp: paymentParams.timeStamp,
          nonceStr: paymentParams.nonceStr,
          package: paymentParams.package,
          signType: paymentParams.signType,
          paySign: paymentParams.paySign,
          success: () => {
            // 显示支付成功提示
            uni.showToast({
              title: '支付成功，正在确认...',
              icon: 'success',
              duration: 2000
            });
          },
          fail: (err) => {
            // 停止轮询
            this.stopPaymentStatusPolling();

            // 隐藏所有Toast
            uni.hideToast();

            // 根据错误类型显示不同提示
            let errorMsg = '支付失败';
            if (err.errMsg) {
              if (err.errMsg.includes('cancel')) {
                errorMsg = '用户取消支付';
              } else if (err.errMsg.includes('fail')) {
                errorMsg = '支付失败，请重试';
              } else {
                errorMsg = `支付失败: ${err.errMsg}`;
              }
            }

            uni.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 3000
            });
          },
          complete: () => {
            // 无论成功失败都关闭充值弹窗
            this.rechargeModalShow = false;
            // 清空充值金额
            this.rechargeAmount = '';
          }
        });

      } catch (error) {
        uni.showToast({
          title: error.message || '支付参数错误，请重试',
          icon: 'none',
          duration: 3000
        });

        // 关闭充值弹窗
        this.rechargeModalShow = false;
      }
    },
    
    // 刷新余额
    async refreshBalance() {
      try {
        // 调用获取余额的API
        const result = await getUserBalance();

        // 更新余额数据
        this.balanceInfo = {
          available: result.available || '0.00',
          frozen: result.frozen || '0.00',
          total: result.total || '0.00'
        };
      } catch (error) {
        console.error('刷新余额异常:', error);
      }
    },
    
    // 显示开始日期选择器
    showStartDatePicker() {
      this.startDateShow = true;
    },

    // 显示结束日期选择器
    showEndDatePicker() {
      this.endDateShow = true;
    },

    // 确认开始日期
    confirmStartDate(value) {
      this.dateRange.startDate = value;
      this.startDateShow = false;
    },

    // 确认结束日期
    confirmEndDate(value) {
      this.dateRange.endDate = value;
      this.endDateShow = false;
      // 日期变更后重新加载数据
      this.loadFlowData();
    },

    // 开始支付状态轮询
    startPaymentStatusPolling(orderNumber) {
      // 清除之前的轮询
      this.stopPaymentStatusPolling();

      // 重置轮询计数
      this.pollingCount = 0;

      // 显示轮询提示
      uni.showToast({
        title: '正在确认支付状态...',
        icon: 'loading',
        duration: 2000
      });

      // 立即查询一次
      this.checkPaymentStatus(orderNumber);

      // 设置定时轮询
      this.pollingTimer = setInterval(() => {
        this.checkPaymentStatus(orderNumber);
      }, this.pollingInterval);
    },

    // 停止支付状态轮询
    stopPaymentStatusPolling() {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }
      this.pollingCount = 0;
    },

    // 检查支付状态
    async checkPaymentStatus(orderNumber) {
      try {
        this.pollingCount++;

        // 查询支付状态
        const result = await queryPaymentOrderStatus(orderNumber);

        if (!result) {
          throw new Error('查询API返回空结果');
        }

        const status = result.status;

        if (status === 'success') {
          // 支付成功
          this.stopPaymentStatusPolling();

          // 关闭所有弹窗
          this.rechargeModalShow = false;
          this.qrcodeModalShow = false;

          // 隐藏所有Toast
          uni.hideToast();

          // 显示支付成功提示
          uni.showToast({
            title: '充值成功！',
            icon: 'success',
            duration: 2000
          });

          // 刷新余额和流水数据
          setTimeout(() => {
            this.refreshBalance();
            this.loadFlowData();
          }, 1000);

        } else if (status === 'failed') {
          // 支付失败
          this.stopPaymentStatusPolling();

          // 关闭充值弹窗
          this.rechargeModalShow = false;

          // 隐藏所有Toast
          uni.hideToast();

          uni.showToast({
            title: '支付失败',
            icon: 'none',
            duration: 2000
          });

        } else if (status === 'pending' || status === 'unknown') {
          // 支付处理中或未知状态，继续轮询

          // 检查是否超过最大轮询次数
          if (this.pollingCount >= this.maxPollingCount) {
            this.stopPaymentStatusPolling();

            // 关闭充值弹窗
            this.rechargeModalShow = false;

            // 隐藏所有Toast
            uni.hideToast();

            uni.showToast({
              title: '支付状态确认超时，请手动刷新查看余额',
              icon: 'none',
              duration: 3000
            });
          } else {
            // 显示轮询进度
            if (this.pollingCount % 5 === 0) {
              uni.showToast({
                title: `正在确认支付状态... (${this.pollingCount}/${this.maxPollingCount})`,
                icon: 'loading',
                duration: 1500
              });
            }
          }
        } else {
          // 其他未知状态
          if (this.pollingCount >= this.maxPollingCount) {
            this.stopPaymentStatusPolling();

            uni.showToast({
              title: '支付状态未知，请手动刷新查看余额',
              icon: 'none',
              duration: 3000
            });
          }
        }

      } catch (error) {
        // 如果查询失败次数过多，停止轮询
        if (this.pollingCount >= this.maxPollingCount) {
          this.stopPaymentStatusPolling();

          uni.showToast({
            title: '支付状态查询失败，请手动刷新查看余额',
            icon: 'none',
            duration: 3000
          });
        }
      }
    },
  },

  // 页面生命周期
  onLoad() {
    // 页面加载时获取余额和流水数据
    this.loadBalanceData();
    this.loadFlowData();
  },

  onShow() {
    // 每次显示页面时检查开户状态
    this.checkAccountStatus();
  },

  // 页面卸载时清理轮询
  onUnload() {
    this.stopPaymentStatusPolling();
  },

  // 页面隐藏时清理轮询
  onHide() {
    this.stopPaymentStatusPolling();
  },
};
</script>

<style lang="scss" scoped>
.balance-manage {
  background: #f5f6fa;
  min-height: 100vh;
}

// 余额卡片
.balance-card {
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  margin: 10rpx 20rpx 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .balance-info {
    flex: 1;

    .balance-title {
      font-size: 28rpx;
      opacity: 0.9;
      margin-bottom: 12rpx;
    }

    .balance-amount {
      font-size: 48rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
      letter-spacing: 1rpx;
    }

    .balance-details {
      display: flex;
      gap: 24rpx;

      .detail-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .detail-label {
          font-size: 22rpx;
          opacity: 0.8;
          margin-bottom: 4rpx;
        }

        .detail-value {
          font-size: 24rpx;
          font-weight: 500;
        }
      }
    }
  }

  .balance-actions {
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .account-status-tip {
      background: rgba(255, 243, 205, 0.9);
      border: 2rpx solid rgba(255, 234, 167, 0.8);
      border-radius: 16rpx;
      padding: 20rpx;
      margin-bottom: 16rpx;

      .status-text {
        font-size: 26rpx;
        color: #856404;
        line-height: 1.4;
      }
    }

    .account-status-info {
      background: rgba(209, 236, 241, 0.9);
      border: 2rpx solid rgba(190, 229, 235, 0.8);
      border-radius: 16rpx;
      padding: 20rpx;
      margin-bottom: 16rpx;

      .status-text {
        font-size: 26rpx;
        color: #0c5460;
        line-height: 1.4;
      }
    }

    .account-status-error {
      background: rgba(248, 215, 218, 0.9);
      border: 2rpx solid rgba(220, 53, 69, 0.3);
      border-radius: 16rpx;
      padding: 20rpx;
      margin-bottom: 16rpx;

      .status-text {
        font-size: 26rpx;
        color: #721c24;
        line-height: 1.4;
      }
    }

    .action-buttons {
      display: flex;
      gap: 16rpx;
    }

    .action-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 2rpx solid rgba(255, 255, 255, 0.3);
      color: #fff;
      padding: 16rpx 24rpx;
      border-radius: 50rpx;
      font-size: 26rpx;
      font-weight: 500;
      text-align: center;
      backdrop-filter: blur(10rpx);
      flex: 1;

      &.withdrawal-btn {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.25);
      }

      &.account-btn {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.35);
      }
    }
  }
}

// 记录列表头部
.records-header {
  background: #fff;
  margin: 0 20rpx 16rpx;
  border-radius: 16rpx;
  padding: 24rpx;

  .records-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .date-filter {
    margin-bottom: 20rpx;

    .filter-label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 12rpx;
    }

    .date-range-selector {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .date-input {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16rpx 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        border: 1rpx solid #e9ecef;

        text {
          font-size: 26rpx;
          color: #333;
        }
      }

      .date-separator {
        font-size: 24rpx;
        color: #666;
        padding: 0 8rpx;
      }
    }
  }

  .type-filter {
    .filter-label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 12rpx;
    }

    .filter-tabs {
      display: flex;
      gap: 12rpx;

      .filter-tab {
        flex: 1;
        padding: 16rpx 20rpx;
        text-align: center;
        background: #f8f9fa;
        border-radius: 12rpx;
        font-size: 26rpx;
        color: #666;
        border: 1rpx solid #e9ecef;

        &.active {
          background: #fdd118;
          color: #fff;
          border-color: #fdd118;
        }
      }
    }
  }
}

// 记录列表
.records-list {
  background: #fff;
  margin: 0 20rpx;
  border-radius: 16rpx;
  overflow: hidden;

  .record-item {
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

// 精美提现弹窗样式
.simple-withdrawal-modal {
  background: linear-gradient(135deg, #fff 0%, #fafbfc 100%);
  border-radius: 32rpx;
  overflow: hidden;
  max-height: 85vh;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  position: relative;
}

// 精美头部
.simple-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 32rpx 32rpx;
  background: linear-gradient(135deg, #fdd118 0%, #ff9500 100%);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  }

  .header-title {
    font-size: 36rpx;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  }

  .close-icon {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10rpx);
    transition: all 0.3s ease;
    
    &:active {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(0.95);
    }
  }
}

// 精美内容区域
.simple-content {
  padding: 32rpx;
  max-height: 65vh;
  overflow-y: auto;
  background: linear-gradient(180deg, rgba(253, 209, 24, 0.02) 0%, transparent 20%);
}

// 精美表单区块
.form-section {
  margin-bottom: 32rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(253, 209, 24, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
    transform: translateY(-2rpx);
  }

  .section-label {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    gap: 8rpx;
    position: relative;
    
    &::before {
      content: '';
      width: 6rpx;
      height: 24rpx;
      background: linear-gradient(135deg, #fdd118 0%, #ff9500 100%);
      border-radius: 3rpx;
      margin-right: 8rpx;
    }

    .optional {
      font-size: 24rpx;
      color: #999;
      font-weight: 400;
      background: #f8f9fa;
      padding: 4rpx 12rpx;
      border-radius: 12rpx;
      margin-left: auto;
    }
  }
}

// 精美提现类型选项
.type-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;

  .type-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx;
    border: 2rpx solid #e8ecf0;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #fff 0%, #fafbfc 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(253, 209, 24, 0.05) 0%, rgba(255, 149, 0, 0.05) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &.selected {
      border-color: #fdd118;
      background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
      box-shadow: 0 8rpx 25rpx rgba(253, 209, 24, 0.2);
      transform: translateY(-2rpx);
      
      &::before {
        opacity: 1;
      }
    }
    
    &:active {
      transform: translateY(0);
    }

    .option-content {
      flex: 1;
      position: relative;
      z-index: 1;

      .option-title {
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 6rpx;
      }

      .option-desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1.4;
      }
    }

    .option-check {
      width: 32rpx;
      height: 32rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #fdd118 0%, #ff9500 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.3);
      position: relative;
      z-index: 1;
    }
  }
}

// 精美金额输入
.amount-input {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e8ecf0;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #fff 0%, #fafbfc 100%);
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  position: relative;
  
  &:focus-within {
    border-color: #fdd118;
    box-shadow: 0 0 0 4rpx rgba(253, 209, 24, 0.1);
    background: #fff;
  }

  .currency {
    font-size: 40rpx;
    font-weight: 700;
    color: #333;
    margin-right: 12rpx;
    background: linear-gradient(135deg, #fdd118 0%, #ff9500 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.balance-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;

  .balance-text {
    font-size: 26rpx;
    color: #666;
    font-weight: 500;
  }

  .all-btn {
    font-size: 26rpx;
    color: #fff;
    padding: 12rpx 24rpx;
    background: linear-gradient(135deg, #fdd118 0%, #ff9500 100%);
    border-radius: 24rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(253, 209, 24, 0.3);
    transition: all 0.3s ease;
    
    &:active {
      transform: translateY(1rpx);
      box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.4);
    }
  }
}

// 输入组
.input-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

// 精美上传区域
.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  border: 3rpx dashed #e8ecf0;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
  gap: 16rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(253, 209, 24, 0.05) 0%, rgba(255, 149, 0, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:active {
    border-color: #fdd118;
    background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
    
    &::before {
      opacity: 1;
    }
  }

  .upload-text {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
    position: relative;
    z-index: 1;
  }
}

// 已上传图片
.uploaded-image {
  display: flex;
  align-items: center;
  gap: 16rpx;

  .invoice-image {
    width: 80rpx;
    height: 80rpx;
    border-radius: 8rpx;
  }

  .image-actions {
    display: flex;
    gap: 16rpx;

    text {
      font-size: 24rpx;
      color: #007AFF;

      &.delete {
        color: #ff3b30;
      }
    }
  }
}

// 精美费用说明
.fee-notice {
  background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
  border: 2rpx solid #fdd118;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-top: 20rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(253, 209, 24, 0.15);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6rpx;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff9500 100%);
  }

  .notice-title {
    display: flex;
    align-items: center;
    gap: 12rpx;
    margin-bottom: 16rpx;
    position: relative;
    z-index: 1;

    text {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .notice-content {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
    position: relative;
    z-index: 1;

    text {
      font-size: 26rpx;
      color: #666;
      line-height: 1.5;
      position: relative;
      padding-left: 20rpx;
      
      &::before {
        content: '•';
        position: absolute;
        left: 0;
        color: #fdd118;
        font-weight: bold;
      }
    }
  }
}

// 精美底部按钮
.simple-footer {
  display: flex;
  gap: 20rpx;
  padding: 32rpx;
  background: linear-gradient(180deg, #fff 0%, #fafbfc 100%);
  border-top: 1rpx solid rgba(253, 209, 24, 0.1);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 32rpx;
    right: 32rpx;
    height: 1rpx;
    background: linear-gradient(90deg, transparent 0%, rgba(253, 209, 24, 0.3) 50%, transparent 100%);
  }

  .footer-btn {
    flex: 1;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 24rpx;
    font-size: 30rpx;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &.cancel-btn {
      border: 2rpx solid #e8ecf0;
      background: linear-gradient(135deg, #fff 0%, #fafbfc 100%);
      color: #666;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:active {
        transform: translateY(2rpx);
        
        &::before {
          opacity: 1;
        }
      }
    }

    &.confirm-btn {
      background: linear-gradient(135deg, #fdd118 0%, #ff9500 100%);
      color: #fff;
      box-shadow: 0 8rpx 25rpx rgba(253, 209, 24, 0.4);
      border: none;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 15rpx rgba(253, 209, 24, 0.5);
        
        &::before {
          opacity: 1;
        }
      }
    }
  }
}

    .record-content {
      padding: 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .record-info {
        flex: 1;

        .record-desc {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
          font-weight: 500;
        }

        .record-time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .record-amount {
        font-size: 32rpx;
        font-weight: bold;

        &.income {
          color: #09be89;
        }

        &.expense {
          color: #ff6b35;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 80rpx 20rpx;
    color: #999;
    font-size: 28rpx;
  }
}

// 开户弹窗样式
.account-modal-content {
  padding: 20rpx 0;

  .account-type-list {
    margin-bottom: 32rpx;

    .account-type-item {
      display: flex;
      align-items: center;
      padding: 24rpx 20rpx;
      border: 2rpx solid #e9ecef;
      border-radius: 16rpx;
      margin-bottom: 16rpx;
      background: #fff;
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background: #f8f9fa;
        border-color: #fdd118;
      }

      .type-icon {
        font-size: 48rpx;
        margin-right: 20rpx;
      }

      .type-info {
        flex: 1;

        .type-title {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          margin-bottom: 8rpx;
        }

        .type-desc {
          font-size: 24rpx;
          color: #666;
          line-height: 1.4;
        }
      }

      .type-arrow {
        margin-left: 16rpx;
      }
    }
  }

  .account-tips {
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 20rpx;

    .tips-title {
      font-size: 26rpx;
      color: #333;
      font-weight: 500;
      margin-bottom: 12rpx;
    }

    .tips-content {
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      text {
        font-size: 24rpx;
        color: #666;
        line-height: 1.5;
      }
    }
  }
}

// 充值弹窗样式
.recharge-modal-content {
  padding: 20rpx 0;

  .amount-section {
    margin-bottom: 40rpx;

    .amount-label {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;
      font-weight: 500;
    }

    .amount-input-wrapper {
      display: flex;
      align-items: center;
      padding: 20rpx;
      border: 2rpx solid #e9ecef;
      border-radius: 12rpx;
      margin-bottom: 20rpx;

      .currency-symbol {
        font-size: 32rpx;
        color: #333;
        margin-right: 8rpx;
        font-weight: bold;
      }
    }

    .quick-amounts {
      display: flex;
      gap: 12rpx;
      flex-wrap: wrap;

      .quick-amount-btn {
        flex: 1;
        min-width: 120rpx;
        padding: 16rpx 20rpx;
        text-align: center;
        background: #f8f9fa;
        border: 2rpx solid #e9ecef;
        border-radius: 12rpx;
        font-size: 26rpx;
        color: #666;

        &.active {
          background: #fdd118;
          color: #fff;
          border-color: #fdd118;
        }
      }
    }
  }

  .payment-section {
    margin-bottom: 40rpx;

    .payment-label {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;
      font-weight: 500;
    }

    .payment-methods {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
    }

    .payment-option {
      display: flex;
      align-items: center;
      gap: 20rpx;
      padding: 10rpx 0;
    }

    .payment-text {
      font-size: 32rpx;
      color: #333;
    }
  }
}

// 二维码弹窗样式
.qrcode-modal-content {
  padding: 40rpx !important;
  text-align: center !important;

  .qrcode-title {
    font-size: 32rpx !important;
    color: #333 !important;
    margin-bottom: 40rpx !important;
    font-weight: 500 !important;
  }

  .qrcode-container {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-bottom: 30rpx !important;
    padding: 20rpx !important;
    background-color: #f8f8f8 !important;
    border-radius: 12rpx !important;

    .qrcode-canvas {
      border: 2rpx solid #e0e0e0 !important;
      border-radius: 8rpx !important;
    }

    .qrcode-image {
      border: 2rpx solid #e0e0e0 !important;
      border-radius: 8rpx !important;
      background: #ffffff !important;
    }
  }

  .qrcode-amount {
    font-size: 36rpx !important;
    color: #ff6b35 !important;
    font-weight: 600 !important;
    margin-bottom: 30rpx !important;
  }

  .qrcode-tips {
    margin-bottom: 40rpx !important;

    text {
      display: block !important;
      font-size: 28rpx !important;
      color: #666 !important;
      line-height: 1.6 !important;
      margin-bottom: 8rpx !important;
    }
  }

  .copy-link-section {
    .copy-link-btn {
      width: 100% !important;
      height: 80rpx !important;
      background-color: #007AFF !important;
      color: #fff !important;
      border: none !important;
      border-radius: 12rpx !important;
      font-size: 32rpx !important;
      font-weight: 500 !important;

      &:active {
        opacity: 0.8 !important;
      }
    }
  }
}
</style>
