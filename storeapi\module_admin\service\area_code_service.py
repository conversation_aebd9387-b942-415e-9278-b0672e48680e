from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from typing import Dict, Any, Optional

from exceptions.exception import QueryException
from utils.log_util import logger


class AreaCodeService:
    """省市区编码服务层"""

    @staticmethod
    async def get_area_info_by_id_card(db: AsyncSession, id_card: str) -> Dict[str, Any]:
        """
        根据身份证号获取省市区信息
        
        :param db: 数据库会话
        :param id_card: 身份证号
        :return: 省市区信息
        """
        if not id_card or len(id_card) < 6:
            return {}
        
        try:
            # 提取身份证前6位作为区编码
            district_code = id_card[:6]
            
            # 从数据库查询对应的省市区信息
            query = text("""
                SELECT province_code, province_name, city_code, city_name, district_code, district_name 
                FROM area_code 
                WHERE district_code = :district_code
            """)
            
            result = await db.execute(query, {"district_code": district_code})
            row = result.fetchone()
            
            if row:
                return {
                    "province_code": row[0],
                    "province_name": row[1],
                    "city_code": row[2],
                    "city_name": row[3],
                    "district_code": row[4],
                    "district_name": row[5]
                }
            else:
                # 如果精确匹配失败，尝试模糊匹配（前4位市编码）
                city_code = district_code[:4] + "00"
                query = text("""
                    SELECT province_code, province_name, city_code, city_name, district_code, district_name 
                    FROM area_code 
                    WHERE city_code = :city_code 
                    LIMIT 1
                """)
                
                result = await db.execute(query, {"city_code": city_code})
                row = result.fetchone()
                
                if row:
                    return {
                        "province_code": row[0],
                        "province_name": row[1],
                        "city_code": row[2],
                        "city_name": row[3],
                        "district_code": district_code,
                        "district_name": "未知区县"
                    }
                else:
                    # 最后尝试省编码匹配（前2位）
                    province_code = district_code[:2] + "0000"
                    query = text("""
                        SELECT province_code, province_name, city_code, city_name, district_code, district_name 
                        FROM area_code 
                        WHERE province_code = :province_code 
                        LIMIT 1
                    """)
                    
                    result = await db.execute(query, {"province_code": province_code})
                    row = result.fetchone()
                    
                    if row:
                        return {
                            "province_code": row[0],
                            "province_name": row[1],
                            "city_code": city_code,
                            "city_name": "未知城市",
                            "district_code": district_code,
                            "district_name": "未知区县"
                        }
            
            return {}
            
        except Exception as e:
            logger.error(f"根据身份证号获取省市区信息失败: {str(e)}")
            return {}

    @staticmethod
    async def get_area_info_by_district_code(db: AsyncSession, district_code: str) -> Optional[Dict[str, Any]]:
        """
        根据区编码获取省市区信息
        
        :param db: 数据库会话
        :param district_code: 区编码
        :return: 省市区信息
        """
        if not district_code:
            return None
        
        try:
            query = text("""
                SELECT province_code, province_name, city_code, city_name, district_code, district_name 
                FROM area_code 
                WHERE district_code = :district_code
            """)
            
            result = await db.execute(query, {"district_code": district_code})
            row = result.fetchone()
            
            if row:
                return {
                    "province_code": row[0],
                    "province_name": row[1],
                    "city_code": row[2],
                    "city_name": row[3],
                    "district_code": row[4],
                    "district_name": row[5]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"根据区编码获取省市区信息失败: {str(e)}")
            return None

    @staticmethod
    async def get_provinces(db: AsyncSession) -> list:
        """
        获取所有省份列表
        
        :param db: 数据库会话
        :return: 省份列表
        """
        try:
            query = text("""
                SELECT DISTINCT province_code, province_name 
                FROM area_code 
                ORDER BY province_code
            """)
            
            result = await db.execute(query)
            rows = result.fetchall()
            
            provinces = []
            for row in rows:
                provinces.append({
                    "province_code": row[0],
                    "province_name": row[1]
                })
            
            return provinces
            
        except Exception as e:
            logger.error(f"获取省份列表失败: {str(e)}")
            return []

    @staticmethod
    async def get_cities_by_province(db: AsyncSession, province_code: str) -> list:
        """
        根据省编码获取城市列表
        
        :param db: 数据库会话
        :param province_code: 省编码
        :return: 城市列表
        """
        try:
            query = text("""
                SELECT DISTINCT city_code, city_name 
                FROM area_code 
                WHERE province_code = :province_code 
                ORDER BY city_code
            """)
            
            result = await db.execute(query, {"province_code": province_code})
            rows = result.fetchall()
            
            cities = []
            for row in rows:
                cities.append({
                    "city_code": row[0],
                    "city_name": row[1]
                })
            
            return cities
            
        except Exception as e:
            logger.error(f"根据省编码获取城市列表失败: {str(e)}")
            return []

    @staticmethod
    async def get_districts_by_city(db: AsyncSession, city_code: str) -> list:
        """
        根据市编码获取区县列表
        
        :param db: 数据库会话
        :param city_code: 市编码
        :return: 区县列表
        """
        try:
            query = text("""
                SELECT district_code, district_name 
                FROM area_code 
                WHERE city_code = :city_code 
                ORDER BY district_code
            """)
            
            result = await db.execute(query, {"city_code": city_code})
            rows = result.fetchall()
            
            districts = []
            for row in rows:
                districts.append({
                    "district_code": row[0],
                    "district_name": row[1]
                })
            
            return districts
            
        except Exception as e:
            logger.error(f"根据市编码获取区县列表失败: {str(e)}")
            return []

    @staticmethod
    def parse_address_from_id_card(id_card: str) -> str:
        """
        从身份证号解析地址信息（静态方法，不需要数据库）
        
        :param id_card: 身份证号
        :return: 地址字符串
        """
        if not id_card or len(id_card) < 6:
            return ""
        
        # 提取前6位区编码
        district_code = id_card[:6]
        
        # 这里可以根据需要添加更多的静态地址解析逻辑
        # 目前返回区编码，实际使用时应该结合数据库查询
        return f"区编码: {district_code}"
